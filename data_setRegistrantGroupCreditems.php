<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$RegistrantGroupId = $_POST['RegistrantGroupId'];
	$UserId = $_POST['UserId'];
	$CredItems = $_POST['CredItems'];
	$CredItems=json_decode($CredItems,true);

	$result = $rcr_transaction->setRegistrantGroupCredItems($RegistrantGroupId,	
															$CredItems,	
															$UserId); 

	$rcr_transaction->disconnectDB (); 


		
		
	echo $result;

?>
