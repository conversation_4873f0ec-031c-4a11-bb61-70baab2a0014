<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
 

	$query = "SELECT  a.Id AS Id, 
                                a.Id as SessionNotesId, 
                                OriginalFileName as SessionNotesDesc, 
                                StoredName,
          						StoredNameExt,	
                                CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) as UserName,
                                a.TransDate

                FROM    SchRegistrantSessionNotes a, 
                        Users e
                WHERE   a.RegistrantId = '{$RegistrantId}'
                    AND a.UserId = e.UserId";      

 


	$ret = getData ($conn, $query);
	
	setDisConn($conn);

	echo $ret;

	 


	// require "ewDataHandler.php"; 
	  
	// $rcr_transaction = new dataHandler(); 

	// $RegistrantId = $_GET['RegistrantId'];  

	 
	// $result = $rcr_transaction->getSchRegistrantSessionNotes(	$RegistrantId ); 
																  

	// $rcr_transaction->disconnectDB (); 

	// echo  "{ success: true,  data: ".json_encode($result)."}";

?>
