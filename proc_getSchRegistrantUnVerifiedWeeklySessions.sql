 DELIMITER $$

    DROP PROCEDURE IF EXISTS proc_getSchRegistrantUnVerifiedWeeklySessions$$

    CREATE PROCEDURE proc_getSchRegistrantUnVerifiedWeeklySessions (IN  p_registrant_id INT, 
                                                                    p_payroll_week DATE,
                                                                    p_service_date DATE 
                                                                )     

                                        

    BEGIN

      
      /*======================*/
      create temporary table tmp

 
      SELECT  	a.Id as MandateId,
						a.ServiceTypeId, 
					   	c.ServiceTypeDesc,	
				       	a.SessionLength,
				       	a.SessionGrpSize,
				       	a.SessionFrequency,
				       	a.SessionFrequencyType,
				       	a.StudentId,
				       	a.Comments,
				       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName

				FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
				WHERE a.RegistrantId = p_registrant_id 
				AND a.StatusId = '1'
				and b.StatusId = '1'
				AND a.StudentId = b.Id
				AND p_service_date BETWEEN a.StartDate AND a.EndDate
				AND a.ServiceTypeId = c.Id
				AND NOT EXISTS (SELECT 1 FROM WeeklyServices e
							WHERE e.MandateId = a.Id 
				       	  	AND e.ScheduleStatusId > 5
				       	  	AND e.ServiceDate = p_service_date 
				       	  	AND a.ServiceTypeId = e.ServiceTypeId
					) ;

	  
	  /* Check "Weekly" Freq. Type*/  			
	  /*==========================*/
	  DELETE FROM tmp a
	     where  a.SessionFrequencyType = 'Weekly'
	     and (SELECT COUNT(*) FROM WeeklyServices d
				       	  	WHERE d.MandateId = a.MandateId 
				       	  	AND d.ScheduleStatusId > 5
				       	  	AND d.PayrollWeek = p_payroll_week    
				       	) >= a.SessionFrequency 	
	  ;			       			
    
	  /* Check "Monthly" Freq. Type*/  			
	  /*==========================*/
	  DELETE FROM tmp a
	     where  a.SessionFrequencyType = 'Monthly'
	     and (SELECT COUNT(*) FROM WeeklyServices d
				       	  	WHERE d.MandateId = a.MandateId 
				       	  	AND d.ScheduleStatusId > 5
				       	  	AND MONTH(d.ServiceDate) = MONTH(p_service_date)   
				       	  	AND YEAR(d.ServiceDate) = YEAR(p_service_date)   

				       	) >= a.SessionFrequency 	
	  ;			       			
     

      SELECT * FROM tmp
      order by StudentName  
      ; 


     
       
      
    END $$

    DELIMITER ;   
     