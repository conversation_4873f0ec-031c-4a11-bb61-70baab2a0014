<?php

	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php');


class PDF extends FPDF
{

	function PDF($orientation='L',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


	//Page header
	function Header()
	{
		//Logo
		$this->Image('monte_logo.png',140,8,40);
		//Arial bold 15
		$this->SetFont('Arial','B',11);
		//Move to the right
		//$this->Cell(80);
		$this->Ln(10);
		//Title
		$this->Cell(20,4,'Vendor:',1,0,'L');
		$this->Cell(60,4,'Bells Nursing Registry',1,0,'C');
		$this->Cell(10);
		$this->Cell(20,4,'Facility:',1,0,'L');
		$this->Cell(150,4,'MONTEFIORE NORTH CONSTANT OBSERVATION SIGN-IN-SHEET',1,1,'C');
		$this->Ln(2);
		$this->Cell(20,4,'Shift:',1,0,'L');
		$this->Cell(60,4,'5/10/2014 7:30AM-7:30PM',1,0,'C');
		
		$this->Ln(10);
		//Set Table Header
		$this->SetFont('Times','',10);
		$this->Cell(40,4,'Patient Name',1,0,'C');
		$this->Cell(15,4,'Gender',1,0,'C');
		$this->Cell(25,4,'Unit',1,0,'C');
		$this->Cell(30,4,'First Name PSM',1,0,'C');
		$this->Cell(30,4,'Last Name PSM',1,0,'C');
		$this->Cell(30,4,'Signature OF PSM',1,0,'C');
		$this->Cell(20,4,'Time In',1,0,'C');
		$this->Cell(20,4,'Time Out',1,0,'C');
		$this->Cell(22,4,'Total Hours',1,0,'C');
		$this->Cell(40,4,'Notes',1,1,'C');
		$this->Ln(1);
		

		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		$this->SetFont('Arial','I',8);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}

	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	$pdf->SetFont('Times','',12);
	//for($i=1;$i<=40;$i++)
		//$pdf->Cell(0,10,'Printing line number '.$i,0,1);
	$pdf->Output();
?>
