  
<?php
    
    ob_start();

    require_once('fpdf/fpdf.php');
    require_once('fpdi/fpdi.php');
    require_once("db_login.php");
    require_once('DB.php');



    $RegistrantId = $_GET['RegistrantId'];

    $FromDate = $_GET['FromDate'];

    $ToDate = $_GET['ToDate'];


    $Data = $_GET['Data'];
    $Data=json_decode($Data,true);


    $connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

    
    if (DB::isError($connection)){
        die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 



  // initiate FPDI
    $pdf = new FPDI();

    $pageNo = 0;
 
   // get the page count

    $pageCount = $pdf->setSourceFile('RSA7A.pdf');
    // iterate through all pages

 
      $pageNo = $pageNo + 1;

    // import a page
    $templateId = $pdf->importPage($pageNo);
    // get the size of the imported page
    $size = $pdf->getTemplateSize($templateId);
 

  //echo 'Data Size: '.sizeof($Data).'</br>';
    

  //foreach ($Data as $StudentId) {




  $x = 0;
  foreach ($Data as $StudentData) {

      
    //var_dump($StudentData);

    $StudentId = $StudentData['StudentId'];
    $MandateId = $StudentData['MandateId'];
     
    //echo'StudentId: '.$StudentId.' Mandate ID: '.$MandateId.'</br>';

    ++$x;

    //echo 'Student: '.$StudentId.'</br>';


    //==================================
    // Get Company/Client Name
    //==================================
    
    $query = "SELECT  a.CompanyName as Agency_Name,
                      CONCAT( a.StreetAddress1, ' ', a.City, ' ', a.State, ' ', a.ZipCode) as Agency_Address,
                      a.TIN as Agency_TaxId,
                      a.PhoneNumber as Agency_Phone,
                      a.Email as Agency_Email,
                      CONCAT( b.FirstName, ' ', b.LastName) as Student_Name,
                      b.ExtId as Student_NYC_Id,
                      DATE_FORMAT( b.DateOfBirth, '%m/%d/%Y' ) AS Student_DOB,
                      d.DistrictName as Student_District,
                      g.ServiceTypeDesc as Student_Service_Type,
                      f.SessionFrequency,
                      f.SessionLength,
                      f.SessionGrpSize,
                      f.Language,
                      CONCAT( e.FirstName, ' ', e.LastName) as Provider_Name,
                      e.ExtId as Provider_ID,
                      CONCAT( e.StreetAddress1, ' ', e.City, ' ', e.State, ' ', e.ZipCode) as Provider_Address,
                      e.MobilePhone as Provider_Phone,
                      e.Email as Provider_Email



                      FROM Company a,
                           SchStudents b,
                           SchSchools c,
                           SchDistricts d,
                           Registrants e,
                           SchStudentMandates f,
                           SchServiceTypes g
                      WHERE b.Id = '{$StudentId}'
                      AND   e.Id = '{$RegistrantId}'
                      
                  /*    AND   f.StudentId = '{$StudentId}'
                      AND   f.RegistrantId = '{$RegistrantId}'
                  */    
                   
                      AND   f.Id = '{$MandateId}'  
                      AND   f.SchoolId = c.Id
                      AND   c.DistrictId = d.Id
                      AND   f.ServiceTypeId = g.Id ";
                
    
    $result = $connection->query ($query);
    while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
        $GLOBALS['Agency_Name'] = $row['Agency_Name'];
        $GLOBALS['Agency_Address'] = $row['Agency_Address']; 
        $GLOBALS['Agency_TaxId'] = $row['Agency_TaxId'];
        $GLOBALS['Agency_Phone'] = $row['Agency_Phone'];
        $GLOBALS['Agency_Email'] = $row['Agency_Email'];
        $GLOBALS['Student_Name'] = $row['Student_Name'];        
        $GLOBALS['Student_NYC_Id'] = $row['Student_NYC_Id'];
        $GLOBALS['Student_DOB'] = $row['Student_DOB'];
        $GLOBALS['Student_District'] = $row['Student_District'];
        $GLOBALS['Student_Service_Type'] = $row['Student_Service_Type'];
        $GLOBALS['SessionFrequency'] = $row['SessionFrequency'];
        $GLOBALS['SessionLength'] = $row['SessionLength'];
        $GLOBALS['SessionGrpSize'] = $row['SessionGrpSize'];
        $GLOBALS['Language'] = $row['Language'];
        $GLOBALS['Provider_Name'] = $row['Provider_Name'];
        $GLOBALS['Provider_ID'] = $row['Provider_ID'];
        $GLOBALS['Provider_Address'] = $row['Provider_Address'];
        $GLOBALS['Provider_Phone'] = $row['Provider_Phone'];
        $GLOBALS['Provider_Email'] = $row['Provider_Email'];
        
    }   



  // initiate FPDI
/*    $pdf = new FPDI();

    $pageNo = 0;
 
 
   // get the page count
    $pageCount = $pdf->setSourceFile('RSA7A.pdf');
    // iterate through all pages
  
    $pageNo = $pageNo + 1;
 
    // import a page
    $templateId = $pdf->importPage($pageNo);
    // get the size of the imported page
    $size = $pdf->getTemplateSize($templateId);

*/


    $pageNo = write_header ($pdf, $pageNo, $size, $templateId, $ToDate);



    /* Print Service Details 
      =================================================== */


    $query1 = "SELECT   DISTINCT DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                        ServiceDate as ServiceDateSort,
                        DATE_FORMAT( ServiceDate, '%m/%d/%Y' ) AS ServiceDate,
                        StartTime as StartTimeSort,
                        DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
                        DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                        SessionGrpSize

                    FROM    WeeklyServices                  
                        Where RegistrantId= '{$RegistrantId}'
                        AND   StudentId = '{$StudentId}' 
                        AND   MandateId = '{$MandateId}' 
                        AND   ScheduleStatusId = '7'
                        AND   ServiceDate between  '{$FromDate}' and '{$ToDate}'  
                ORDER BY ServiceDateSort, StartTimeSort  
            ";
                                                         

            $result1 = $connection->query($query1);
             
            $j = $result1->numRows();

             

            $i = 0; 
            

            $pdf->Ln(30);
            

            while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {




                $i++;


                  
                if ($i > 10) {

                   $i = 1;                    
                  
                   $pageNo = write_header ($pdf, $pageNo, $size, $templateId);
                   $pdf->Ln(30);


                }   


                      $pdf->Cell(9,4,'',0,0,'L');
                      $pdf->Cell(25,4,$row1['ServiceDate'],0,0,'L');
                      $pdf->Cell(20,4,'',0,0,'L');
                      $pdf->Cell(15,4,'1',0,0,'L');
                      $pdf->Cell(20,4,'',0,0,'L');
                      $pdf->Cell(18,4,$row1['StartTime'],0,0,'C');
                      $pdf->Cell(24,4,'',0,0,'L');
                      $pdf->Cell(18,4,$row1['EndTime'],0,0,'C');
                      $pdf->Cell(25,4,'',0,0,'L');
                      $pdf->Cell(15,4,$row1['SessionGrpSize'],0,1,'L');


                      //$pdf->Ln(6);
                     
                       
                  switch ($i) {
                    case '1':
                      
                      // Service Date - Line 1 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);

                      break;
                    
                    case '2':
                      // Service Date - Line 2 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);



                      break;


                    case '3':

                     // Service Date - Line 3 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);

                      break;

                    case '4':

                     // Service Date - Line 4 


                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);

                      break;


                    case '5':

                     // Service Date - Line 5 
              
                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);


                      break;

                    case '6':

                       // Service Date - Line 6 
                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);


                      break;

                    case '7':

                     // Service Date - Line 7 


                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);
 

                      break;

                    case '8':

                     // Service Date - Line 8 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);


                      break;

                    case '9':

                      // Service Date - Line 9 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);

                      break;


                    case '10':

                     // Service Date - Line 10 

                      break;


                  }
                                 
                
            } 

       
 
    }
  


    // Output the new PDF
    $pdf->Output();
    //$pdf->Output($GLOBALS['Student_Name'].'-RSA7a.pdf','D');
    //ob_end_flush();    

    $connection->disconnect();

 
  


    /*=================================*/

    function write_header ($pdf, $pageNo, $size, $templateId, $ToDate) {

    /*

      // get the page count
      $pageCount = $pdf->setSourceFile('RSA7A.pdf');
      // iterate through all pages

      $pageNo = $pageNo + 1;

      // import a page
      $templateId = $pdf->importPage($pageNo);
      // get the size of the imported page
      $size = $pdf->getTemplateSize($templateId);
    */
      // create a page (landscape or portrait depending on the imported page size)
      if ($size['w'] > $size['h']) {
          $pdf->AddPage('L', array($size['w'], $size['h']));
      } else {
          $pdf->AddPage('P', array($size['w'], $size['h']));
      }

      // use the imported page
      $pdf->useTemplate($templateId);

      //$pdf->SetFont('Helvetica');
      $pdf->SetFont('Arial','',10);
    

      $pageNo = $pageNo + 1;
    

         // Current Month 
              $pdf->SetXY(125, 43);
              //$pdf->Cell(4,5,date('m'),0,0,'L');
              $pdf->Cell(4,5,date('m', strtotime($ToDate)),0,0,'L');



         // Current Year 
              $pdf->SetXY(175, 43);
              //$pdf->Cell(4,5,date('Y'),0,0,'L');
              $pdf->Cell(4,5,date('Y', strtotime($ToDate)),0,0,'L');

         // Student Name  

              $pdf->SetXY(40, 62);
              $pdf->Cell(60,4,$GLOBALS['Student_Name'],0,0,'L');

         
         // Student DOB 

              $pdf->SetXY(147, 61);
              $pdf->Cell(20,4,$GLOBALS['Student_DOB'],0,0,'L');


         // Student NYC ID  


              $pdf->SetXY(40, 71);
              $pdf->Cell(20,4,$GLOBALS['Student_NYC_Id'],0,0,'L');


         // Student District  


              $pdf->SetXY(100, 71);
              $pdf->Cell(25,4,$GLOBALS['Student_District'],0,0,'L');

         // Student Services  
              

              $pdf->SetXY(153, 71);
              $pdf->Cell(40,4,$GLOBALS['Student_Service_Type'],0,0,'L');


         // Student Hourly Rate  

      /*        
              $pdf->SetXY(35, 84);
              $pdf->Cell(15,4,'$50.00',0,0,'L');

      */
         // Student Service Frequency  
              

              $pdf->SetXY(74, 84);
              $pdf->Cell(10,4,$GLOBALS['SessionFrequency'],0,0,'L');


         // Student Service Duration  
              $pdf->SetXY(105, 84);
              $pdf->Cell(10,4,$GLOBALS['SessionLength'],0,0,'L');

         
         // Student Service Group Size  
              $pdf->SetXY(146, 84);
              $pdf->Cell(10,4,$GLOBALS['SessionGrpSize'],0,0,'L');


         // Student Service Language  
              $pdf->SetXY(176, 84);
              $pdf->Cell(20,4,$GLOBALS['Language'],0,0,'L');


         // Student Service Frequency  
              $pdf->SetXY(64, 94);
              $pdf->Cell(20,4,'School',0,0,'L');


         // Provider Name  
              $pdf->SetXY(42, 117);
              $pdf->Cell(60,4,$GLOBALS['Provider_Name'],0,0,'L');


         // Provider SS#   
              $pdf->SetXY(146, 117);
              $pdf->Cell(20,4,$GLOBALS['Provider_ID'],0,0,'L');


         // Provider Address  
              $pdf->SetXY(42, 127);
              $pdf->Cell(120,4,$GLOBALS['Provider_Address'],0,0,'L');

         // Provider Phone  
              $pdf->SetXY(42, 135);
              $pdf->Cell(25,4,$GLOBALS['Provider_Phone'],0,0,'L');

         // Provider E-Mail  
              $pdf->SetXY(146, 135);
              $pdf->Cell(50,4,$GLOBALS['Provider_Email'],0,0,'L');

         // Agency Name  
              $pdf->SetXY(41, 157);
              $pdf->Cell(50,4,$GLOBALS['Agency_Name'],0,0,'L');


         // Agency Tax ID 
              $pdf->SetXY(148, 157);
              $pdf->Cell(30,4,$GLOBALS['Agency_TaxId'],0,0,'L');


         // Agency Address  
              $pdf->SetXY(41, 166);
              $pdf->Cell(150,4,$GLOBALS['Agency_Address'],0,0,'L');


         // Agency Phone  
              $pdf->SetXY(41, 174);
              $pdf->Cell(25,4,$GLOBALS['Agency_Phone'],0,0,'L');


         // Agency E-Mail  
              $pdf->SetXY(148, 174);
              $pdf->Cell(40,4,$GLOBALS['Agency_Email'],0,1,'L');


        return  $pageNo;       

    }


?>
 
