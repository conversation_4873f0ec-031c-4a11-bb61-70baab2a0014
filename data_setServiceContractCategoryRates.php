<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$form_data = json_decode(file_get_contents('php://input'));


	$ServiceTypeId = $form_data->{'ServiceTypeId'};
	$ContractCategoryId = $form_data->{'ContractCategoryId'};
	$RateDateRangeId = $form_data->{'RateDateRangeId'};
	
	$WDBillRateShift1 = $form_data->{'WDBillRateShift1'};
	$WDPayRateShift1 = $form_data->{'WDPayRateShift1'};
	$WEBillRateShift1 = $form_data->{'WEBillRateShift1'};
	$WEPayRateShift1 = $form_data->{'WEPayRateShift1'};
	$WDBillRateShift2 = $form_data->{'WDBillRateShift2'};
	$WDPayRateShift2 = $form_data->{'WDPayRateShift2'};
	$WEBillRateShift2 = $form_data->{'WEBillRateShift2'};
	$WEPayRateShift2 = $form_data->{'WEPayRateShift2'};
	$WDBillRateShift3 = $form_data->{'WDBillRateShift3'};
	$WDPayRateShift3 = $form_data->{'WDPayRateShift3'};
	$WEBillRateShift3 = $form_data->{'WEBillRateShift3'};
	$WEPayRateShift3 = $form_data->{'WEPayRateShift3'};
	$WDBillRateShift4 = $form_data->{'WDBillRateShift4'};
	$WDPayRateShift4 = $form_data->{'WDPayRateShift4'};
	$WEBillRateShift4 = $form_data->{'WEBillRateShift4'};
	$WEPayRateShift4 = $form_data->{'WEPayRateShift4'};
	$WDBillRateShift5 = $form_data->{'WDBillRateShift5'};
	$WDPayRateShift5 = $form_data->{'WDPayRateShift5'};
	$WEBillRateShift5 = $form_data->{'WEBillRateShift5'};
	$WEPayRateShift5 = $form_data->{'WEPayRateShift5'};
	$UserId = $form_data->{'UserId'};
	
	/* Set Day Shift (1) WeekDay Rates
	==========================================*/
	
	
	$ShiftId = '1';
	$RateTypeId = '1';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WDBillRateShift1,
															$WDPayRateShift1,
															$UserId	
															); 

	/* Set Day Shift (1) WeekEnd Rates
	==========================================*/
	
	
	$ShiftId = '1';
	$RateTypeId = '2';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WEBillRateShift1,
															$WEPayRateShift1,
															$UserId	
															); 
	/* Set Evening Shift (2) WeekDay Rates
	==========================================*/
	
	
	$ShiftId = '2';
	$RateTypeId = '1';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WDBillRateShift2,
															$WDPayRateShift2,
															$UserId	
															); 

	/* Set Evening Shift (2) WeekEnd Rates
	==========================================*/
	
	
	$ShiftId = '2';
	$RateTypeId = '2';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WEBillRateShift2,
															$WEPayRateShift2,
															$UserId	
															); 															

															
	/* Set Night Shift (3) WeekDay Rates
	==========================================*/
	
	
	$ShiftId = '3';
	$RateTypeId = '1';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WDBillRateShift3,
															$WDPayRateShift3,
															$UserId	
															); 

	/* Set Night Shift (3) WeekEnd Rates
	==========================================*/
	
	
	$ShiftId = '3';
	$RateTypeId = '2';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WEBillRateShift3,
															$WEPayRateShift3,
															$UserId	
															); 															
	
	/* Set 12 Hr Day Shift (4) WeekDay Rates
	==========================================*/
	
	
	$ShiftId = '4';
	$RateTypeId = '1';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WDBillRateShift4,
															$WDPayRateShift4,
															$UserId	
															); 

	/* Set 12 Hr Day Shift (4) WeekEnd Rates
	==========================================*/
	
	
	$ShiftId = '4';
	$RateTypeId = '2';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WEBillRateShift4,
															$WEPayRateShift4,
															$UserId	
															); 						
	
	/* Set 12 Hr Night Shift (5) WeekDay Rates
	==========================================*/
	
	
	$ShiftId = '5';
	$RateTypeId = '1';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WDBillRateShift5,
															$WDPayRateShift5,
															$UserId	
															); 

	/* Set 12 Hr Night Shift (5) WeekEnd Rates
	==========================================*/
	
	
	$ShiftId = '5';
	$RateTypeId = '2';
	
	$result = $rcr_transaction->setServiceContractCategoryRates($ContractCategoryId,
															$ServiceTypeId,
															$RateDateRangeId,
															$ShiftId,
															$RateTypeId,
															$WEBillRateShift5,
															$WEPayRateShift5,
															$UserId	
															); 						
	
	/*********************************/														
	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
