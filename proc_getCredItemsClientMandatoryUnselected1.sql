

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getCredItemsClientMandatoryUnselected1$$


CREATE  PROCEDURE proc_getCredItemsClientMandatoryUnselected1 (IN p_client_id INT, p_service_type_id INT )
											  
BEGIN


	
	/*=================================================================================*/
	/* Get All Unselected Client Mandatory Credentialing Items for a gived Client/Client Unit   */ 
	/*=================================================================================*/
	
	create temporary table tmp engine=memory

		SELECT a.Id as id, 
		a.CredItemDesc,
		'0' as CondFL
		FROM  CredentialingItems a
		WHERE NOT EXISTS ( SELECT 1 FROM CredentialingItemsConditionalDetails b
							WHERE a.id = b.CredItemId)
	UNION
		SELECT  c.Id as id, 
		c.ConditionalItemDesc as CredItemDesc,
		'1' as CondFL
		FROM  CredentialingItemsConditionalHeader c ;
		 	
		
	
	SELECT * FROM tmp a
	WHERE  NOT EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b
						WHERE b.ClientId = p_client_id
						AND b.ServiceTypeId = p_service_type_id
                        AND a.id = b.MandatoryCredItemId)  						
	ORDER BY a.CredItemDesc ;	
	 
	
	
	drop temporary table if exists tmp;
	
 
END$$

DELIMITER ;	
