<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$ClientId = $_POST['ClientId'];
	$ClientUnitId = $_POST['ClientUnitId'];
	$ServiceDate = $_POST['ServiceDate'];
	$PayrollWeek = $_POST['PayrollWeek'];
	$ShiftId = $_POST['ShiftId'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	$ShiftsNumber = $_POST['ShiftsNumber'];
	$UserId = $_POST['UserId'];

	/* Status - '0' (Pending)
	============================*/
	$ScheduleStatusId = '0';
		
	
	/*
	/* Calculate Payroll Week (Saturday)
	============================*/
	/*
	$we_date = strtotime('next Saturday', strtotime($ServiceDate));
    $PayrollWeek =  date('Y-m-d', $we_date);	
    */

	/* Day Of Week ($ServiceDate)
	============================*/
	$WeekDay = date('l', strtotime($ServiceDate));
	$WeekDay = substr($WeekDay,0,3);
	
	//$WeekDay = date('w', $ServiceDate);

	for ($i=0; $i<$ShiftsNumber; $i++)
	{
	
	
		$result = $rcr_transaction->setClientAddPendingShifts(	$ClientId, 
																$ClientUnitId, 
																$ScheduleStatusId,
																$ServiceDate,
																$WeekDay, 
																$PayrollWeek,
																$ShiftId,
																$ServiceTypeId,
																$UserId); 
	
	}
	
	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
