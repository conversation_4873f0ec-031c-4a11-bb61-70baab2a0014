  
<?php
    

    require_once("db_GetSetData.php");
     $conn = getCon();

 

    $Rsa7aFormSignatureStatus = $_POST['Rsa7aFormSignatureStatus'];
    $UserId = $_POST['UserId'];

    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);
 

    if ($Rsa7aFormSignatureStatus == '4') {

      $ParentApprovalSentEmailDate = date("Y-m-d h:i:s");

    } else {

      $ParentApprovalSentEmailDate = ("0000-00-00 00:00:00");

    }

    foreach ($Data as $Rsa7aFormId) {

        
  

         $query = "UPDATE SchRsa7aFormSignatures       
             SET StatusId =  '{$Rsa7aFormSignatureStatus}',
                 ParentApprovalSentEmailDate = '{$ParentApprovalSentEmailDate}',
                 UserId = '{$UserId}',
                 TransDate = NOW()
           WHERE  Id =     '{$Rsa7aFormId}'
             ";
   

        $ret =  setData ($conn, $query);        
  
  }   


    
  setDisConn($conn);
  //echo $ret;
  echo $query;
 
    


?>
 
