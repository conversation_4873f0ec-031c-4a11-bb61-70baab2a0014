<?php 
 
 	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$ServiceDate = $_GET['ServiceDate'];  

	$query = "	 SELECT  	DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 	
					 		DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
							FORMAT((a.TotalHours * 60), 0) as TotalHours,
							group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
							group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
							
							SessionGrpSize,
							a.RegistrantId,
							COALESCE((SELECT StoredName FROM SchStudentsSessionNotes h
							WHERE a.Id = h.ScheduleId),'') as StoredDocName,

							COALESCE((SELECT DocumentName FROM SchStudentsSessionNotes h
							WHERE a.Id = h.ScheduleId),'') as DocumentName,
							-- a.ScheduleStatusId,
							SessionDeliveryModeId,
							CASE SessionDeliveryModeId 
								WHEN 'V' THEN 'Audio & Video'
								WHEN 'A' THEN 'Audio Only'
								WHEN 'I' THEN 'In-Person'
							ELSE 'Undefined'
							END AS SessionDeliveryModeDesc 
							

					FROM 	WeeklyServices a, SchStudents b
								WHERE a.RegistrantId = '{$RegistrantId}' 
								AND   a.ServiceDate =  '{$ServiceDate}'  
								AND   a.ScheduleStatusId > 5
								AND   b.Id = a.StudentId
					GROUP BY a.StartTime, a.EndTime, a.TotalHours	 ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
 
?>
