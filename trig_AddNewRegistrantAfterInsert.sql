
/*=========================================*/

DELIMITER $$

DROP TRIGGER IF EXISTS trig_AddNewRegistrantAfterInsert  $$

CREATE TRIGGER trig_AddNewRegistrantAfterInsert AFTER INSERT ON Registrants

FOR EACH ROW BEGIN

	DECLARE v_RegistrantGroupId INT;
	

	/*============================================================================= */
	/* Insert Cred. Items (Non-Conditoonal )based on Registrant Type ID */
	/*============================================================================= */

	   
	insert into RegistrantCredItems
		(
		RegistrantId,
		StatusId, 
		CredItemId,
		UserId,
		TransDate
		)
	SELECT 	NEW.Id,
			'2',
			MandatoryCredItemId,
			NEW.UserId,
			NEW.TransDate
	FROM  CredentialingItemsMandatory  
		WHERE RegistrantTypeId = NEW.TypeId
		AND CondFL = 0 ;
		    
	/*================================================================================== */
	/* Insert into "RegistrantCredentialingItemsConditional" DEFAULT Conditonal Items      */
	/*================================================================================== */
	INSERT INTO RegistrantCredentialingItemsConditional
	SELECT NEW.Id,
		   a.Id,
		   a.ConditionalSwitchDefault	
	FROM CredentialingItemsConditionalHeader a, CredentialingItemsMandatory b
		WHERE b.RegistrantTypeId = NEW.TypeId
		AND   CondFL = 1
		AND   a.Id = b.MandatoryCredItemId ;
	 
	
	/*============================================================================= */
	/* Insert Cred. Items (Conditonal )based on Defaulr Conditions */
	/*============================================================================= */

	   
	insert into RegistrantCredItems
		(
		RegistrantId,
		CredItemId,
		UserId,
		TransDate
		)
	Select 	NEW.Id,
			c.CredItemId,
			NEW.UserId,
			NEW.TransDate
	from  RegistrantCredentialingItemsConditional b, CredentialingItemsConditionalDetails c
		Where b.RegistrantId = NEW.Id
		And b.ConditionalItemId = c.ConditionalItemId
		And b.ConditionalSwitch = c.ConditionalSwitch ;
		
		
	/*============================================================================= */
	/* Insert User Information for Registrant Portal Access */
	/*============================================================================= */
	
	INSERT INTO Users
		( 	UserStatusId, 
			UserGroupId, 
			ExtId,
			Login, 
			Password, 
			FirstName, 
			LastName, 
			Email 
						) 
						VALUES 
						(
							'1',
							'5',
							NEW.SearchId,
							CONCAT( LOWER( SUBSTRING( NEW.FirstName, 1, 1 ) ) , LOWER( NEW.LastName )),
							MD5(CONCAT( LOWER( SUBSTRING( NEW.FirstName, 1, 1 ) ) , LOWER( NEW.LastName ),'1' )),
							NEW.FirstName,
							NEW.LastName,
							NEW.Email
						);
	
	
END; $$

DELIMITER ;