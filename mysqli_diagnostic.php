<?php
/**
 * MySQLi Diagnostic Script
 * This script helps diagnose MySQLi installation and configuration issues
 */

echo "<h1>MySQLi Diagnostic Report</h1>\n";
echo "<hr>\n";

// 1. Check PHP Version
echo "<h2>1. PHP Version Information</h2>\n";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>\n";
echo "<strong>PHP SAPI:</strong> " . php_sapi_name() . "<br>\n";
echo "<strong>Operating System:</strong> " . PHP_OS . "<br>\n";
echo "<br>\n";

// 2. Check if MySQLi class exists
echo "<h2>2. MySQLi Class Availability</h2>\n";
if (class_exists('mysqli')) {
    echo "✅ <strong>MySQLi class is AVAILABLE</strong><br>\n";
    echo "<strong>MySQLi Client Version:</strong> " . mysqli_get_client_info() . "<br>\n";
} else {
    echo "❌ <strong>MySQLi class is NOT AVAILABLE</strong><br>\n";
    echo "<span style='color: red;'>This is the source of your error!</span><br>\n";
}
echo "<br>\n";

// 3. Check loaded extensions
echo "<h2>3. Database-Related Extensions</h2>\n";
$db_extensions = ['mysqli', 'mysql', 'pdo', 'pdo_mysql'];
foreach ($db_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ <strong>$ext:</strong> Loaded<br>\n";
    } else {
        echo "❌ <strong>$ext:</strong> Not loaded<br>\n";
    }
}
echo "<br>\n";

// 4. List all loaded extensions
echo "<h2>4. All Loaded Extensions</h2>\n";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<div style='max-height: 200px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px;'>\n";
foreach ($extensions as $ext) {
    echo "$ext<br>\n";
}
echo "</div><br>\n";

// 5. Check PHP configuration
echo "<h2>5. PHP Configuration</h2>\n";
echo "<strong>Configuration File (php.ini):</strong> " . php_ini_loaded_file() . "<br>\n";
echo "<strong>Additional .ini files:</strong> " . (php_ini_scanned_files() ?: 'None') . "<br>\n";
echo "<br>\n";

// 6. Test MySQLi functionality (if available)
if (class_exists('mysqli')) {
    echo "<h2>6. MySQLi Functionality Test</h2>\n";
    
    // Test basic MySQLi instantiation
    try {
        // Don't actually connect, just test class instantiation
        echo "✅ MySQLi class can be instantiated<br>\n";
        
        // Test MySQLi constants
        if (defined('MYSQLI_ASSOC')) {
            echo "✅ MySQLi constants are available<br>\n";
        } else {
            echo "❌ MySQLi constants are not available<br>\n";
        }
        
        // Test MySQLi functions
        if (function_exists('mysqli_connect')) {
            echo "✅ MySQLi functions are available<br>\n";
        } else {
            echo "❌ MySQLi functions are not available<br>\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing MySQLi: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "<h2>6. MySQLi Not Available</h2>\n";
    echo "<span style='color: red;'>Cannot test MySQLi functionality because the class is not available.</span><br>\n";
}
echo "<br>\n";

// 7. Environment information
echo "<h2>7. Environment Information</h2>\n";
echo "<strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "<br>\n";
echo "<strong>Script Path:</strong> " . __FILE__ . "<br>\n";
echo "<strong>Current Working Directory:</strong> " . getcwd() . "<br>\n";
echo "<strong>User:</strong> " . (function_exists('posix_getpwuid') ? posix_getpwuid(posix_geteuid())['name'] : 'Unknown') . "<br>\n";
echo "<br>\n";

// 8. Recommendations
echo "<h2>8. Recommendations</h2>\n";
if (!class_exists('mysqli')) {
    echo "<div style='background-color: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>\n";
    echo "<strong>🚨 CRITICAL ISSUE:</strong> MySQLi extension is not installed or enabled.<br><br>\n";
    echo "<strong>To fix this on Ubuntu 24:</strong><br>\n";
    echo "1. <code>sudo apt update</code><br>\n";
    echo "2. <code>sudo apt install php-mysqli</code><br>\n";
    echo "3. <code>sudo systemctl restart apache2</code> (or nginx)<br>\n";
    echo "4. Refresh this page to verify the fix<br><br>\n";
    echo "<strong>Alternative commands:</strong><br>\n";
    echo "• <code>sudo apt install php" . PHP_MAJOR_VERSION . "." . PHP_MINOR_VERSION . "-mysqli</code><br>\n";
    echo "• <code>sudo apt install php-mysql</code><br>\n";
    echo "</div>\n";
} else {
    echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>\n";
    echo "✅ <strong>MySQLi is properly installed and available!</strong><br>\n";
    echo "Your PHP environment should be able to use MySQLi connections.<br>\n";
    echo "</div>\n";
}
echo "<br>\n";

// 9. Test database connection (if db_login.php exists)
echo "<h2>9. Database Connection Test</h2>\n";
if (file_exists('db_login.php') && class_exists('mysqli')) {
    echo "Found db_login.php file. Testing connection...<br>\n";
    try {
        include('db_login.php');
        
        // Check if variables are defined
        $required_vars = ['db_host', 'db_username', 'db_password', 'db_database'];
        $missing_vars = [];
        
        foreach ($required_vars as $var) {
            if (!isset($$var)) {
                $missing_vars[] = $var;
            }
        }
        
        if (!empty($missing_vars)) {
            echo "❌ Missing variables in db_login.php: " . implode(', ', $missing_vars) . "<br>\n";
        } else {
            echo "✅ All required variables found in db_login.php<br>\n";
            
            // Test actual connection
            $connection = new mysqli($db_host, $db_username, $db_password, $db_database);
            
            if ($connection->connect_error) {
                echo "❌ Database connection failed: " . $connection->connect_error . "<br>\n";
            } else {
                echo "✅ Database connection successful!<br>\n";
                echo "Connected to: " . $connection->host_info . "<br>\n";
                $connection->close();
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing database connection: " . $e->getMessage() . "<br>\n";
    }
} else {
    if (!file_exists('db_login.php')) {
        echo "⚠️ db_login.php file not found in current directory<br>\n";
    }
    if (!class_exists('mysqli')) {
        echo "❌ Cannot test database connection - MySQLi not available<br>\n";
    }
}

echo "<hr>\n";
echo "<p><em>Diagnostic completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
hr { margin: 20px 0; }
</style>
