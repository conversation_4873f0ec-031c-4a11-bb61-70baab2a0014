<?php 


require "ewDataHandler.php"; 
  
	$rcr_transaction = new dataHandler(); 

	$AlertId = $_POST['AlertId'];
	$Msg = $_POST['Msg'];
	$CloseAlertFL = $_POST['CloseAlertFL'];	
	$UserId = $_POST['UserId'];

	$SchoolFL = $_POST['SchoolFL'];
	if (!$SchoolFL) {

		$SchoolFL = '0';
	}



	$result = $rcr_transaction->setAlertMessageResponse($AlertId,
														$Msg,
														$SchoolFL,
														$UserId ); 

	if ($CloseAlertFL == 1) {

		$result1 = $rcr_transaction->setCloseAlertMessage(	$AlertId,
															$UserId ); 

	}

	$rcr_transaction->disconnectDB (); 

	echo $result;
?>
