<?php 
	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId = $_GET['RegistrantId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


	 
	$result = $rcr_transaction->getSchRegistrantDateRangeSchedules(	$RegistrantId, 
																	$FromDate,
																	$ToDate
																  );

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";

?>
