

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setRegistrantClientMandCredItems$$

CREATE PROCEDURE proc_setRegistrantClientMandCredItems (p_registrant_id INT,
														p_client_id INT,
														p_client_unit_id INT,
														p_service_type_id INT,
														p_user_id INT
														) 		 

BEGIN
	

	DECLARE v_MandCredItemId INT;
	DECLARE v_CondFL INT;	
	DECLARE v_RecCont INT;


	DECLARE done INT DEFAULT 0;

	
	/*============================================*/
	
	DECLARE cur1 CURSOR FOR
      SELECT   MandatoryCredItemId, CondFL	 
	FROM CredentialingItemsClientMandatory
		WHERE ClientId = p_client_id
		AND ClientUnitId in (0, p_client_unit_id)
		AND ServiceTypeId = p_service_type_id
		;
       
	declare continue handler for not found set done := true;   
	
	create temporary table tmp 

	(
		CredItemId INT,
		DeactivatedFL INT	
	);
	


	OPEN cur1;
	
	read_loop: LOOP
 
       FETCH cur1 INTO v_MandCredItemId, v_CondFL;
   
		IF done THEN
			LEAVE read_loop;
		END IF;	

		/*==========================*/
	
		
		/* Process Non-Conditional Cred. Items  */
		/*======================================*/

		IF (v_CondFL = 0) THEN /* Start - v_CondFL = 0 */


			SET v_RecCont = (SELECT COUNT(*) FROM RegistrantCredItems
								WHERE RegistrantId = p_registrant_id
								AND CredItemId =  v_MandCredItemId
								AND v_CondFL = '0'
				);

			IF (v_RecCont = 1) THEN

				/*==========================*/
				/* Active if previously deactivated Cred. Items */
				/* (Non-Conditional Items ONLY) */
				/*==========================*/


				UPDATE RegistrantCredItems 
					SET DeactivatedFL = '0'
				WHERE RegistrantId = 	p_registrant_id
				AND CredItemId = v_MandCredItemId
				AND DeactivatedFL = '1'
				AND v_CondFL = '0';


			ELSE

			/*==========================*/
			/* Insert New Cred. Items  te*/
			/* (Non-Conditional Items ONLY) */
			/*==========================*/

				INSERT INTO RegistrantCredItems
						(
						RegistrantId,
						StatusId, 
						CredItemId,
						UserId,
						TransDate
						)
					SELECT 	p_registrant_id,
							'2',
							v_MandCredItemId,
							p_user_id,
							NOW() ;
					 

			END IF;

		ELSE 	/* Else - v_CondFL = 1 */
		
		/* Process Conditional Cred. Items  */
		/*======================================*/

			SET v_RecCont = (SELECT COUNT(*) FROM RegistrantCredentialingItemsConditional
								WHERE RegistrantId = p_registrant_id
								AND ConditionalItemId =  v_MandCredItemId
				);

			IF (v_RecCont = 1) THEN

				/*==========================*/
				/* Active if previously deactivated Cred. Items */
				/* (Non-Conditional Items ONLY) */
				/*==========================*/


				UPDATE RegistrantCredItems 
					SET DeactivatedFL = '0'
				WHERE RegistrantId = 	p_registrant_id
				AND CredItemId in (SELECT a.CredItemId 
									FROM 	CredentialingItemsConditionalDetails a 
									WHERE 	a.ConditionalItemId = v_MandCredItemId )
				AND DeactivatedFL = '1'					 		
				;


			ELSE

				/*================================================================================== */
				/* Insert into "RegistrantCredentialingItemsConditional" DEFAULT Conditonal Items      */
				/*================================================================================== */
				INSERT INTO RegistrantCredentialingItemsConditional
				SELECT p_registrant_id,
					   a.Id,
					   a.ConditionalSwitchDefault	
				FROM CredentialingItemsConditionalHeader a
					WHERE a.Id = v_MandCredItemId
				;
					 
				 
				
				/*============================================================================= */
				/* Insert Cred. Items (Conditonal )based on Default Conditions */
				/*============================================================================= */

				   
				INSERT INTO RegistrantCredItems
					(
					RegistrantId,
					CredItemId,
					UserId,
					TransDate
					)
				SELECT 	p_registrant_id,
						c.CredItemId,
						p_user_id,
						NOW()
				FROM  RegistrantCredentialingItemsConditional b, CredentialingItemsConditionalDetails c
					WHERE b.RegistrantId = p_registrant_id
					AND b.ConditionalItemId = v_MandCredItemId
					AND b.ConditionalItemId = c.ConditionalItemId
					AND b.ConditionalSwitch = c.ConditionalSwitch ;
					 

			END IF;




		END IF; /* End - (v_CondFL = 0) */


		/*==========================*/




   	END LOOP;
  	CLOSE cur1; 
	
	SELECT v_MandCredItemId, v_CondFL;

	
	
END $$

DELIMITER ;	