  
<?php
    
	/** Include path **/
	//set_include_path(get_include_path() . PATH_SEPARATOR . '../../Classes/');
	
	//include 'PHPExcel/IOFactory.php';
	
	require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');
	

	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//==================================
	// Get School Session Start/End Dates
	//==================================
 	
	 
	$query = "SELECT SchoolSeasonEndDate
		FROM SchSchoolYear			 
		WHERE CurrentYearFL = '1'";
	
	$result = $connection->query ($query);
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$EndDate = $row['SchoolSeasonEndDate'];
	}	
	 
	/* Upload New File 
	 =============================================*/
		

	$inputFileName = '../uploads/mandates.csv';

 
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    Return ; 

	}
  

	$file_handle = fopen("../uploads/mandates.csv", "r");
	
	ini_set("auto_detect_line_endings", true);

	$cnt = 0;	
	$linecount = 0;	

   while (($row = fgetcsv($file_handle)) !== FALSE) { // while - start

   			$cnt++;


			$m_rec = substr(($row[4]),0,1);

			if (($m_rec == '2') || ($m_rec == '1')) { // m_rec - Start

				/* Student External ID
				 ===================*/

				$student_ext_id = $row[4];


				/* Student Last Name
				 ===================*/

				$student_last_name = $row[5];


				/* Student First Name
				 ===================*/

				$student_first_name = $row[6];


				/* Student DOB
				============================= */
				
			/*	
				$dob = $row[7];
				$date_unix_dob = date_create('30-12-1899');
    			date_add($date_unix_dob, date_interval_create_from_date_string("{$dob} days"));
    			$dob_str = date_format($date_unix_dob, 'Y-m-d');
			*/
    			$dob_str = date("Y-m-d", strtotime($row[11]));


				/* Student School ID
				 ===================*/

				$school_ext_id = '%'.$row[12].'%';



				/* Student Service Type Desc
				 ================================*/

				$student_serv_type_desc = $row[18];


				/* Student Language
				 ================================*/

				$student_language = $row[20];

				/* Mandate Ind/Grp Desc
				 ================================*/

				$mandate_ind_grp_desc = $row[21];


				/* Mandate Group Size
				 ================================*/

				$mandate_grp_size = $row[22];


				/* Mandate Service Freq
				 ================================*/

				// $mandate_serv_freq = $row[26];
				$mandate_serv_freq = $row[23];
				
				$mandate_serv_freq_num = filter_var($mandate_serv_freq, FILTER_SANITIZE_NUMBER_INT);

				/* Mandate Freq Type
				 ================================*/

				 $freq_arr  = explode(" ",$mandate_serv_freq);
				 $mandate_freq_type = $freq_arr[1];


				/* Mandate Service Duration
				 ================================*/

				$mandate_serv_dur = $row[24];
				$mandate_serv_dur_num = filter_var($mandate_serv_dur, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Start Date
				============================= */
							
    				//$start_date_str = date("Y-m-d", strtotime($row[29]));


				/* Mandate First Attend Date/Start Date
				=============================================================== */
							
    			$start_date_str = date("Y-m-d", strtotime($row[36]));
    			$start_date_str = $start_date_str;



				/* Mandate DOE Id
				============================= */
							
    			$mandate_doe_id = $row[38];;


				/* Provider Name
				 ================================*/

				
				$mandate_provider_name = $row[28];
				$mandate_provider_name = $connection->escapeSimple ($mandate_provider_name);
				$provider_name = explode(" ", $mandate_provider_name);
				$max = sizeof($provider_name);
				$provider_first_name = strtoupper($provider_name[0]);
				$provider_last_name = strtoupper($provider_name[$max-1]);

				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = trim($row[30]);



				/*========================*/
				//================================ 
				//  Check if Student Exists  
				//================================ 
	 			
				$query5 = "SELECT 1 
								FROM SchStudents 
							WHERE ExtId = trim('{$student_ext_id}') ";
							
					
							
				$result5 = $connection->query ($query5);

				if (DB::isError($result5)){
					die("Could not query the database:<br />$query5 ".DB::errorMessage($result));
				}			

				
				//=======================
				// Add New Student
				//=======================
				
				
				if ($result5->numRows() == 0) {  // Start 1 
					
					
					$query1 = "INSERT INTO SchStudents
						(ExtId, 
						 SearchId, 
						 StatusId, 
						 SchoolId,
						 DateOfBirth, 
						 FirstName, 
						 LastName, 
						 UserId, 
						 TransDate) 	
						VALUES 
						(
							'{$student_ext_id}',
							'{$student_ext_id}',
							'1',
							 COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1),0),

							'{$dob_str}',
							'{$student_first_name}',
							'{$student_last_name}',
							'{$user_id}',
							now() )";
					
						
					$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
			
					if (DB::isError($result1)){
								die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
					}
				} // End 1	


			//================================ 
			//  Check if Mandate Already Exists  
			//================================ 
		 	

			
			if (strlen($start_date_str) > 10) {

				$start_date_str = substr($start_date_str,0,10);	

			}

			// $query4 = "SELECT 1 
			// 			FROM SchStudentMandates a, SchServiceTypes b
			// 		WHERE a.StudentExtId = '{$student_ext_id}'
			// 		AND a.StartDate = '{$start_date_str}' 
 		// 			AND b.ServiceTypeDesc like '{$student_serv_type_desc}'	
 		// 			AND a.SessionGrpSize = '{$mandate_grp_size}'
 		// 			AND a.SessionFrequency = '{$mandate_serv_freq_num}'
   //                  AND a.ServiceTypeId = b.Id 
   //              /*    AND a.DOEFirstAttendDate = '{$mandate_first_attend_date}' */
   //                  AND a.DOEProvider = '{$mandate_provider_name}'  
   //                  AND a.StatusId = '1' 

   //                  ";
                    
 
			$query4 = "SELECT 1 
						FROM SchStudentMandates a, SchServiceTypes b
					WHERE a.StudentExtId = '{$student_ext_id}'
					AND a.StartDate = '{$start_date_str}' 
 					AND b.ServiceTypeDesc like '{$student_serv_type_desc}'	
 					AND a.SessionGrpSize = '{$mandate_grp_size}'
 					AND a.SessionFrequency = '{$mandate_serv_freq_num}'
 					AND a.SessionFrequencyType = '{$mandate_freq_type}'
                    AND a.ServiceTypeId = b.Id 
                    AND a.SessionLength  = '{$mandate_serv_dur_num}'  
                    AND a.DOEProvider = '{$mandate_provider_name}'  
                    AND a.StatusId = '1' 

                    ";


			$result4 = $connection->query ($query4);

		 	
			if (DB::isError($result4)){
						die("Could not query the database:<br />$query4 ".DB::errorMessage($result));
			}			
		 	
			$mandate_exists =& $result4->numRows();			
		 
			
			//=============================
			// Terminate Existing Mandate
			//============================



		/*	
			if (($mandate_exists == 1) && ($mandate_status_desc == 'Terminated'))  { // Inactivate Terminated Mandate  - Start

			$query2 = "UPDATE  SchStudentMandates a, SchServiceTypes b
						SET a.StatusId = '2'
					WHERE a.StudentExtId = '{$student_ext_id}'
					AND a.StartDate = '{$start_date_str}' 
 					AND b.ServiceTypeDesc like '{$student_serv_type_desc}'	
 					AND a.SessionGrpSize = '{$mandate_grp_size}'
                    AND a.ServiceTypeId = b.Id 
                     AND a.StatusId = '1' 
						 ";

						
						$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			


			}	// Inactivate Terminated Mandate  - End

			*/
			//=======================
			// Add New Mandate
			//=======================
			

			if (($mandate_exists == 0) && ($mandate_status_desc != 'Terminated'))  { // Mandate not exists - Start
			 


			$query2 = "INSERT INTO SchStudentMandates
						(	
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							RegistrantId,
							StartDate,
							EndDate,	
							SECMandateStatus,
							SessionFrequency,
							SessionFrequencyType,
							SessionLength,
							SessionGrpSize,
							Language,
							DOEServiceTypeId, 
							DOEFirstAttendDate,
							DOEProvider,
							MandateDOEId,
							UserId,
							TransDate
						) 
						SELECT   
						 
						 	'0',
							'{$student_ext_id}',
							COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '%{$school_ext_id}%' LIMIT 1),0),
							'1',
							Id,

							COALESCE((SELECT Id from Registrants 
							/*  WHERE  Concat(FirstName, ' ', LastName) like '{$mandate_provider_name}' LIMIT 1),0), */
							  WHERE  UPPER(Concat(TRIM(FirstName), ' ', TRIM(LastName)))  LIKE UPPER('%{$mandate_provider_name}%') 	LIMIT 1),0),
							'{$start_date_str}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_freq_type}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							
							CASE '{$mandate_ind_grp_desc}' 
								WHEN 'Individual' THEN DOEServiceTypeIndId
								ELSE DOEServiceTypeGrpId
							END,
							'{$mandate_first_attend_date}',
							'{$mandate_provider_name}',
							'{$mandate_doe_id}',
							'{$user_id}',
							now()  
						FROM SchServiceTypes
					WHERE ServiceTypeDesc like '{$student_serv_type_desc}' 
					AND '{$mandate_status_desc}' != 'Terminated' ";

						
						$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			



				} // Mandate not exists - End	        
		 
				/*=========================*/

			 	
			 
			} // m_rec - end



		} // while - end	


				//========================================================
				// Updade Student ID in the SchStudentMandates table
				//========================================================
				
				$query3 = "UPDATE SchStudents a,  SchStudentMandates b
                            SET b.StudentId = a.Id
                        WHERE b.StudentId = 0
                        AND  a.ExtId = b.StudentExtId  "; 		
 			
			
				$result3 = $connection->getAll($query3, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result3)){
			            die("Could not query the database:<br />$query3 ".DB::errorMessage($result));
			        }


	$connection->disconnect();
 
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";

?>