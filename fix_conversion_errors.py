#!/usr/bin/env python3
"""
Fix Common MySQLi Conversion Errors
===================================

This script fixes common errors that occur when converting from PEAR DB to MySQLi,
specifically targeting issues like mixed syntax and incorrect method calls.
"""

import re
import sys
import os
from typing import List, Tuple

class MySQLiConversionFixer:
    def __init__(self):
        self.fixes_applied = []
        
    def fix_file(self, file_path: str) -> str:
        """Fix common MySQLi conversion errors in a PHP file"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        original_content = content
        
        # Apply all fixes
        content = self._fix_connection_syntax(content)
        content = self._fix_error_handling(content)
        content = self._fix_result_methods(content)
        content = self._fix_connection_cleanup(content)
        content = self._fix_mixed_pear_mysqli(content)
        content = self._add_result_cleanup(content)
        
        return content
        
    def _fix_connection_syntax(self, content: str) -> str:
        """Fix broken MySQLi connection syntax"""
        # Fix double function call syntax like: new mysqli(...)("mysqli://...")
        pattern = r'new mysqli\s*\([^)]+\)\s*\([^)]+\)'
        if re.search(pattern, content):
            content = re.sub(pattern, 'new mysqli($db_host, $db_username, $db_password, $db_database)', content)
            self.fixes_applied.append("Fixed broken MySQLi connection syntax")
            
        # Fix invalid $this->connection references in procedural context
        pattern = r'\$this->connection->error'
        if re.search(pattern, content):
            content = re.sub(pattern, '$connection->error', content)
            self.fixes_applied.append("Fixed invalid $this->connection references")
            
        return content
        
    def _fix_error_handling(self, content: str) -> str:
        """Fix error handling methods"""
        # Fix PEAR DB getMessage() calls
        pattern = r'\$db->getMessage\(\)'
        if re.search(pattern, content):
            content = re.sub(pattern, '$connection->error', content)
            self.fixes_applied.append("Fixed PEAR DB getMessage() calls")
            
        # Fix error checking patterns
        pattern = r'if\s*\(\s*!\$([^|]+)\s*\|\|\s*\$this->connection->error\s*\)'
        if re.search(pattern, content):
            content = re.sub(pattern, r'if (!$\1 || $connection->error)', content)
            self.fixes_applied.append("Fixed error checking patterns")
            
        return content
        
    def _fix_result_methods(self, content: str) -> str:
        """Fix result set methods"""
        # Fix PEAR DB fetchRow() method
        pattern = r'\$([^=\s]+)\s*=&\s*\$([^-]+)->fetchRow\s*\(\s*\)'
        if re.search(pattern, content):
            content = re.sub(pattern, r'$\1 = $\2->fetch_assoc()', content)
            self.fixes_applied.append("Fixed PEAR DB fetchRow() method")
            
        # Fix while loop with fetchRow
        pattern = r'while\s*\(\s*\$([^=\s]+)\s*=&\s*\$([^-]+)->fetchRow\s*\(\s*\)\s*\)'
        if re.search(pattern, content):
            # This is more complex - replace with proper MySQLi fetch
            def replace_while_fetch(match):
                var_name = match.group(1)
                result_var = match.group(2)
                return f'if (${result_var}->num_rows > 0) {{\n        ${var_name} = ${result_var}->fetch_assoc()'
            content = re.sub(pattern, replace_while_fetch, content)
            self.fixes_applied.append("Fixed while fetchRow() loop")
            
        return content
        
    def _fix_connection_cleanup(self, content: str) -> str:
        """Fix connection cleanup methods"""
        # Fix PEAR DB disconnect() method
        pattern = r'\$connection->disconnect\s*\(\s*\)'
        if re.search(pattern, content):
            content = re.sub(pattern, '$connection->close()', content)
            self.fixes_applied.append("Fixed connection disconnect() method")
            
        return content
        
    def _fix_mixed_pear_mysqli(self, content: str) -> str:
        """Fix mixed PEAR DB and MySQLi code"""
        # Look for patterns that suggest mixed usage
        
        # Fix DB::isError with MySQLi result
        pattern = r'DB::isError\s*\(\s*\$([^)]+)\s*\)'
        if re.search(pattern, content):
            content = re.sub(pattern, r'!$\1', content)
            self.fixes_applied.append("Fixed mixed DB::isError() with MySQLi")
            
        # Fix DB::errorMessage with MySQLi
        pattern = r'DB::errorMessage\s*\(\s*\$([^)]+)\s*\)'
        if re.search(pattern, content):
            content = re.sub(pattern, '$connection->error', content)
            self.fixes_applied.append("Fixed mixed DB::errorMessage() with MySQLi")
            
        return content
        
    def _add_result_cleanup(self, content: str) -> str:
        """Add proper result cleanup where missing"""
        # Look for query results that aren't being freed
        # This is a simple heuristic - look for fetch_assoc() without corresponding free()
        
        if 'fetch_assoc()' in content and '->free()' not in content:
            # Add a comment suggesting result cleanup
            pattern = r'(\$[^=\s]+\s*=\s*\$[^-]+->fetch_assoc\(\);)'
            if re.search(pattern, content):
                def add_cleanup_comment(match):
                    return match.group(1) + '\n        // TODO: Add $result->free(); after processing result'
                content = re.sub(pattern, add_cleanup_comment, content)
                self.fixes_applied.append("Added result cleanup suggestions")
                
        return content
        
    def save_fixed_file(self, original_file: str, fixed_content: str, backup: bool = True) -> str:
        """Save the fixed content to a new file"""
        base_name = os.path.splitext(original_file)[0]
        extension = os.path.splitext(original_file)[1]
        
        # Create backup if requested
        if backup:
            backup_file = f"{base_name}_conversion_backup{extension}"
            if not os.path.exists(backup_file):
                with open(original_file, 'r', encoding='utf-8') as f:
                    backup_content = f.read()
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(backup_content)
                print(f"Backup created: {backup_file}")
        
        # Save fixed file
        fixed_file = f"{base_name}_conversion_fixed{extension}"
        with open(fixed_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
            
        return fixed_file
        
    def print_fixes_summary(self):
        """Print summary of fixes applied"""
        print("\n" + "="*50)
        print("CONVERSION FIXES APPLIED")
        print("="*50)
        
        if self.fixes_applied:
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"{i}. {fix}")
        else:
            print("No conversion issues found to fix.")
            
        print("="*50)

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 fix_conversion_errors.py <php_file>")
        print("Example: python3 fix_conversion_errors.py data_setSchDOEBillingUpload_mysqli.php")
        sys.exit(1)
    
    php_file = sys.argv[1]
    
    try:
        fixer = MySQLiConversionFixer()
        
        print(f"Fixing conversion errors in {php_file}...")
        fixed_content = fixer.fix_file(php_file)
        
        output_file = fixer.save_fixed_file(php_file, fixed_content)
        
        print(f"Fixed file saved as: {output_file}")
        fixer.print_fixes_summary()
        
        print("\n✅ Conversion error fixing completed!")
        print("\nNext steps:")
        print("1. Review the fixed file")
        print("2. Test the fixed file in your application")
        print("3. Consider implementing prepared statements for better security")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
