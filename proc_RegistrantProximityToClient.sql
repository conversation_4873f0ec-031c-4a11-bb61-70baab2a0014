

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_RegistrantProximityToClient$$

CREATE PROCEDURE proc_RegistrantProximityToClient (IN p_client_id int, p_registrant_type_id int, p_service_date varchar(10), p_start_time varchar(10), p_end_time varchar(10))  

BEGIN

 

	
	DECLARE v_Client_Zipcode VARCHAR(5);
	DECLARE v_Client_Latitude VARCHAR(8);
	DECLARE v_Client_Longitude VARCHAR(9);
	DECLARE v_Registrant_Latitude VARCHAR(8);
	DECLARE v_Registrant_Longitude VARCHAR(9);
	
	DECLARE done INT DEFAULT 0;	
	
	/* Get Client Zipcode*/
	/*=================================*/
	Select ZipCode into v_Client_Zipcode  
		from Clients
	Where Id = p_client_id;	
	
	/* Get Client Latitude/Longitude  */
	/*=================================*/
	Select Latitude, Longitude
		into v_Client_Latitude, v_Client_Longitude
		from ZipCodes
	Where SUBSTRING(v_Client_Zipcode,1,5) = ZipCode;	

	
	create temporary table tmp engine=memory

	SELECT a.RegistrantId,
		CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
		(SELECT max(ServiceDate) from WeeklyServices g
			where b.Id = g.RegistrantId
			AND g.ClientId = p_client_id 
			AND ScheduleStatusId = 7 ) as LastSchedDate,    
		(SELECT 1 from WeeklyServices g
			where b.Id = g.RegistrantId
			AND ServiceDate =  STR_TO_DATE( p_service_date, '%m-%d-%Y' ) 
			AND StartTime >= p_start_time 
			AND EndTime <= p_end_time 
			AND ScheduleStatusId in (5,6,7)) as ConflictSched,    
		b.City,
		COALESCE(MobilePhone,'') as MobilePhone,
		COALESCE(HomePhone,'') as HomePhone,
		TypeId,
		Latitude,
		Longitude,
		SUBSTRING(b.ZipCode,1,5) as ZipCode,
        0000.00 as ProximityToClient,
		(SELECT count(*) from RegistrantCredItems d
			where b.Id = d.RegistrantId
			AND ComplianceLevelId = 1 ) as CredItemsNonCompliant,    
		(SELECT count(*) from RegistrantCredItems d
            where b.Id = d.RegistrantId
			AND ComplianceLevelId != 1 ) as CredItemsCompliant

		
	FROM ClientApprovedRegistrants a,
		 Registrants b, 
		 ZipCodes c,
		 RegistrantTypes f
	where 	a.RegistrantId = b.Id 
			and b.TypeId = f.Id
			and a.Status = 1
			and SUBSTRING(b.ZipCode,1,5)  = c.ZipCode
			and a.ClientId = p_client_id
			and b.TypeId = p_registrant_type_id
			and not exists (SELECT 1 FROM ClientRestrictedRegistrants d
			                   WHERE a.RegistrantId = d.RegistrantId 
							   AND RestrictStatus = '1')			
			;
			
	
	
UPDATE tmp a, ZipCodes c   
	Set ProximityToClient = (
	3959 *
	acos(
		cos(radians(v_Client_Latitude)) *
		cos(radians(c.Latitude)) *
		cos(radians(c.Longitude) - radians(v_Client_Longitude)) +
		sin(radians(v_Client_Latitude)) *
		sin(radians(c.Latitude))
		) 
	) 
Where a.ZipCode = c.ZipCode;
	
	
	SELECT 	RegistrantId,
			RegistrantName,
            CASE COALESCE(LastSchedDate, 0)  
                WHEN '0' THEN ''
            ELSE DATE_FORMAT( LastSchedDate, '%m-%d-%Y' )
            END as LastSchedDate,
            CASE (ConflictSched)  
                WHEN '1' THEN 'Yes'
            ELSE 'No'
            END as ConflictSched,
			
			CredItemsNonCompliant,
			CredItemsCompliant,
			City,
			CONCAT ( '<p>' , trim(MobilePhone) , '</br> ',  trim(HomePhone), '</p>' ) as PhoneNumbers,
			TypeId,
			ZipCode,
			ProximityToClient,
			(SELECT  CASE count(*) 
                WHEN 0 THEN ''
					ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
				END as SpecialtiesList
            FROM RegistrantAttchedSpecialties a, Specialties b
                where 	b.Id = a.SpecialtyId
                        and  a.RegistrantId = tmp.RegistrantId) as SpecialtiesList,
		(SELECT  CASE count(*) 
                WHEN 0 THEN ''
					ELSE group_concat( CredItem SEPARATOR ', ' )
				END as NonComplList
			FROM RegistrantCredItems a, CredentialingItems b
				WHERE a.RegistrantId = tmp.RegistrantId
				AND b.Id = a.CredItemId
				AND ComplianceLevelId =1 )	 as NonComplList						
						
	From tmp
	Order By ProximityToClient, RegistrantName;

	drop temporary table if exists tmp;	
	
END $$

DELIMITER ;	