<?php 


	require "ewDataHandler.php"; 
  
	$rcr_transaction = new dataHandler(); 


	$RegistrantId = $_POST['RegistrantId'];
	$NotificationEmails = $_POST['NotificationEmails'];
	$Subject = $_POST['Subject'];
	$Message = $_POST['Message'];
	$RegistrantCredItemIdStr = $_POST['RegistrantCredItemIdStr'];
	$UserId = $_POST['UserId'];   

	$headers = 'From: <EMAIL>' . "\r\n" .
	    'Reply-To: <EMAIL>' . "\r\n" .
	    'X-Mailer: PHP/' . phpversion();

	mail($NotificationEmails, $Subject, $Message, $headers);


	/* Add "E-mail Sent" Comments for all returned Cred. Items
	 ==========================================================*/


	$Msg = 'Communicated by Email'; 
	$reg_cred_id_arr = explode(',', $RegistrantCredItemIdStr); 

	

	foreach ($reg_cred_id_arr as $reg_cred_id) {


	 
		$result = $rcr_transaction->setRegistrantCredItemMsgs (
												$reg_cred_id, 
												$Msg,
												'1',	  
												$UserId ); 
	 

	}
 
	/* Add  Message for the Registrant
	 ==================================================================================*/

	 	$reg_msg = 'Information related to Credentailing Items "Deficiencies" was E-mailed!';

		$resultq = $rcr_transaction->setRegistrantMessages(  $RegistrantId,
															'1',
															'47',
															$reg_msg,
															$UserId ); 

	
	$rcr_transaction->disconnectDB (); 

?>
