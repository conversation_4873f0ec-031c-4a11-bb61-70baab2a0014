#!/bin/bash

# Fix MySQLi Installation on Ubuntu 24
# This script installs and configures MySQLi extension

echo "🔧 Fixing MySQLi Installation on Ubuntu 24"
echo "=========================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to get PHP version
get_php_version() {
    php -r "echo PHP_MAJOR_VERSION.'.'.PHP_MINOR_VERSION;"
}

# Check if PHP is installed
if ! command_exists php; then
    echo "❌ PHP is not installed. Installing PHP first..."
    sudo apt update
    sudo apt install -y php php-cli
fi

PHP_VERSION=$(get_php_version)
echo "✅ PHP Version: $PHP_VERSION"

# Check current MySQLi status
echo ""
echo "🔍 Checking current MySQLi status..."
MYSQLI_STATUS=$(php -m | grep -i mysqli || echo "NOT_FOUND")

if [ "$MYSQLI_STATUS" = "NOT_FOUND" ]; then
    echo "❌ MySQLi extension is NOT installed"
else
    echo "✅ MySQLi extension is already installed: $MYSQLI_STATUS"
fi

# Install MySQLi extension
echo ""
echo "📦 Installing MySQLi extension..."
sudo apt update

# Install MySQLi for the current PHP version
if [[ "$PHP_VERSION" == "8."* ]]; then
    echo "Installing MySQLi for PHP 8.x..."
    sudo apt install -y php-mysqli php-mysql
elif [[ "$PHP_VERSION" == "7."* ]]; then
    echo "Installing MySQLi for PHP 7.x..."
    sudo apt install -y php-mysqli php-mysql
else
    echo "Installing MySQLi for PHP (generic)..."
    sudo apt install -y php-mysqli php-mysql
fi

# Also install other commonly needed extensions
echo "📦 Installing additional PHP extensions..."
sudo apt install -y php-mbstring php-xml php-curl php-zip php-gd

# Restart web server
echo ""
echo "🔄 Restarting web services..."

# Check which web server is running
if systemctl is-active --quiet apache2; then
    echo "Restarting Apache..."
    sudo systemctl restart apache2
    WEB_SERVER="Apache"
elif systemctl is-active --quiet nginx; then
    echo "Restarting Nginx and PHP-FPM..."
    sudo systemctl restart nginx
    sudo systemctl restart php${PHP_VERSION}-fpm
    WEB_SERVER="Nginx"
else
    echo "⚠️  No web server detected. You may need to restart your web server manually."
    WEB_SERVER="Unknown"
fi

# Verify installation
echo ""
echo "✅ Verifying MySQLi installation..."
MYSQLI_CHECK=$(php -m | grep -i mysqli)

if [ -n "$MYSQLI_CHECK" ]; then
    echo "✅ MySQLi is now installed and available: $MYSQLI_CHECK"
else
    echo "❌ MySQLi installation failed. Manual intervention required."
    exit 1
fi

# Test MySQLi functionality
echo ""
echo "🧪 Testing MySQLi functionality..."
php -r "
if (class_exists('mysqli')) {
    echo '✅ MySQLi class is available\n';
    echo '✅ MySQLi version: ' . mysqli_get_client_info() . '\n';
} else {
    echo '❌ MySQLi class is still not available\n';
    exit(1);
}
"

# Check PHP configuration
echo ""
echo "📋 PHP Configuration Summary:"
echo "PHP Version: $(php -v | head -n1)"
echo "PHP Extensions: $(php -m | grep -E '(mysqli|mysql|pdo)' | tr '\n' ' ')"
echo "Web Server: $WEB_SERVER"

# Provide next steps
echo ""
echo "🎉 MySQLi Installation Complete!"
echo "================================"
echo ""
echo "Next steps:"
echo "1. Test your PHP application"
echo "2. If you still get errors, check the PHP error log"
echo "3. Verify your database credentials in db_login.php"
echo ""
echo "Useful commands:"
echo "  - Check PHP modules: php -m | grep mysqli"
echo "  - Check PHP version: php -v"
echo "  - Test MySQLi: php -r \"new mysqli('localhost', 'user', 'pass', 'db');\""
echo "  - View PHP error log: sudo tail -f /var/log/apache2/error.log"
echo ""
echo "If you continue to have issues, run:"
echo "  sudo apt install --reinstall php-mysqli"
echo "  sudo systemctl restart apache2"
