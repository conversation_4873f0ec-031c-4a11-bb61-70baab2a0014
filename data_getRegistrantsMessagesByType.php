<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$MessageTypeId = $_GET['MessageTypeId'];
	$FromMessageDateUnf = $_GET['FromMessageDate'];
	$ToMessageDateUnf = $_GET['ToMessageDate'];
	
	$FromMessageDate = substr($FromMessageDateUnf, 0, 10);
	$ToMessageDate = substr($ToMessageDateUnf, 0, 10);

	$FromMessageDate = $FromMessageDate. ' 00:00:00';
	$ToMessageDate = $ToMessageDate. ' 23:59:59';


	$result = $rcr_transaction->RegistrantsMessagesByType(	$MessageTypeId,
															$FromMessageDate,
															$ToMessageDate );
					

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";

?>
