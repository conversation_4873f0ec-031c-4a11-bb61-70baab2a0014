

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getRegistrantsNonComplCredItemsList$$

	CREATE PROCEDURE proc_getRegistrantsNonComplCredItemsList (IN 	p_cred_item_id VARCHAR(10), 
																	p_reg_type_id VARCHAR(10),
																	p_client_id VARCHAR(10))  


BEGIN

	create temporary table tmp1 engine=memory
		SELECT a.RegistrantId, 
		       b.ClientName
	FROM ClientApprovedRegistrantsNonRest a, Clients b
	WHERE a.ClientId = p_client_id
	AND   a.ClientId = b.Id   ;

	
	create temporary table tmp engine=memory
	SELECT 	c.Id as RegistrantId,
			CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers,
			Email,
			CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')', ' (', c.Id,')') as RegistrantName,
			CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)) as RegistrantNameDisp,
			ClientName
	FROM 	Registrants c,
			RegistrantTypes f,	
			tmp1 d			
	WHERE 	c.TypeId = f.Id
	AND     c.TypeId like p_reg_type_id
	AND 	c.StatusId = '1'
	AND     c.Id = d.RegistrantId
	AND EXISTS ( SELECT 1 from RegistrantCredItems a
					WHERE c.Id = a.RegistrantId
					AND a.ComplianceLevelId = 1	
		)
	;	


	create temporary table tmp2 engine=memory 
	SELECT a.Id as  RegistrantCredItemTransId,
					a.RegistrantId,
					c.RegistrantName,
					c.RegistrantNameDisp,
					CredItemDesc,
					a.CredItemId,
					CASE CredItemType
						WHEN '1' THEN 'Needed Once'
						WHEN '2' THEN 'Needs Renewal'
						WHEN '3' THEN 'Conditional (Needs Renewal)'
					END AS CredItemTypeDesc,
					CASE  ComplianceLevelId 
						WHEN '1' THEN 'Non-Compliant'
						ELSE 'Compliant'
					END AS ComplianceLevelDesc,
					a.CredItemStatus,
					ComplianceLevelId,
					CredItemType,
					CredItemStatusDesc, 					
					COALESCE(( SELECT Msg
								FROM RegistrantCredItemMessages b
								WHERE b.Id = ( SELECT max( d.Id )
									FROM RegistrantCredItemMessages d
									WHERE d.RegistrantCredItemTransId = a.Id )),'') as LastMessage,
					COALESCE(( SELECT CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) )
								FROM RegistrantCredItemMessages b, Users e
								WHERE b.UserId = e.UserId
								AND b.Id = ( SELECT max( d.Id )
									FROM RegistrantCredItemMessages d
									WHERE d.RegistrantCredItemTransId = a.Id )),'') as UserName,
					
					COALESCE(( SELECT DATE_FORMAT( b.TransDate, '%m-%d-%Y %h:%i %p' )
								FROM RegistrantCredItemMessages b, Users e
								WHERE b.UserId = e.UserId
								AND b.Id = ( SELECT max( d.Id )
									FROM RegistrantCredItemMessages d
									WHERE d.RegistrantCredItemTransId = a.Id )),'') as TransDate,
					 

					
					(SELECT max(k.ServiceDate)  
					 FROM WeeklyServices k
					WHERE k.ScheduleStatusId > 6
					AND   k.RegistrantId = c.RegistrantId) as LastSchedDate ,
					
					PhoneNumbers,
					COALESCE(Email,'') as Email,
					COALESCE(a.Results,'') as Results,	
					COALESCE(a.Comments,'') as Comments,
					COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
					COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'00-00-0000')  as PrevExpirationDate,
					c.ClientName
		FROM 	RegistrantCredItems a,
				CredentialingItems b,
				CredentialingItemStatuses g,
				tmp c
									
		WHERE 	a.CredItemId = b.Id 
		AND 	a.RegistrantId = c.RegistrantId
		AND 	a.CredItemStatus = g.CredItemStatus
		AND 	a.ComplianceLevelId = 1
		AND     a.StatusId != '0'
		AND 	a.CredItemId like p_cred_item_id
		AND EXISTS (SELECT 1 FROM ClientApprovedRegistrants d
               WHERE a.RegistrantId = d.RegistrantId
               AND   d.Status = '1'
               AND   d.ClientId  =  p_client_id
		) 

	ORDER BY RegistrantName, CredItemDesc 	;
	 
	SELECT 	RegistrantCredItemTransId,
			RegistrantId,
			RegistrantName,
			RegistrantNameDisp,
			CredItemDesc,
			CredItemId,
			CredItemTypeDesc,
			ComplianceLevelDesc,
			CredItemStatus,
			ComplianceLevelId,
			CredItemType,
			CredItemStatusDesc, 					
			LastMessage,
			UserName,
			TransDate,
			COALESCE(DATE_FORMAT( LastSchedDate , '%m-%d-%Y' ),'')  as LastSchedDate ,			
			PhoneNumbers,
			Email,
			Results,	
			Comments,
			ExpirationDate,
			PrevExpirationDate,
			ClientName 

	FROM tmp2;


	drop temporary table if exists tmp;
	drop temporary table if exists tmp1;
	drop temporary table if exists tmp2;


END $$

	DELIMITER ;	