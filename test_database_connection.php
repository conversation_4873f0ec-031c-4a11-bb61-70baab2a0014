<?php
/**
 * Database Connection Tester
 * Tests database credentials and connection for the converted MySQLi application
 */

echo "<h1>Database Connection Test</h1>\n";
echo "<hr>\n";

// 1. Check if db_login.php exists
$db_login_file = 'db_login.php';
if (!file_exists($db_login_file)) {
    echo "<div style='color: red; background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>\n";
    echo "<strong>❌ ERROR:</strong> db_login.php file not found!<br>\n";
    echo "Expected location: " . realpath('.') . "/db_login.php<br>\n";
    echo "Please create this file with your database credentials.\n";
    echo "</div>\n";
    exit;
}

echo "<h2>1. Loading Database Configuration</h2>\n";
echo "✅ Found db_login.php file<br>\n";

// Include the database configuration
include($db_login_file);

// 2. Check required variables
echo "<h2>2. Checking Configuration Variables</h2>\n";
$required_vars = ['db_host', 'db_username', 'db_password', 'db_database'];
$missing_vars = [];

foreach ($required_vars as $var) {
    if (isset($$var)) {
        $value = $$var;
        if ($var === 'db_password') {
            $display_value = str_repeat('*', strlen($value));
        } else {
            $display_value = $value;
        }
        echo "✅ <strong>$var:</strong> $display_value<br>\n";
    } else {
        $missing_vars[] = $var;
        echo "❌ <strong>$var:</strong> NOT SET<br>\n";
    }
}

if (!empty($missing_vars)) {
    echo "<div style='color: red; background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>\n";
    echo "<strong>❌ ERROR:</strong> Missing required variables: " . implode(', ', $missing_vars) . "<br>\n";
    echo "Please add these variables to your db_login.php file.\n";
    echo "</div>\n";
    exit;
}

// 3. Test MySQLi availability
echo "<h2>3. Checking MySQLi Availability</h2>\n";
if (class_exists('mysqli')) {
    echo "✅ MySQLi class is available<br>\n";
    echo "MySQLi Client Version: " . mysqli_get_client_info() . "<br>\n";
} else {
    echo "<div style='color: red; background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>\n";
    echo "❌ <strong>ERROR:</strong> MySQLi class is not available!<br>\n";
    echo "Please install the MySQLi extension: sudo apt install php-mysqli<br>\n";
    echo "</div>\n";
    exit;
}

// 4. Test database connection
echo "<h2>4. Testing Database Connection</h2>\n";

try {
    $connection = new mysqli($db_host, $db_username, $db_password, $db_database);
    
    if ($connection->connect_error) {
        echo "<div style='color: red; background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>\n";
        echo "<strong>❌ CONNECTION FAILED:</strong><br>\n";
        echo "<strong>Error:</strong> " . $connection->connect_error . "<br>\n";
        echo "<strong>Error Number:</strong> " . $connection->connect_errno . "<br><br>\n";
        
        // Provide specific error guidance
        if (strpos($connection->connect_error, 'Access denied') !== false) {
            echo "<strong>🔧 SOLUTION:</strong><br>\n";
            echo "1. Check your username and password are correct<br>\n";
            echo "2. Ensure the database user exists and has proper permissions<br>\n";
            echo "3. Run these MySQL commands as root:<br>\n";
            echo "<code>\n";
            echo "CREATE USER IF NOT EXISTS '$db_username'@'$db_host' IDENTIFIED BY 'your_password';<br>\n";
            echo "GRANT ALL PRIVILEGES ON `$db_database`.* TO '$db_username'@'$db_host';<br>\n";
            echo "FLUSH PRIVILEGES;<br>\n";
            echo "</code>\n";
        } elseif (strpos($connection->connect_error, 'Unknown database') !== false) {
            echo "<strong>🔧 SOLUTION:</strong><br>\n";
            echo "1. Create the database: <code>CREATE DATABASE `$db_database`;</code><br>\n";
            echo "2. Or check if the database name is correct in db_login.php<br>\n";
        } elseif (strpos($connection->connect_error, "Can't connect to MySQL server") !== false) {
            echo "<strong>🔧 SOLUTION:</strong><br>\n";
            echo "1. Check if MySQL/MariaDB is running: <code>sudo systemctl status mysql</code><br>\n";
            echo "2. Start the service: <code>sudo systemctl start mysql</code><br>\n";
            echo "3. Check if the host '$db_host' is correct<br>\n";
        }
        
        echo "</div>\n";
        exit;
    }
    
    echo "<div style='color: green; background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>\n";
    echo "✅ <strong>DATABASE CONNECTION SUCCESSFUL!</strong><br>\n";
    echo "<strong>Server Info:</strong> " . $connection->server_info . "<br>\n";
    echo "<strong>Host Info:</strong> " . $connection->host_info . "<br>\n";
    echo "<strong>Protocol Version:</strong> " . $connection->protocol_version . "<br>\n";
    echo "</div>\n";
    
    // 5. Test basic query
    echo "<h2>5. Testing Basic Query</h2>\n";
    
    $result = $connection->query("SELECT 1 as test_value");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ Basic query successful: " . $row['test_value'] . "<br>\n";
        $result->free();
    } else {
        echo "❌ Basic query failed: " . $connection->error . "<br>\n";
    }
    
    // 6. Test database selection
    echo "<h2>6. Testing Database Selection</h2>\n";
    
    if ($connection->select_db($db_database)) {
        echo "✅ Database '$db_database' selected successfully<br>\n";
        
        // Show tables in database
        $result = $connection->query("SHOW TABLES");
        if ($result && $result->num_rows > 0) {
            echo "✅ Found " . $result->num_rows . " tables in database:<br>\n";
            echo "<ul>\n";
            while ($row = $result->fetch_array()) {
                echo "<li>" . $row[0] . "</li>\n";
            }
            echo "</ul>\n";
            $result->free();
        } else {
            echo "⚠️ Database is empty (no tables found)<br>\n";
        }
    } else {
        echo "❌ Failed to select database '$db_database': " . $connection->error . "<br>\n";
    }
    
    // 7. Test character set
    echo "<h2>7. Testing Character Set</h2>\n";
    
    echo "Current charset: " . $connection->character_set_name() . "<br>\n";
    
    if ($connection->set_charset("utf8")) {
        echo "✅ Successfully set charset to utf8<br>\n";
    } else {
        echo "❌ Failed to set charset: " . $connection->error . "<br>\n";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>\n";
    echo "<strong>❌ EXCEPTION:</strong> " . $e->getMessage() . "<br>\n";
    echo "</div>\n";
}

// 8. Test your actual dataHandler class
echo "<h2>8. Testing Your DataHandler Class</h2>\n";

if (file_exists('ewDataHandler.php')) {
    echo "✅ Found ewDataHandler.php file<br>\n";
    
    try {
        include_once('ewDataHandler.php');
        
        if (class_exists('dataHandler')) {
            echo "✅ dataHandler class loaded successfully<br>\n";
            
            // Test instantiation
            $handler = new dataHandler();
            echo "✅ dataHandler instantiated successfully<br>\n";
            
            // Test disconnection
            $handler->disconnectDB();
            echo "✅ Database disconnection successful<br>\n";
            
        } else {
            echo "❌ dataHandler class not found in ewDataHandler.php<br>\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing dataHandler: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "⚠️ ewDataHandler.php file not found in current directory<br>\n";
}

echo "<hr>\n";
echo "<h2>Summary</h2>\n";
echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>\n";
echo "If all tests above passed, your database connection is working correctly!<br>\n";
echo "Your PHP application should now be able to connect to the database using MySQLi.<br><br>\n";
echo "<strong>Next steps:</strong><br>\n";
echo "1. Test your actual PHP application<br>\n";
echo "2. Check application logs for any remaining issues<br>\n";
echo "3. Verify all required database tables exist<br>\n";
echo "</div>\n";

echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
hr { margin: 20px 0; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
