<?php 

$PayrollWeek  = $_GET['PayrollWeek'];
	

$res = array(
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 6 day')),
        'dow' => 'sun',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 6 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 5 day')),
        'dow' => 'mon',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 5 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 4 day')),
        'dow' => 'tue',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 4 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 3 day')),
        'dow' => 'wed',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 3 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 2 day')),
        'dow' => 'thu',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 2 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 1 day')),
        'dow' => 'fri',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 1 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek)),
        'dow' => 'sat',
		'id' => date('m-d-Y',strtotime($PayrollWeek))

    ),
);

echo  "{ success: true,  data: ".json_encode($res)."}";
//echo var_dump($PayrollWeek);

?>

 