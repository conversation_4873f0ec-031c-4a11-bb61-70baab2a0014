<?php 

	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

	$SelectType = $_GET['SelectType'];
	$SearchId = $_GET['SearchId'];

	

	if ($SelectType == 'ActiveOnly') {

		$Statuses = '(1)';

	} else {

		$Statuses = '(1,2,3)';
	}
	
	if(!$SearchId) {

		$SearchId = '%%';
	}
	
	
	$result = $rcr_transaction->getRegistrants($Statuses, $SearchId);
	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
	  

?>
