

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setClientOrientationCandidateSchedule$$

CREATE PROCEDURE proc_setClientOrientationCandidateSchedule (p_orient_id INT, p_registrant_id INT, p_user_id INT)  

BEGIN

 	
	/*============================================*/
	
	create temporary table tmp 

	(
		PayrollWeek date,
		ServiceDate date,
		WeekDay varchar(3)
	
	);
	

	
	/*====================================================*/
	/* Set First Date for Orientation Schedule    */
	/*====================================================*/
	
	
	INSERT INTO tmp (ServiceDate)
	
	SELECT StartDate FROM ClientOrientations
		WHERE Id = p_orient_id;
		
	/*====================================================*/
	/* Set Additional Dates for Orientation Schedule    */
	/*====================================================*/
	
	INSERT INTO tmp (ServiceDate)
	
	SELECT OrientAddtnlDate FROM ClientOrientationAddtnlDates
		WHERE OrientationId = p_orient_id;	


	/*====================================================*/
	/* Set Payroll Week for Orientation Schedule    */
	/*====================================================*/
		
	UPDATE tmp
		SET PayrollWeek = date_add(ServiceDate, interval IF(7-dayofweek(ServiceDate) = 0, 7, 7-dayofweek(ServiceDate)) day),
			WeekDay = SUBSTRING((dayname( ServiceDate)),1,3)
		;


	
	/*====================================================*/
	
	INSERT INTO WeeklyServices
		  (
			PayrollWeek,
			ClientId,
			ClientUnitId,
			ScheduleStatusId,
			ServiceDate,	 
			StartTime, 
			EndTime, 		
			TotalHours , 
			LunchHour,
			WeekDay ,
			RegistrantId, 
			RegistrantTypeId,
			ServiceTypeId,
			OrientationId,
			UserId,
			TransDate )	
	
	
	SELECT 	a.PayrollWeek,
			b.ClientId,
			b.ClientUnitId,
			'7',
			a.ServiceDate,
			c.StartTime,
			c.EndTime,
			c.TotalHours,
			c.LunchHour,
			a.WeekDay,
			p_registrant_id,
			b.RegistrantTypeId,
			b.ServiceTypeId,
			p_orient_id,
			p_user_id,
			NOW()	
			
	FROM tmp a,  ClientOrientations b, ClientShifts c	
		WHERE b.Id = p_orient_id
		AND b.ClientId = c.ClientId
		AND c.ShiftId = '1';
	
	drop temporary table if exists tmp;
	 
	
END $$

DELIMITER ;	