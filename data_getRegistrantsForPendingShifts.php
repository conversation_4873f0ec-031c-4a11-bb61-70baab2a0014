<?php 
require "ewDataHandler.php"; 
  
	$rcr_transaction = new dataHandler(); 

	$ClientId = $_GET['ClientId'];
	$ClientUnitId = $_GET['ClientUnitId'];
	$ServiceTypeId = $_GET['ServiceTypeId'];
	$ServiceDate = $_GET['ServiceDate'];
	$ShiftId = $_GET['ShiftId'];

	/* Calculate Payroll Week (Saturday)
	============================*/
	$we_date = strtotime('next Saturday', strtotime($ServiceDate));
    $PayrollWeek =  date('Y-m-d', $we_date);	
	

	$result = $rcr_transaction->getRegistrantsForPendingShifts(	$ClientId, 
																$ClientUnitId,
																$ServiceTypeId,		
																$ServiceDate, 
																$PayrollWeek, 
																$ShiftId);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
	  

?>
