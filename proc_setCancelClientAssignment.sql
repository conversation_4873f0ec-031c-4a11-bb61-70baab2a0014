

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setCancelClientAssignment$$


CREATE  PROCEDURE proc_setCancelClientAssignment(IN p_assignment_id INT,
												    p_msg VARCHAR(96),
													p_user_id INT
											)
BEGIN


	
	/*============================================*/
	/* Get All Schedules to Be Cancelled   */ 
	/*============================================*/
	
	create temporary table tmp engine=memory

	SELECT Id as ScheduleId
		FROM WeeklyServices 
	WHERE AssignmentId = p_assignment_id
	AND ScheduleStatusId = '6'; 	
		
	/*============================================*/
	/* Add Cancellation Messages   */ 
	/*============================================*/
	
	INSERT INTO WeeklyServicesMessages
	
	SELECT ScheduleId,
		   p_msg,
		   '1',
		   p_user_id,
		   NOW()
	FROM tmp;	   
	
	
	/*=========================================*/
    /* Set Assignment's  Status to "Cancelled" */
	/*=========================================*/

	UPDATE WeeklyServices a, tmp b
		SET ScheduleStatusId = '3'
	WHERE a.Id = b.ScheduleId;  	
	
	/*================================*/
    /* Cancel Assignment's  "Pend Confirmation" Shifts */
	/*===============================*/
		
	UPDATE ClientAssignments
		SET StatusId = '3'
	WHERE Id =  p_assignment_id;		
	
	drop temporary table if exists tmp;
	
 
END$$

DELIMITER ;	
