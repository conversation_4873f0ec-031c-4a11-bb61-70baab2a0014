  
<?php
    
    ob_start();

    require_once('fpdf/fpdf.php');
    require_once('fpdi/fpdi.php');
    require_once("db_login.php");
    require_once('DB.php');
    require_once("db_GetSetData.php");

    $conn = getCon();



    $RegistrantId = $_GET['RegistrantId'];

    $FromDate = $_GET['FromDate'];

    $ToDate = $_GET['ToDate'];

    $GLOBALS['ReportMonth'] = date('m', strtotime($ToDate));
    $GLOBALS['ReportYear'] = date('Y', strtotime($ToDate));


    $Data = $_GET['Data'];
    $Data=json_decode($Data,true);


    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

    
    if (DB::isError($connection)){
        die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 



  // initiate FPDI
    $pdf = new FPDI();

    $pageNo = 0;
 
   // get the page count

    $pageCount = $pdf->setSourceFile('../../RSA7A.pdf');
    // iterate through all pages

 
      $pageNo = $pageNo + 1;

    // import a page
    $templateId = $pdf->importPage($pageNo);
    // get the size of the imported page
    $size = $pdf->getTemplateSize($templateId);
    $pdf->SetAutoPageBreak(false);

  //echo 'Data Size: '.sizeof($Data).'</br>';
    

  //foreach ($Data as $StudentId) {




  $x = 0;
  foreach ($Data as $StudentData) {

      
    //var_dump($StudentData);

    $StudentId = $StudentData['StudentId'];
    $MandateId = $StudentData['MandateId'];
     
    //echo'StudentId: '.$StudentId.' Mandate ID: '.$MandateId.'</br>';

    ++$x;

    //echo 'Student: '.$StudentId.'</br>';


    //==================================
    // Get Company/Client Name
    //==================================
    
    $query = "SELECT  a.CompanyName as Agency_Name,
                      CONCAT( a.StreetAddress1, ' ', a.City, ' ', a.State, ' ', a.ZipCode) as Agency_Address,
                      a.TIN as Agency_TaxId,
                      a.PhoneNumber as Agency_Phone,
                      a.Email as Agency_Email,
                      CONCAT( b.FirstName, ' ', b.LastName) as Student_Name,
                      b.ExtId as Student_NYC_Id,
                      DATE_FORMAT( b.DateOfBirth, '%m/%d/%Y' ) AS Student_DOB,
                      d.DistrictName as Student_District,
                      g.ServiceTypeDesc as Student_Service_Type,
                      f.SessionFrequency,
                      f.SessionLength,
                      f.SessionGrpSize,
                      f.Language,
                      c.SchoolName,
                      CONCAT( e.FirstName, ' ', e.LastName) as Provider_Name,
                      e.ExtId as Provider_ID,
                      CONCAT( e.StreetAddress1, ' ', e.City, ' ', e.State, ' ', e.ZipCode) as Provider_Address,
                      e.MobilePhone as Provider_Phone,
                      e.Email as Provider_Email,
                      GuardianEmail,
                      GuardianPhone



                      FROM Company a,
                           SchStudents b,
                           SchSchools c,
                           SchDistricts d,
                           Registrants e,
                           SchStudentMandates f,
                           SchServiceTypes g
                      WHERE b.Id = '{$StudentId}'
                      AND   e.Id = '{$RegistrantId}'
                      
                  /*    AND   f.StudentId = '{$StudentId}'
                      AND   f.RegistrantId = '{$RegistrantId}'
                  */    
                   
                      AND   f.Id = '{$MandateId}'  
                      AND   f.SchoolId = c.Id
                      AND   c.DistrictId = d.Id
                      AND   f.ServiceTypeId = g.Id ";
                
    
    $result = $connection->query ($query);
    while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
        $GLOBALS['Agency_Name'] = $row['Agency_Name'];
        $GLOBALS['Agency_Address'] = $row['Agency_Address']; 
        $GLOBALS['Agency_TaxId'] = $row['Agency_TaxId'];
        $GLOBALS['Agency_Phone'] = $row['Agency_Phone'];
        $GLOBALS['Agency_Email'] = $row['Agency_Email'];
        $GLOBALS['Student_Name'] = $row['Student_Name'];        
        $GLOBALS['Student_NYC_Id'] = $row['Student_NYC_Id'];
        $GLOBALS['Student_DOB'] = $row['Student_DOB'];
        $GLOBALS['Student_District'] = $row['Student_District'];
        $GLOBALS['Student_Service_Type'] = $row['Student_Service_Type'];
        $GLOBALS['SessionFrequency'] = $row['SessionFrequency'];
        $GLOBALS['SessionLength'] = $row['SessionLength'];
        $GLOBALS['SessionGrpSize'] = $row['SessionGrpSize'];
        $GLOBALS['Language'] = $row['Language'];
        $GLOBALS['Provider_Name'] = $row['Provider_Name'];
        $GLOBALS['Provider_ID'] = $row['Provider_ID'];
        $GLOBALS['Provider_Address'] = $row['Provider_Address'];
        $GLOBALS['Provider_Phone'] = $row['Provider_Phone'];
        $GLOBALS['Provider_Email'] = $row['Provider_Email'];

        $GLOBALS['SchoolName'] = $row['SchoolName'];

        $GLOBALS['GuardianEmail'] = $row['GuardianEmail'];
        $GLOBALS['GuardianPhone'] = $row['GuardianPhone'];

        
    }   



  // initiate FPDI
/*    $pdf = new FPDI();

    $pageNo = 0;
 
 
   // get the page count
    $pageCount = $pdf->setSourceFile('RSA7A.pdf');
    // iterate through all pages
  
    $pageNo = $pageNo + 1;
 
    // import a page
    $templateId = $pdf->importPage($pageNo);
    // get the size of the imported page
    $size = $pdf->getTemplateSize($templateId);

*/


    $pageNo = write_header ($pdf, $pageNo, $size, $templateId);



    /* Print Service Details 
      =================================================== */


    $query1 = "SELECT   DISTINCT DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                        ServiceDate as ServiceDateSort,
                        DATE_FORMAT( ServiceDate, '%m/%d/%Y' ) AS ServiceDate,
                        StartTime as StartTimeSort,
                        DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
                        DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                        SessionGrpSize,
                        SessionDeliveryModeId

                    FROM    WeeklyServices                  
                        Where RegistrantId= '{$RegistrantId}'
                        AND   StudentId = '{$StudentId}' 
                        AND   MandateId = '{$MandateId}' 
                        AND   ScheduleStatusId > '6'
                         AND   SessionDeliveryModeId != 'I'
                        AND   ServiceDate between  '{$FromDate}' and '{$ToDate}'  
                ORDER BY ServiceDateSort, StartTimeSort  
            ";
                                                         

            $result1 = $connection->query($query1);
             
            $j = $result1->numRows();
            $GLOBALS['NumOfSessions'] = $j;
             

            $i = 0; 
            

            $pdf->Ln(30);
            

            while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {




                $i++;


                  
                if ($i > 6) {

                   $i = 1;                    
                  
                   $pageNo = write_header ($pdf, $pageNo, $size, $templateId);
                   $pdf->Ln(30);


                }   


                      $pdf->Cell(9,4,'',0,0,'L');
                      $pdf->Cell(25,4,$row1['ServiceDate'],0,0,'L');
                      $pdf->Cell(10,4,'',0,0,'L');
                      $pdf->Cell(15,4,'1',0,0,'L');
                      $pdf->Cell(10,4,'',0,0,'L');
                      $pdf->Cell(18,4,$row1['StartTime'],0,0,'C');
                      $pdf->Cell(17,4,'',0,0,'L');
                      $pdf->Cell(18,4,$row1['EndTime'],0,0,'C');
                      $pdf->Cell(21,4,'',0,0,'L');
                      $pdf->Cell(15,4,$row1['SessionGrpSize'],0,0,'L');
                      $pdf->Cell(15,4,'',0,0,'L');
                      $pdf->Cell(10,4,$row1['SessionDeliveryModeId'],0,1,'C');


                      //$pdf->Ln(6);
                     
                       
                  switch ($i) {
                    case '1':
                      
                      // Service Date - Line 1 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);

                      break;
                    
                    case '2':
                      // Service Date - Line 2 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);



                      break;


                    case '3':

                     // Service Date - Line 3 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);

                      break;

                    case '4':

                     // Service Date - Line 4 


                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);

                      break;


                    case '5':

                     // Service Date - Line 5 
              
                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);


                      break;

                    case '6':

                       // Service Date - Line 6 
                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);


                      break;

                    case '7':

                     // Service Date - Line 7 


                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 6);
 

                      break;

                    case '8':

                     // Service Date - Line 8 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);


                      break;

                    case '9':

                      // Service Date - Line 9 

                      $cur_y =  $pdf->GetY();
                      $pdf->SetY($cur_y + 5);

                      break;


                    case '10':

                     // Service Date - Line 10 

                      break;


                  }
                                 
                
            } 

    
   $pdf->SetY(261);

   $pdf->SetFont('Arial','B',10);
   $pdf->Cell(46,5,'',0,0,'C');
   $pdf->Cell(20,5,$GLOBALS['NumOfSessions'],0,0,'C');
 
   
   //==============

   // $GLOBALS['VerifiedByEmail'] = '4';

   // $pdf->SetFont('ZapfDingbats','B',10);
   // $pdf->SetY(320);
   // $pdf->SetX(150);

   // $pdf->Cell(10,5,$GLOBALS['VerifiedByEmail'],0,0,'C');


   // $GLOBALS['VerifiedByPhone'] = '4';
   // $pdf->Cell(16,5,'',0,0,'C');
   // $pdf->Cell(10,5,$GLOBALS['VerifiedByPhone'],0,0,'C');


   // $pdf->SetFont('Arial','B',10);


   //==============
   // Get RSA 7a Signatures - Start
   //==============================

        $query3 = "SELECT distinct 

                          StatusId,
                          ProviderSignatureName,
                          ProviderSignatureTitle, 
                          DATE_FORMAT( ProviderSignatureTimeStamp, '%m/%d/%Y' ) as ProviderSignatureTimeStamp,
                          SupervisorSignatureName,
                          DATE_FORMAT( SupervisorSignatureTimeStamp, '%m/%d/%Y' ) as SupervisorSignatureTimeStamp,
                          ParentSignatureName,
                          DATE_FORMAT( ParentSignatureTimeStamp, '%m/%d/%Y' ) as ParentSignatureTimeStamp,
                          PrincipalSignatureName,
                          DATE_FORMAT( PrincipalSignatureTimeStamp, '%m/%d/%Y' ) as PrincipalSignatureTimeStamp,
                          ParentApprovalEmailFileName,
                          DATE_FORMAT( TransDate, '%m/%d/%Y' ) as ParentPhoneApprovalTimeStamp



                 FROM SchRsa7aFormSignatures 
                    
                   WHERE MandateId = '{$MandateId}'
                   AND FromDate = '{$FromDate}' 
                   AND ToDate = '{$ToDate}'
                   AND FormTypeId = '2'
                   ";

    
    //echo '$query3: '.$query3.'</br>';               

    $result3 =  mysqli_query($conn, $query3) or die
    ("Error 3 in Selecting " . mysqli_error($conn));
    

    if (mysqli_num_rows($result3) == "1") {

        $row = $result3->fetch_assoc();

       //$pdf->SetFont('Arial','B',8);

       $pdf->SetFont('Arial','',10);


        $pdf->SetY(298);
        $pdf->Cell(35,4,'',0,0,'C');
        $pdf->Cell(40,4,$GLOBALS['GuardianEmail'].' / '.$GLOBALS['GuardianPhone'],0,0,'L');


        if ($row['ParentSignatureTimeStamp'] != '00/00/0000') { // Signed by School Principal



            $pdf->SetY(306);
            $pdf->Cell(35,4,'',0,0,'C');
            $pdf->Cell(30,4,$row['ParentSignatureTimeStamp'],0,0,'L');


  

        }  

        if ($row['StatusId'] == '4') { // Fully Signed



          $ver_by_email =  strpos($row['ParentApprovalEmailFileName'],'.eml',1); 


          if ($ver_by_email != '') {

             $pdf->SetFont('ZapfDingbats','B',10);
             $pdf->SetY(320);
             $pdf->SetX(150);

             $pdf->Cell(10,5,'4',0,0,'C');
             $pdf->SetFont('Arial','',10);


          } else {

            $pdf->SetY(306);
            $pdf->Cell(35,4,'',0,0,'C');
            $pdf->Cell(30,4,$row['ParentPhoneApprovalTimeStamp'],0,0,'L');


            $pdf->SetFont('ZapfDingbats','B',10);
            $pdf->SetY(320);
            $pdf->SetX(160);
            $pdf->Cell(16,5,'',0,0,'C');
            $pdf->Cell(10,5,'4',0,0,'C');
            $pdf->SetFont('Arial','',10);

 

          }



       }   

       if ($row['ProviderSignatureTimeStamp']) { // Signed by Provider


            // if ($row['ProviderSignatureTitle']) {

            //     $GLOBALS['Provider_Title'] =  $row['ProviderSignatureTitle'];
            // }


            $pdf->SetY(313);
            $provider_signature = $row['ProviderSignatureName'].' '.$GLOBALS['Provider_Title'].' (E.S.)';  
            
            $pdf->Cell(35,4,'',0,0,'C');
            $pdf->Cell(40,4,$provider_signature,0,0,'L');
            


            $pdf->SetY(320);

            $pdf->Cell(35,4,'',0,0,'C');
            $pdf->Cell(40,4,$row['ProviderSignatureName'].' / '.$row['ProviderSignatureTimeStamp'],0,0,'L');
            //$pdf->Cell(5,4,'',0,0,'C');
            //$pdf->Cell(40,4,$row['ProviderSignatureTimeStamp'],0,0,'L');


        }



 

 


//        if ($row['ParentSignatureName']) { // Signed by School Principal
        // if ($row['ParentSignatureTimeStamp'] != '00/00/0000') { // Signed by School Principal

        //     $pdf->SetY(336);
        //     //$pdf->SetY(180);            
            

        //     if ($row['ParentSignatureName']) {

        //       $parent_signature = $row['ParentSignatureName'].' (E.S.)';  

        //     } else {

        //       $parent_signature = "Student's Guardian (E.S.)";  

        //     }
            


        //     $pdf->Cell(110,4,'',0,0,'C');
        //     $pdf->Cell(40,4,$parent_signature,0,0,'L');
        //     $pdf->Cell(25,4,'',0,0,'C');
        //     $pdf->Cell(20,4,$row['ParentSignatureTimeStamp'],0,0,'L');


        // }



    }  

   // Get RSA 7a Signatures - End
   //==============================       
 
    }
  


    // Output the new PDF
    $pdf->Output();
    //$pdf->Output($GLOBALS['Student_Name'].'-RSA7a.pdf','D');
    //ob_end_flush();    

    $connection->disconnect();

 
  


    /*=================================*/

    function write_header ($pdf, $pageNo, $size, $templateId) {

    /*

      // get the page count
      $pageCount = $pdf->setSourceFile('RSA7A.pdf');
      // iterate through all pages

      $pageNo = $pageNo + 1;

      // import a page
      $templateId = $pdf->importPage($pageNo);
      // get the size of the imported page
      $size = $pdf->getTemplateSize($templateId);
    */
      // create a page (landscape or portrait depending on the imported page size)
      if ($size['w'] > $size['h']) {
          $pdf->AddPage('L', array($size['w'], $size['h']));
      } else {
          $pdf->AddPage('P', array($size['w'], $size['h']));
      }

      // use the imported page
      $pdf->useTemplate($templateId);

      //$pdf->SetFont('Helvetica');
      $pdf->SetFont('Arial','',10);
    

      $pageNo = $pageNo + 1;
    

         // Current Month 
              $pdf->SetXY(125, 53);
              //$pdf->Cell(4,5,date('m', strtotime($ToDate)),0,0,'L');
              $pdf->Cell(4,5,$GLOBALS['ReportMonth'],0,0,'L');



         // Current Year 
              $pdf->SetXY(175, 53);
              //$pdf->Cell(4,5,date('Y', strtotime($ToDate)),0,0,'L');
              $pdf->Cell(4,5,$GLOBALS['ReportYear'],0,0,'L');



         // Student Name  

              $pdf->SetXY(40, 72);
              $pdf->Cell(60,4,$GLOBALS['Student_Name'],0,0,'L');

         
         // Student DOB 

              $pdf->SetXY(147, 72);
              $pdf->Cell(20,4,$GLOBALS['Student_DOB'],0,0,'L');


         // Student NYC ID  


              $pdf->SetXY(40, 81);
              $pdf->Cell(20,4,$GLOBALS['Student_NYC_Id'],0,0,'L');


         // Student District  


              $pdf->SetXY(100, 81);
              $pdf->Cell(25,4,$GLOBALS['Student_District'],0,0,'L');

         // Student Services  
              

              $pdf->SetXY(153, 81);
              $pdf->Cell(40,4,$GLOBALS['Student_Service_Type'],0,0,'L');


         // Student Hourly Rate  

      /*        
              $pdf->SetXY(35, 84);
              $pdf->Cell(15,4,'$50.00',0,0,'L');

      */
         // Student Service Frequency  
              

              $pdf->SetXY(74, 94);
              $pdf->Cell(10,4,$GLOBALS['SessionFrequency'],0,0,'L');


         // Student Service Duration  
              $pdf->SetXY(105, 94);
              $pdf->Cell(10,4,$GLOBALS['SessionLength'],0,0,'L');

         
         // Student Service Group Size  
              $pdf->SetXY(146, 94);
              $pdf->Cell(10,4,$GLOBALS['SessionGrpSize'],0,0,'L');


         // Student Service Language  
              $pdf->SetXY(176, 94);
              $pdf->Cell(20,4,$GLOBALS['Language'],0,0,'L');


         // DOE Plaform Used   
              $pdf->SetXY(186, 103);
              $pdf->Cell(20,4,'Yes',0,0,'L');


         // Student School Name  
              $pdf->SetXY(64, 94);
              // $pdf->Cell(40,4,$GLOBALS['SchoolName'],0,0,'L');
              //$pdf->Cell(40,4,'Tele-Therapy',0,0,'L');


         // Provider Name  
              $pdf->SetXY(42, 122);
              $pdf->Cell(60,4,$GLOBALS['Provider_Name'],0,0,'L');


         // Provider SS#   
              $pdf->SetXY(146, 122);
              $provider_id = substr($GLOBALS['Provider_ID'],-4); 
              $pdf->Cell(20,4,$provider_id,0,0,'L');



         // Provider Address  
              $pdf->SetXY(42, 132);
              $pdf->Cell(120,4,$GLOBALS['Provider_Address'],0,0,'L');

         // Provider Phone  
              $pdf->SetXY(42, 139);
              $pdf->Cell(25,4,$GLOBALS['Provider_Phone'],0,0,'L');

         // Provider E-Mail  
              $pdf->SetXY(146, 139);
              $pdf->Cell(50,4,$GLOBALS['Provider_Email'],0,0,'L');

         // Agency Name  
              $pdf->SetXY(41, 155);
              $pdf->Cell(50,4,$GLOBALS['Agency_Name'],0,0,'L');


         // Agency Tax ID 
              $pdf->SetXY(148, 154);
              $pdf->Cell(30,4,$GLOBALS['Agency_TaxId'],0,0,'L');


         // Agency Address  
              $pdf->SetXY(41, 163);
              $pdf->Cell(150,4,$GLOBALS['Agency_Address'],0,0,'L');


         // Agency Phone  
              $pdf->SetXY(118, 171);
              $pdf->Cell(25,4,$GLOBALS['Agency_Phone'],0,0,'L');


         // Agency E-Mail  
              $pdf->SetXY(40, 171);
              $pdf->Cell(40,4,$GLOBALS['Agency_Email'],0,1,'L');


        return  $pageNo;       

    }


?>
 
