<?php
// MySQLi is built into PHP - no include needed

class TestDataHandler {
    var $connection;
    
    function TestDataHandler() {
        include('db_login.php');
        
        $this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
                if ($this->connection->connect_error) {
                    die("Connection failed: " . $this->connection->connect_error);
                };
        if ($this->connection->connect_error) {
            die("Could not connect to the database: <br />".$this->connection->connect_error ?: $this->connection->error);
        }
        
        $this->connection->set_charset("utf8");
    }
    
    function getUsers() {
        $query = "SELECT id, name, email FROM users";
        $result_temp = $this->connection->query($query);

        if ($result_temp) {

            $result = [];

            while ($row = $result_temp->fetch_assoc()) {

                $result[] = $row;

            }

            $result_temp->free();

        } else {

            $result = false;

        }
        
        if (!$result || $this->connection->error) {
            die("Could not query the database:<br />$query ".$this->connection->error);
        }
        
        return $result;
    }
    
    function addUser($name, $email) {
        $name = $this->connection->real_escape_string($name);
        $email = $this->connection->real_escape_string($email);
        
        $query = "INSERT INTO users (name, email) VALUES ('$name', '$email')";
        $result = $this->connection->query($query);
        
        if (!$result || $this->connection->error) {
            die("Could not insert user:<br />$query ".$this->connection->error);
        }
        
        return $result;
    }
    
    function closeConnection() {
        $this->connection->close();
    }
}
?>
