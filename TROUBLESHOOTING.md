# Troubleshooting Guide: PHP Pear DB to MySQLi Conversion

## Common Error: "Fatal error: Call to undefined method PEAR::set_charset()"

### Problem Description
This error occurs when the conversion script didn't properly convert all PEAR DB references to MySQLi, and the code is still trying to use PEAR methods on a MySQLi connection object.

### Root Cause
The original conversion may have missed some DB::connect() patterns or left residual PEAR references in the code.

### Solution

#### Step 1: Re-run the Improved Converter
Use the updated converter script which has better pattern matching:

```bash
python3 php_pear_to_mysqli_converter.py your_file.php --output your_file_fixed.php
```

#### Step 2: Manual Verification Checklist

After conversion, manually check these key areas in your PHP file:

1. **Connection Setup** - Should look like this:
   ```php
   // ✅ CORRECT (MySQLi)
   $this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
   if ($this->connection->connect_error) {
       die("Connection failed: " . $this->connection->connect_error);
   }
   
   // ❌ WRONG (Still PEAR DB)
   $this->connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
   ```

2. **Character Set** - Should look like this:
   ```php
   // ✅ CORRECT (MySQLi)
   $this->connection->set_charset("utf8");
   
   // ❌ WRONG (PEAR DB)
   $this->connection->query('SET NAMES utf8');
   ```

3. **Query Execution** - Should look like this:
   ```php
   // ✅ CORRECT (MySQLi)
   $result_temp = $this->connection->query($query);
   if ($result_temp) {
       $result = [];
       while ($row = $result_temp->fetch_assoc()) {
           $result[] = $row;
       }
       $result_temp->free();
   } else {
       $result = false;
   }
   
   // ❌ WRONG (PEAR DB)
   $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
   ```

4. **Error Handling** - Should look like this:
   ```php
   // ✅ CORRECT (MySQLi)
   if (!$result || $this->connection->error) {
       die("Query failed: " . $this->connection->error);
   }
   
   // ❌ WRONG (PEAR DB)
   if (DB::isError($result)) {
       die("Query failed: " . DB::errorMessage($result));
   }
   ```

#### Step 3: Search and Replace Remaining Issues

If you still see PEAR references, manually search and replace:

1. **Search for**: `DB::connect`
   **Replace with**: `new mysqli($db_host, $db_username, $db_password, $db_database)`

2. **Search for**: `DB::isError($this->connection)`
   **Replace with**: `$this->connection->connect_error`

3. **Search for**: `DB::isError($result)`
   **Replace with**: `!$result || $this->connection->error`

4. **Search for**: `DB::errorMessage`
   **Replace with**: `$this->connection->error`

5. **Search for**: `->escapeSimple(`
   **Replace with**: `->real_escape_string(`

6. **Search for**: `->disconnect()`
   **Replace with**: `->close()`

#### Step 4: Validate Database Configuration

Ensure your `db_login.php` file has the correct variable names:

```php
<?php
$db_host = "localhost";        // or your database host
$db_username = "your_username";
$db_password = "your_password";
$db_database = "your_database";
?>
```

#### Step 5: Test the Conversion

1. **Syntax Check** (if PHP is available):
   ```bash
   php -l your_file_fixed.php
   ```

2. **Runtime Test**: Create a simple test script:
   ```php
   <?php
   include('your_file_fixed.php');
   
   try {
       $handler = new dataHandler();
       echo "✅ Connection successful!\n";
       $handler->disconnectDB();
   } catch (Exception $e) {
       echo "❌ Error: " . $e->getMessage() . "\n";
   }
   ?>
   ```

## Other Common Issues

### Issue: "Class 'mysqli' not found"
**Solution**: Ensure MySQLi extension is installed:
```bash
# Check if MySQLi is available
php -m | grep mysqli

# Install MySQLi (Ubuntu/Debian)
sudo apt-get install php-mysqli

# Install MySQLi (CentOS/RHEL)
sudo yum install php-mysqli
```

### Issue: Connection parameters not found
**Solution**: Verify your `db_login.php` file exists and has correct variable names.

### Issue: "Too many connections" error
**Solution**: Ensure you're calling `$this->connection->close()` to close connections properly.

## Prevention Tips

1. **Always backup** your original files before conversion
2. **Test thoroughly** in a development environment first
3. **Use version control** to track changes
4. **Consider prepared statements** for better security:
   ```php
   $stmt = $this->connection->prepare("SELECT * FROM users WHERE id = ?");
   $stmt->bind_param("i", $user_id);
   $stmt->execute();
   $result = $stmt->get_result();
   ```

## Getting Help

If you continue to experience issues:

1. Check the conversion summary output for missed patterns
2. Use the `validate_php_syntax.py` script to check for syntax errors
3. Review the generated backup files to compare before/after
4. Consider running the converter multiple times if needed

## Useful Commands

```bash
# Re-run conversion with verbose output
python3 php_pear_to_mysqli_converter.py your_file.php --output fixed_file.php

# Check for remaining PEAR references
grep -n "DB::" your_file_fixed.php
grep -n "PEAR" your_file_fixed.php

# Validate PHP syntax
php -l your_file_fixed.php

# Test database connection
php -r "new mysqli('localhost', 'user', 'pass', 'db'); echo 'MySQLi available';"
```
