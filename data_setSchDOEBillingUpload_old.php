  
<?php
    
	/** Include path **/
	set_include_path(get_include_path() . PATH_SEPARATOR . '../../Classes/');
	
	include 'PHPExcel/IOFactory.php';
	require_once('DB.php');
	include('db_login.php');
	

	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

	//Portal Out Input File  
	//==========================================================
	$out_File = "../uploads/doe_billing_upload.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");
 	

	//Portal Input Input File  
	//==========================================================

	$inputFileName = '../uploads/doe_billing_upload.xls';


	/* Upload New File 
	 =============================================*/
 
 		
   if($ufile != none){ 
      
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	} else {
		
		$err_flag = '1';
		//print "Error uploading extracted file. Please try again!!! "; 
	  
		$linecount = 0;
		echo  "{ success: true, transactions: '{$linecount}'}";

	    Return ; 

	}
   
 

	$inputFileType = 'Excel5';
	//$inputFileName = '../uploads/mandates.xls';

	$objReader = new PHPExcel_Reader_Excel5();
	$objReader->setReadDataOnly(true);
	$objPHPExcel = $objReader->load($inputFileName);

	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	if (sizeof($sheetData) < 2) {

		$err_flag = '1';
		//print "Wrong file was selected. Please try again!!! "; 
	  
		$linecount = 0;
		echo  "{ success: true, transactions: '{$linecount}'}";

	    Return ; 

	}
	
		$cnt = 0;	
		$linecount = 0;	
			
		foreach ($sheetData as &$row_xls) { // foreach - start


			/* Check if Wrog file was selected 
			 ====================================*/


				$cnt++;
		
		
				//=========================
				// Write Header
				//========================	
				
				if ($cnt == 1) {


				/* Check if Wrog file was selected 
			 	====================================*/

			 			if ($row_xls["A"] != 'SRAP FISCAL YR') {

			 				$err_flag = '1';


			 			} else {

			 				/* Write Heading 
                             ==================*/
			 				$write_flag = '1';


			 			}




				}


				if ($cnt != 1) { // cnt !=1 - srart  


					//==============================
					// Get Therapist Id
					//==============================

					$therapist_id = $row_xls["K"];

					//==============================
					// Get Student Id
					//==============================
					
					$student_id = $row_xls["L"];

					//==============================
					// Get Service Code
					//==============================

					$doe_service_code = $row_xls["O"];

					//==============================
					// Get Start Date
					//==============================
					
					$date_doe = $row_xls["P"];
					$date_doe = $date_doe - 1;
					//$timestamp = ($date_doe - 25569) * 86400;
					$timestamp = mktime(0,0,0,1,$date_doe,1900);
					$start_date = date("m/d/Y",$timestamp);
					
					//==============================
					// Get End Date
					//==============================
					
					$date_doe = $row_xls["Q"];
					$date_doe = $date_doe - 1;
					//$timestamp = ($date_doe - 25569) * 86400;
					$timestamp = mktime(0,0,0,1,$date_doe,1900);
					$end_date = date("m/d/Y",$timestamp);

					//==============================
					// Get Invoice Date
					//==============================
					
					$date_doe = $row_xls["V"];
					$date_doe = $date_doe - 1;
					//$timestamp = ($date_doe - 25569) * 86400;
					$timestamp = mktime(0,0,0,1,$date_doe,1900);
					$invoice_date = date("m/d/Y",$timestamp);

					//==============================
					// Get Service Date
					//==============================
					
					$date_doe = $row_xls["W"];
					$date_doe = $date_doe - 1;
					//$timestamp = ($date_doe - 25569) * 86400;
					$timestamp = mktime(0,0,0,1,$date_doe,1900);
					$service_date_wtr = date("m/d/Y",$timestamp);
					$service_date = date("Y-m-d",$timestamp);


			 	/*	
				    echo ' Therapist Id: '.$therapist_id.' Student ID: '.$student_id.' Service Code: '.$doe_service_code.' Service Date: '.$service_date.'</br>'; 			 	
			    */

			
		if (strpos($doe_service_code, '1') !== false) { //Individual

			$query1 = "SELECT 	DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
									DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
									a.Id as ServiceId,
									a.SessionGrpSize as GroupSize 
							FROM 	WeeklyServices a,
									Registrants b,
									SchStudents c,
									SchServiceTypes d,
									SchStudentMandates e 							
							WHERE ServiceDate = '{$service_date}'
							AND a.ScheduleStatusId = '7'
							AND a.RegistrantId = b.Id
							AND a.StudentId = c.Id  
							AND a.ServiceTypeId = d.Id 
							AND DOEServiceTypeIndId = '{$doe_service_code}'
							AND b.ExtId = '{$therapist_id}'
							AND a.MandateId = e.Id
							AND e.SessionGrpSize = 1
							AND c.ExtId = '{$student_id}'";


		} else { // Group


			$query1 = " SELECT 		DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
									DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
									a.Id as ServiceId,
									a.SessionGrpSize as GroupSize 
							FROM 	WeeklyServices a,
									Registrants b,
									SchStudents c,
									SchServiceTypes d,
									SchStudentMandates e  							
							WHERE ServiceDate = '{$service_date}'
							AND a.ScheduleStatusId = '7'
							AND a.RegistrantId = b.Id
							AND a.StudentId = c.Id  
							AND a.ServiceTypeId = d.Id 
							AND DOEServiceTypeGrpId = '{$doe_service_code}'
							AND b.ExtId = '{$therapist_id}'
							AND a.MandateId = e.Id
							AND e.SessionGrpSize > 1
							AND c.ExtId = '{$student_id}'";

		}



					$result1 = $connection->query ($query1);
					
					if ($result1->numRows() == 1) { // numRows() == 1 - Start
				 
				 
				
						if (DB::isError($result1)){
							//die("Could not query the database:<br />$query ".DB::errorMessage($result1));
							die($db->getMessage());
						}

						
						while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {
								$start_time = $row['StartTime'];
								$end_time = $row['EndTime'];
								$service_id = $row['ServiceId'];
								$group_size = $row['GroupSize'];
						}  
						
					
						if (!$group_size) {
							$group_size = 1;
						}
						
					
						$out_Line_str = implode("\t", $row_xls); 
				
						
						$row_xls["P"] = $start_date;
						$row_xls["Q"] = $end_date;
						$row_xls["V"] = $invoice_date;
						$row_xls["W"] = $service_date_wtr;
						
						
						$row_xls["X"] = 'P';
						$row_xls["Y"] =  $group_size;
						$row_xls["Z"] =  $start_time;
						$row_xls["AA"] = $end_time;
						$row_xls["AB"] = 'S';
							
						$linecount++;
						$i++;
						$write_flag = '1';
						
						 
				}	// numRows() == 1 - End




			} // Cnt !=1  - end

			/* Wrong file selected error
			 =====================================*/
			if ($err_flag == '1') {

				//print "Wrong file was selected. Please try again!!! "; 
			  
				$linecount = 0;
				echo  "{ success: true, transactions: '{$linecount}'}";
			    Return ; 

			}


			$out_Line = $row_xls;
			$out_Line_str = implode("\t", $out_Line); 
		
			if ($write_flag == '1') {
		
				$out_Line_str = $out_Line_str."\n";
				//print $out_Line_str;

				fwrite($fh, $out_Line_str);


				$write_flag = '0';
				$start_time = '';
				$end_time = '';

			}	

		} // for foreach - end	

	$connection->disconnect();
	
   
	
	fclose($fh);		 
			  

	//$msg = "";
	//$msg = $msg."Total Transactions To Be Uploaded: ".transactions: '{$linecount}';

	echo  "{ success: true, transactions: '{$linecount}'}";
		//Return ;  

?>