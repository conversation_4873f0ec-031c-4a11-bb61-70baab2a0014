

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_RegistrantCredEmail$$

CREATE PROCEDURE proc_RegistrantCredEmail (IN 	p_registrant_id int, 
												p_sender_name VARCHAR(50),
												p_sender_email VARCHAR(50))  

BEGIN

 

	
    DECLARE v_CredItemId, v_CredItem, v_CredItemType, v_CredItemStatus, v_ComplianceLevelId, v_ExpirationDate VARCHAR(30); 
	DECLARE v_RegistrantCredItemId BIGINT;
	DECLARE v_ExpDays INT;
	DECLARE v_Message TEXT;  
	DECLARE done INT DEFAULT 0;
	
	 
	
	/*============================================*/
	
	DECLARE cur1 CURSOR FOR
      SELECT    a.Id as RegistrantCredItemId, 
      			CredItemId , 		
				CredItemDesc, 
				CredItemType , 
				CredItemStatus ,  
				ComplianceLevelId , 
				COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
          		DATEDIFF( ExpirationDate, CURDATE( )  ) as ExpDays
	FROM RegistrantCredItems a, CredentialingItems b
		WHERE a.CredItemId = b.Id 
		AND RegistrantId = p_registrant_id
		/*AND a.ComplianceLevelId  = '1' */
		AND ((a.StatusId =0) && (b.CredItemCategory = 2)) = FALSE					 
		;
       
	declare continue handler for not found set done := true;   


   /* Temp table to  store Registrant's Cred. to be used in Messages genration 
     ========================================================================*/
    create temporary table tmp1 

        (
            RegistrantCredItemId BIGINT
        );


	create temporary table tmp engine=memory

 	SELECT  Id as RegistrantId, 
			FirstName, 
            LastName,
            MiddleInitial,
            Email,
            CONCAT( 'Your Credentialing Items Are Missing/Expired (', trim(CompanyName), ')' ) AS Subject,
            CONCAT( 'Dear ', trim(FirstName), ' ' , trim(LastName),  ',\n',  '\n' , 'The Following Credentialing Items Need Your IMMEDIATE ATTENTION!!!:') AS Message
			
 
		FROM Registrants, Company 

		WHERE Id = p_registrant_id ;
		
		 
		Set v_Message = '';
		 
		 
		 
	OPEN cur1;
	
	read_loop: LOOP
 
       FETCH cur1 INTO 	v_RegistrantCredItemId,
       					v_CredItemId,
       					v_CredItem,
       					v_CredItemType,
       					v_CredItemStatus,
       					v_ComplianceLevelId,
       					v_ExpirationDate, 
       					v_ExpDays;
   
		IF done THEN
			LEAVE read_loop;
		END IF;
		

     
		/* Item Will Expire in 30 Days  */
		/*==========================================*/

		IF ((v_ExpDays < 31) and (v_CredItemStatus = 2)) THEN  
			Select   CONCAT( v_Message , ' Cred. Item: ==> ', v_CredItem , ';  Status: ==> Item Will Expire on ', v_ExpirationDate, "\r\n") into v_Message; 
 		
			INSERT INTO tmp1
			VALUES(v_RegistrantCredItemId);

 		END IF;
		
		/* Item is Missing  */
		/*==========================================*/

		IF (v_CredItemStatus = 1) THEN  
				Select   CONCAT( v_Message  , " Cred. Item: ==> ", v_CredItem , ";  Status: ==> Item Not Received\r\n") into v_Message;

			INSERT INTO tmp1
			VALUES(v_RegistrantCredItemId);

		END IF;

		/* Item is Expired  */
		/*==========================================*/
	
		IF (v_CredItemStatus = 3) THEN  
				Select   CONCAT( v_Message  , ' Cred. Item: ==> ', v_CredItem , ';  Status: ==> Item Expired  on ', v_ExpirationDate, "\r\n") into v_Message;

			INSERT INTO tmp1
			VALUES(v_RegistrantCredItemId);

		END IF;

	
    END LOOP;
    CLOSE cur1; 


	Select   CONCAT( "\r\n" , "\r\n" ,v_Message, "\r\n") into v_Message;
	Select   CONCAT( v_Message,p_sender_name, "\r\n") into v_Message;
	Select   CONCAT( v_Message,p_sender_email, "\r\n") into v_Message;
	
	
 	SELECT  RegistrantId,
			FirstName, 
            LastName,
            MiddleInitial,
            Email,
            Subject, 
            CONCAT(Message, '\n' , '\n' , v_Message) as Message,
            group_concat( RegistrantCredItemId SEPARATOR ', ' ) as RegistrantCredItemIdStr
	
	from tmp, tmp1;
	
	drop temporary table if exists tmp;
	drop temporary table if exists tmp1;
	 
	
END $$

DELIMITER ;	