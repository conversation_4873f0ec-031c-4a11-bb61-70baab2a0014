<?php 


	require "ewDataHandler.php";   
	  
	$rcr_transaction = new dataHandler();  

	$ClientId = $_POST['ClientId'];
	$UserId = $_POST['UserId'];

	$StartDate  = mktime(0, 0, 0, date("m")  , date("d"), date("Y"));
	$PerStartDateMon = strtotime('Monday', $StartDate);
	$PerStartDateTue = strtotime('Tuesday', $StartDate);
	$PerEndDateSun = strtotime('Sunday', $PerStartDateTue);

 
	$result = $rcr_transaction->setGenerateNewInvoicesData(	$ClientId, 
															date('Y-m-d', $PerStartDateMon),		
															date('Y-m-d', $PerStartDateTue),
															date('Y-m-d', $PerEndDateSun),
															$UserId) ;

	$rcr_transaction->disconnectDB (); 
	 
	
	//echo  '{ success: true };

	echo $result;



?>
