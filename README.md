# PHP Pear DB to MySQLi Converter

A Python script that automatically converts PHP files from using the deprecated Pear DB library to native MySQLi.

## Features

- **Automatic conversion** of common Pear DB patterns to MySQLi equivalents
- **Backup creation** of original files before conversion
- **Comprehensive error handling** conversion
- **Detailed conversion summary** showing what was changed
- **Support for multiple conversion patterns** including:
  - Database connections
  - Query execution methods
  - Result set handling
  - Error checking
  - String escaping
  - Connection management

## Installation

No installation required! Just ensure you have Python 3.6+ installed.

```bash
# Check Python version
python3 --version
```

## Usage

### Basic Usage

```bash
python3 php_pear_to_mysqli_converter.py your_file.php
```

This will:
1. Create a backup file (`your_file_pear_backup.php`)
2. Generate a converted file (`your_file_mysqli.php`)
3. Display a summary of all conversions made

### Command Line Options

```bash
# Convert without creating backup
python3 php_pear_to_mysqli_converter.py your_file.php --no-backup

# Specify custom output file
python3 php_pear_to_mysqli_converter.py your_file.php --output converted_file.php

# Show help
python3 php_pear_to_mysqli_converter.py --help
```

## Conversion Examples

### Before (Pear DB)
```php
<?php
require_once('DB.php');

class DataHandler {
    var $connection;
    
    function DataHandler() {
        $this->connection = DB::connect("mysqli://$user:$pass@$host/$db");
        if (DB::isError($this->connection)){
            die("Connection failed: ".DB::errorMessage($this->connection));
        }
        $this->connection->query('SET NAMES utf8');
    }
    
    function getUsers() {
        $query = "SELECT * FROM users";
        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
        
        if (DB::isError($result)){
            die("Query failed: ".DB::errorMessage($result));
        }
        return $result;
    }
    
    function addUser($name) {
        $name = $this->connection->escapeSimple($name);
        $query = "INSERT INTO users (name) VALUES ('$name')";
        $result = $this->connection->query($query);
        
        if (DB::isError($result)){
            die("Insert failed: ".DB::errorMessage($result));
        }
    }
    
    function close() {
        $this->connection->disconnect();
    }
}
?>
```

### After (MySQLi)
```php
<?php
// MySQLi is built into PHP - no include needed

class DataHandler {
    var $connection;
    
    function DataHandler() {
        $this->connection = new mysqli($host, $user, $pass, $db);
        if ($this->connection->connect_error) {
            die("Connection failed: " . $this->connection->connect_error);
        }
        $this->connection->set_charset("utf8");
    }
    
    function getUsers() {
        $query = "SELECT * FROM users";
        $result_temp = $this->connection->query($query);
        if ($result_temp) {
            $result = [];
            while ($row = $result_temp->fetch_assoc()) {
                $result[] = $row;
            }
            $result_temp->free();
        } else {
            $result = false;
        }
        
        if (!$result || $this->connection->error) {
            die("Query failed: ".$this->connection->error);
        }
        return $result;
    }
    
    function addUser($name) {
        $name = $this->connection->real_escape_string($name);
        $query = "INSERT INTO users (name) VALUES ('$name')";
        $result = $this->connection->query($query);
        
        if (!$result || $this->connection->error) {
            die("Insert failed: ".$this->connection->error);
        }
    }
    
    function close() {
        $this->connection->close();
    }
}
?>
```

## Supported Conversions

| Pear DB Pattern | MySQLi Equivalent |
|----------------|-------------------|
| `require_once('DB.php')` | `// MySQLi is built into PHP - no include needed` |
| `DB::connect("mysqli://...")` | `new mysqli($host, $user, $pass, $db)` |
| `$conn->getAll($query, DB_FETCHMODE_ASSOC)` | `$result->fetch_assoc()` loop |
| `DB::isError($result)` | `!$result \|\| $conn->error` |
| `DB::errorMessage($result)` | `$conn->error` |
| `$conn->escapeSimple($str)` | `$conn->real_escape_string($str)` |
| `$conn->disconnect()` | `$conn->close()` |
| `$conn->query('SET NAMES utf8')` | `$conn->set_charset("utf8")` |

## Important Notes

1. **Review converted code**: Always review the converted code before using in production
2. **Test thoroughly**: Test all database operations after conversion
3. **Database credentials**: Ensure your `db_login.php` file uses the correct variable names (`$db_host`, `$db_username`, `$db_password`, `$db_database`)
4. **Error handling**: The converter updates error handling, but you may want to implement more sophisticated error handling
5. **Performance**: MySQLi prepared statements are recommended for better security and performance

## Limitations

- Does not convert to prepared statements (recommended for security)
- May require manual adjustment for complex custom Pear DB usage
- Formatting may need cleanup in some cases
- Does not handle all possible Pear DB edge cases

## Contributing

Feel free to submit issues or pull requests to improve the converter.

## License

This script is provided as-is for educational and utility purposes.
