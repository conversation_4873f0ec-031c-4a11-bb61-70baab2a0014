<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$RegistrantId = $_POST['RegistrantId'];
$HighPriority = $_POST['HighPriority'];
$MessageTypeId = $_POST['MessageTypeId'];
$Msg = $_POST['Msg'];
$UserId = $_POST['UserId'];

 
$result = $rcr_transaction->setRegistrantMessages($RegistrantId,
												$HighPriority,
												$MessageTypeId,
												$Msg,
												$UserId ); 
 
$rcr_transaction->disconnectDB (); 

echo $result;

?>
