<?php 


	require "ewDataHandler.php";   
	  
	$rcr_transaction = new dataHandler();  

	$InvoiceNumber = $_POST['InvoiceNumber'];
	$LineNumber = $_POST['LineNumber'];
	$Units = $_POST['Units'];
	$UnitsOrig = $_POST['UnitsOrig'];
	$BillRate = $_POST['BillRate'];
	$BillAmount = $_POST['BillAmount'];
	$BillAmountOrig = $_POST['BillAmountOrig'];
	$ScheduleId = $_POST['ScheduleId'];
	$ClientUnitId = $_POST['ClientUnitId'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	$UserId = $_POST['UserId'];


 
	$result = $rcr_transaction->setAdjustClientInvoiceLineItem(	$InvoiceNumber,
																$LineNumber,
																$Units,
																$UnitsOrig,
																$BillRate,
																$BillAmount,
																$BillAmountOrig,
																$ScheduleId,
																$ClientUnitId,
																$ServiceTypeId,
																$UserId 
															  ) ;

	$rcr_transaction->disconnectDB (); 
	 
	
	//echo  '{ success: true };

	echo $result;



?>
