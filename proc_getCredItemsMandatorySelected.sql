

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getCredItemsMandatorySelected$$


CREATE  PROCEDURE proc_getCredItemsMandatorySelected (IN p_registrant_type_id INT )
											  
BEGIN


	
	/*=================================================================================*/
	/* Get All Unselected Mandatory Credentialing Items for a gived Registrant Type   */ 
	/*=================================================================================*/
	
	create temporary table tmp engine=memory

		SELECT a.Id as id, 
		a.CredItemDesc,
		'0' as CondFL
		FROM  CredentialingItems a
		  
		WHERE EXISTS (SELECT 1 FROM CredentialingItemsMandatory b
						WHERE b.RegistrantTypeId = p_registrant_type_id
                        AND a.id = b.MandatoryCredItemId
						AND CondFL = 0)  						
	UNION
		SELECT  c.Id as id, 
		c.ConditionalItemDesc as CredItemDesc,
		'1' as CondFL
		FROM  CredentialingItemsConditionalHeader c
		WHERE  CredItemCategory = '1'
		AND EXISTS (SELECT 1 FROM CredentialingItemsMandatory b
						WHERE b.RegistrantTypeId = p_registrant_type_id
                        AND c.id = b.MandatoryCredItemId
						AND CondFL = 1)  						
		
		
		; 	
		
	
	SELECT * FROM tmp a
	ORDER BY a.CredItemDesc ;	
	 
	
	
	drop temporary table if exists tmp;
	
 
END$$

DELIMITER ;	
