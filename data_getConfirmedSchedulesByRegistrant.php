<?php 

require "ewDataHandler.php"; 

$RegistrantId = $_GET['RegistrantId'];
$StartDate = $_GET['StartDate'];
$EndDate = $_GET['EndDate'];

$rcr_transaction = new dataHandler(); 


$result = $rcr_transaction->getConfirmedSchedulesByRegistrant(	$RegistrantId, 
																$StartDate,
																$EndDate  );

$rcr_transaction->disconnectDB (); 

echo  "{ success: true,  data: ".json_encode($result)."}";
  

?>
