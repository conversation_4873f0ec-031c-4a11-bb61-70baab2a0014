

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setClientAssignmentShifts$$


CREATE  PROCEDURE proc_setClientAssignmentShifts(IN p_assign_id INT,
												    p_service_date DATE,
														p_shift_id INT,
													p_user_id INT
											)
BEGIN


   
   DECLARE v_PayrollWeek DATE;
   DECLARE v_Week_Day CHAR(3);
   
    
 
	/*===========================*/
    /* Get Payroll Week Info*/
	/*===========================*/
   
	
   	SET v_PayrollWeek = LAST_DAY(p_service_date);

	/*===========================*/
    /* Get Week Date Name for the Service Date Info*/
	/*===========================*/

	SELECT SUBSTRING((DAYNAME(p_service_date) ),1,3) into v_Week_Day;
	
	
	/*================================*/
    /* Add New Shift  Info*/
	/*===============================*/
	  
	INSERT INTO WeeklyServices
		(
			PayrollWeek,
			ClientId,
			ClientUnitId,
			ServiceTypeId,
			ScheduleStatusId,
			ServiceDate,	 
			StartTime, 
			EndTime, 		
			TotalHours,
			LunchHour,
			WeekDay ,
			ShiftId,
			RegistrantId, 
			RegistrantTypeId,
			AssignmentId,
			UserId,
			TransDate 
		)	
		 
	SELECT 	v_PayrollWeek,
			a.ClientId,
			a.ClientUnitId,
			a.ServiceTypeId,
			'7',
			p_service_date,
			StartTime, 
			EndTime, 
			TotalHours,
			LunchHour,
			v_Week_Day,
			p_shift_id,
			a.RegistrantId,
			b.TypeId,
			p_assign_id,
			p_user_id,
			NOW()
	FROM ClientAssignments a, Registrants b, ClientShifts c 
	WHERE a.Id = p_assign_id
	AND a.RegistrantId = b.Id
	AND a.ClientId = c.ClientId
	AND ShiftId = p_shift_id;												
	
	
 
END$$

DELIMITER ;	
