<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$CredItemId = $_GET['CredItemId'];
	$RegistrantTypeId = $_GET['RegistrantTypeId'];
	$ClientId = $_GET['ClientId'];
	
	if (!$CredItemId) {
		$CredItemId = '%%';
	}
	
	if (!$RegistrantTypeId) {
		$RegistrantTypeId = '%%';
	}

	if (!$ClientId) {
		$ClientId = '%%';
	}


	$result = $rcr_transaction->getRegistrantsNonComplCredItemsList($CredItemId, 
																	$RegistrantTypeId,
																	$ClientId);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";

?>
