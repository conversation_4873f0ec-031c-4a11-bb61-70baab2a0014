<?php 
	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$CredItemTransId = $_POST['CredItemTransId'];
	$StatusId = $_POST['StatusId'];
	$UserId = $_POST['UserId'];

	$result = $rcr_transaction->setRegistrantCredItemsStatus($CredItemTransId,
															 $StatusId, 
															 $UserId);
	
	
	//==============================
	// Add Cred. Item Message
	//==============================
	$HighPriority = '0';
	$Msg = ($StatusId == 0) ? 'Cred. Item was Turned Off' : 'Cred. Item was Turned On';
	
	$result1 = $rcr_transaction->setRegistrantCredItemMsgs(	$CredItemTransId,
															$Msg,
															$HighPriority,
															$UserId ); 


	
	
	$rcr_transaction->disconnectDB (); 

	//echo  "{ success: true,  data: ".json_encode($result)."}";
  
	echo $result;

?>
