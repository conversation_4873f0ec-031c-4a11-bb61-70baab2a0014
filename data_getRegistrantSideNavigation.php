<?php 
 
	require "ewDataHandler.php"; 

	$rcr_transaction = new dataHandler(); 

	$RegistrantView = $_GET['RegistrantView'];

	if ($RegistrantView == '1') { 

		$result = $rcr_transaction->getRegistrantWebNavigation();
	} else {
	
		$result = $rcr_transaction->getRegistrantSideNavigation();

	}
		
		
	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
  

?>
