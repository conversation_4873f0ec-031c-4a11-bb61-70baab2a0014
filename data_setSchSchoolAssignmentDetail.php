<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$AssignmentId = $_POST['AssignmentId'];
	$AssignmentDetailId = $_POST['AssignmentDetailId'];
	$WeekDayId = $_POST['WeekDayId'];
	$StartTime = $_POST['StartTime'];	
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$RegistrantId = $_POST['RegistrantId'];
	$ApplyAllDays = $_POST['ApplyAllDays'];
	$UserId = $_POST['UserId'];


	if ($ApplyAllDays == '1') {

		$result = $rcr_transaction->setSchSchoolAssignmentDetailAll($AssignmentId,	
																	$StartTime,
																	$EndTime,
																	$TotalHours,
																	$RegistrantId,
																	$UserId ); 


	} else {

		$result = $rcr_transaction->setSchSchoolAssignmentDetail(   $AssignmentId,
																	$AssignmentDetailId,	
																	$WeekDayId,
																	$StartTime,
																	$EndTime,
																	$TotalHours,
																	$RegistrantId,
																	$UserId ); 

	}



	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
