<?php


	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php'); 

 	
	$logo_image = '../images/Gotham_logo.png';
	
	$GLOBALS['Logo'] = $logo_image;
	
	 
class PDF extends FPDF
{

	function PDF($orientation='P',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


	//Page header
	function Header()
	{
		//Logo
		$this->Image($GLOBALS['Logo'],10,15,60);
		//$this->Ln(60);
		//Arial bold 15
		
		//Move to the right
		//$this->Cell(80);
		//$this->Cell(50,4,'Gotham Companies',1,1,'C');
 
		//$this->Cell(140,4,$GLOBALS['Logo'],1,0,'C');
  
		$this->SetFont('Arial','B',16);
		//$this->Ln(28);

		//$this->Cell(5);
 		$this->Cell(0,4,'Employment Application',0,1,'C');
		

	/*		

		$this->Ln(15);
		//Title
		$this->Cell(30,4,'Vendor:',1,0,'L');
		$this->Cell(60,4,$GLOBALS['Company'],1,0,'C');
		$this->Cell(10);
		$this->Cell(30,4,'Facility:',1,0,'L');
		$this->Cell(120,4,$GLOBALS['ClientName'],1,1,'C');
		
		$this->Ln(2);
		$this->Cell(30,4,'W/E Date:',1,0,'L');
		$this->Cell(60,4,$GLOBALS['PayrollWeekFrm'],1,0,'C');	
		$this->Cell(10);
		$this->Cell(30,4,'Timestamp:',1,0,'L');
		$this->Cell(40,4,date('m/d/Y g:i A'),1,0,'C');
 
		$this->Ln(10);
		//Set Table Header
		$this->SetFont('Times','B',10);
		$this->Cell(8,4,'#',1,0,'C');
		$this->Cell(20,4,'Service Date',1,0,'C');
		$this->Cell(25,4,'Service Type',1,0,'C');
		$this->Cell(25,4,'Week Day',1,0,'C');
		$this->Cell(25,4,'Start Time',1,0,'C');
		$this->Cell(27,4,'End Time',1,0,'C');
		$this->Cell(15,4,'Tot Hrs',1,0,'C');
		$this->Cell(40,4,'Status',1,0,'C');
		$this->Cell(70,4,'Employee Name',1,1,'C');
		$this->Ln(1);
	*/	
 
		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-20);
		

		/* New York */ 
		$this->SetFont('Times','B',9);
		$this->Cell(20,4,'',0,0,'L');
		$this->Cell(20,4,'New York: ',0,0,'L');
		$this->SetFont('Times','',9);
		$this->Cell(62,4,'75 Mainden Lane, New York, NY 10038',0,0,'L');
		$this->Cell(15,4,'* Phone:',0,0,'L');
		$this->SetFont('Times','B',9);
		$this->Cell(20,4,'************',0,0,'L');

		$this->SetFont('Times','',9);
		$this->Cell(40,4,' * Fax ************',0,1,'L');

		/* Bronx */
		$this->SetFont('Times','B',9);
		$this->Cell(20,4,'',0,0,'L');
		$this->Cell(20,4,'Bronx:',0,0,'L');
		$this->SetFont('Times','',9);
		$this->Cell(62,4,'2488 Grand Concourse, Bronx, NY 10458',0,0,'L');
		$this->Cell(15,5,'* Phone:',0,0,'L');
		$this->SetFont('Times','B',9);
		$this->Cell(20,4,'************',0,0,'L');

		$this->SetFont('Times','',9);
		$this->Cell(40,4,' * Fax ************',0,1,'L');
		$this->SetFont('Times'	,'B',9);

		$this->Cell(0,4,'www.gothamcompanies.com',0,1,'C');


		//Arial italic 8
		$this->SetFont('Times','I',9);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}
 
	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	//$pdf->SetFont('Arial','',8);
	
	//+++++++++++++++++++++++++++++++++++++++++++++++++
  
/* Page 1 */
/*========*/

	/*  Line #1 : Name/Mobile Phone  */	
		$pdf->Line(10, 35, 200, 35);	
		$pdf->Line(10, 35, 10, 43);	
		$pdf->Line(140, 35, 140, 43);	
		$pdf->Line(10, 43, 200, 43);	
		$pdf->Line(200, 35, 200, 43);	


		$pdf->Ln(22);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);
		$pdf->Cell(22,7,'Last Name:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(40,7,'Jones',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(25,7,'First Name:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(40,7,'Mary',0,0,'L');
 
		$pdf->SetFont('Arial',	'B',10);
		$pdf->Cell(30,7,'Mobile Phone:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(50,7,'(*************',0,0,'L');


	/*  Line #2 : Address/Mobile Phone  */	
		/*$pdf->Line(10, 45, 270, 45);	*/	
		$pdf->Line(10, 43, 10, 50);	
		$pdf->Line(140, 43, 140, 50);	
		$pdf->Line(10, 50, 200, 50);	
		$pdf->Line(200, 43, 200, 50);	

		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);
		$pdf->Cell(22,7,'Street:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(40,7,'125 Main Street',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(8,7,'Apt:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(57,7,'3B',0,0,'L');

		$pdf->SetFont('Arial',	'B',10);
		$pdf->Cell(30,7,'Home Phone:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(50,7,'(*************',0,0,'L');


	/*  Line #3 : City/State/Zip  */	
		/*$pdf->Line(10, 55, 270, 55);	*/
		$pdf->Line(10, 50, 10, 57);	
		$pdf->Line(140, 50, 140, 57);	
		$pdf->Line(10, 57, 200, 57);	
		$pdf->Line(200, 50, 200, 57);	


		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);
		$pdf->Cell(22,7,'City:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(40,7,'Brooklyn',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(12,7,'State:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(14,7,'NY',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(17,7,'Zip Code:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(30,7,'10012',0,0,'L');	


	/*  Line #4 : E-mail/SS#  */	
		$pdf->Line(10, 57, 10, 64);		
		$pdf->Line(140, 57, 140, 64);	
		$pdf->Line(10, 64, 200, 64);	
		$pdf->Line(200, 57, 200, 64);	

		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);
		$pdf->Cell(22,7,'E-Mail:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(102,7,'<EMAIL>',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(14,7,'SS#: ',0,0,'R');
		$pdf->SetFont('Arial','U',10);
		$pdf->Cell(50,7,'XXX-XX-7974',0,0,'L');


	/*  Line #5 : Status/License#Exp date  */	
		$pdf->Line(10, 64, 10, 71);		
		$pdf->Line(140, 64, 140, 71);	
		$pdf->Line(10, 71, 200, 71);	
		$pdf->Line(200, 64, 200, 71);	

		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);
		$pdf->Cell(22,7,'Status:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(20,7,'RN',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(20,7,'License#:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(65,7,'23929932',0,0,'L');
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(20,7,'Exp.Date:',0,0,'L');
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(50,7,'10/23/2018',0,0,'L');


	/*  Line #5 - #8 : Certifications */	
		$pdf->Line(10, 71, 10, 108);		
		/*$pdf->Line(140, 71, 140, 88);*/	
		$pdf->Line(10, 108, 200, 108);	
		$pdf->Line(200, 71, 200, 108);	

		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);	
		$pdf->Cell(22,7,'Certifications:',0,1,'L');
		$pdf->Ln(2);
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(5);
		$pdf->Cell(3,3,'X',1,0,'C');
		$pdf->Cell(12,3,'BLS',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'01/14/2019',0,0,'L');

		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'CCRN',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,0,'L');


		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'CEN',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,1,'L');
		$pdf->Cell(20,3,'',0,1,'L');

		$pdf->Cell(5);
		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'Chemo',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,0,'L');

		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'CNOR',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,0,'L');


		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'CNRN',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,1,'L');
		$pdf->Cell(20,3,'',0,1,'L');

		$pdf->Cell(5);
		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'FHM',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,0,'L');

		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'NRP',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,0,'L');


		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'PALS',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,1,'L');	
		$pdf->Cell(20,3,'',0,1,'L');

		$pdf->Cell(5);
		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'RNC',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,0,'L');

		$pdf->Cell(3,3,' ',1,0,'C');
		$pdf->Cell(12,3,'TNCC',0,0,'L');
		$pdf->SetFont('Arial','B',9);
		$pdf->Cell(16,3,'Exp. Date:',0,0,'L');
		$pdf->SetFont('Arial','',9);		
		$pdf->Cell(20,3,'',0,1,'L');
	/*	$pdf->Cell(20,3,'',0,1,'L');*/


	/*  Line #9 - #12 : Education */	
		$pdf->Line(10, 115, 200, 115);
		$pdf->Line(10, 115, 10, 158);		
		$pdf->Line(10, 158, 200, 158);	
		$pdf->Line(200, 115, 200, 158);	


		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);	
		$pdf->Cell(22,7,'Education:',0,1,'L');
		$pdf->Ln(3);
		$pdf->SetFont('Arial','',9);

			
		/* College/Nursing School */
		$pdf->Line(10, 125, 200, 125);
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(3)	;
		$pdf->Cell(60,7,'Brooklyn College',0,0,'L');
		$pdf->Cell(80,7,'Brooklyn, NY',0,0,'L');
		$pdf->Cell(40,7,'BA',0,0,'L');
		


		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(7);

		$pdf->Cell(3);	
		$pdf->Cell(60,3,'College/Nursing School',0,0,'L');
		$pdf->Cell(80,3,'Location',0,0,'L');
		$pdf->Cell(40,3,'Diploma/Degree Received',0,0,'L');


		/* Graduate  School */
		$pdf->Ln(5);
		$pdf->SetFont('Arial','',9);

		$pdf->Line(10, 138, 200, 138);
		$pdf->Cell(3)	;
		$pdf->Cell(60,7,'NYU',0,0,'L');
		$pdf->Cell(80,7,'New York, NY',0,0,'L');
		$pdf->Cell(40,7,'MSN',0,0,'L');
		


		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(60,3,'Graduate School',0,0,'L');
		$pdf->Cell(80,3,'Location',0,0,'L');
		$pdf->Cell(40,3,'Diploma/Degree Received',0,0,'L');

		/* Other  School */
		$pdf->Ln(5);
		$pdf->SetFont('Arial','',9);

		$pdf->Line(10, 151, 200, 151);
		$pdf->Cell(3)	;
		$pdf->Cell(60,7,'',0,0,'L');
		$pdf->Cell(80,7,'',0,0,'L');
		$pdf->Cell(40,7,'',0,0,'L');
		


		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(60,3,'Other School, If Applicable',0,0,'L');
		$pdf->Cell(80,3,'Location',0,0,'L');
		$pdf->Cell(40,3,'Diploma/Degree Received',0,0,'L');

	
	/*  Employment Profile */	

		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);	
		$pdf->Cell(92,7,'Employment Profile: Complete for ANY position for past 7 years. Explain gaps in employment',0,1,'L');
		$pdf->Ln(2);

	/*  Employment # 1 */	
		$pdf->Line(10, 165, 200, 165);
		$pdf->Line(10, 165, 10, 201);		
		$pdf->Line(10, 201, 200, 201);	
		$pdf->Line(200, 165, 200, 201);	


	/*  Employer 1 */	
		
		$pdf->Line(10, 175, 200, 175);

		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(57,7,'Brooklyn Hospital',0,0,'L');
		$pdf->Cell(57,7,'121 DeKalb Avenue',0,0,'L');
		$pdf->Cell(34,7,'Brooklyn',0,0,'L');
		$pdf->Cell(14,7,'NY',0,0,'L');
		$pdf->Cell(20,7,'10034',0,0,'L');
		

		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(58,3,'Employer',0,0,'L');
		$pdf->Cell(57,	3,'Address',0,0,'L');
		$pdf->Cell(33,3,'City',0,0,'L');
		$pdf->Cell(14,3,'State',0,0,'L');
		$pdf->Cell(20,3,'Zip Code',0,1,'L');


	/*  Position 1 */	
		$pdf->Line(10, 185, 200, 185);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Nurse Supervisor',0,0,'L');
		$pdf->Cell(54,7,'ER, Med Surg',0,0,'L');
		$pdf->Cell(20,7,'10/11/2013',0,0,'L');
		$pdf->Cell(20,7,'04/01/2015',0,0,'L');
		$pdf->Cell(40,7,'Switched Jobs',0,0,'L');


		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Position',0,0,'L');
		$pdf->Cell(54,	3,'Specialty Areas',0,0,'L');
		$pdf->Cell(20,3,'Began',0,0,'L');
		$pdf->Cell(20,3,'Ended',0,0,'L');
		$pdf->Cell(20,3,'Reason for Leaving',0,1,'L');


	/*  Supervisor 1 */	
		$pdf->Line(10, 195, 200, 195);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Maria Torres - RN, BSN',0,0,'L');
		$pdf->Cell(30,7,'(*************',0,0,'L');
		$pdf->Cell(25,7,'Per Diem',0,0,'L');
		$pdf->Cell(70,7,'Specilalty Staffing',0,0,'L');



		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(7);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Supervisor Name/Position',0,0,'L');
		$pdf->Cell(30,	3,'Phone',0,0,'L');
		$pdf->Cell(25,	3,'Assign. Type',0,0,'L');
		$pdf->Cell(40,	3,'Agency Name',0,1,'L');

		$pdf->Ln(2);

	/*  Employment # 2 */	
	
		$pdf->Line(10, 203, 200, 203);
		$pdf->Line(10, 203, 10, 237);		
		$pdf->Line(10, 237, 200, 237);	
		$pdf->Line(200, 203, 200, 237);	
	 
		$pdf->Ln(2);

	/*  Employer 2 */	
		
		$pdf->Line(10, 211, 200, 211);

		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(57,7,'Brooklyn Hospital',0,0,'L');
		$pdf->Cell(57,7,'121 DeKalb Avenue',0,0,'L');
		$pdf->Cell(34,7,'Brooklyn',0,0,'L');
		$pdf->Cell(14,7,'NY',0,0,'L');
		$pdf->Cell(20,7,'10034',0,0,'L');
		

		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(58,3,'Employer',0,0,'L');
		$pdf->Cell(57,	3,'Address',0,0,'L');
		$pdf->Cell(33,3,'City',0,0,'L');
		$pdf->Cell(14,3,'State',0,0,'L');
		$pdf->Cell(20,3,'Zip Code',0,1,'L');

	/*  Position 2 */	
		$pdf->Line(10, 222, 200, 222);
		
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Nurse Supervisor',0,0,'L');
		$pdf->Cell(54,7,'ER, Med Surg',0,0,'L');
		$pdf->Cell(20,7,'10/11/2013',0,0,'L');
		$pdf->Cell(20,7,'04/01/2015',0,0,'L');
		$pdf->Cell(40,7,'Switched Jobs',0,0,'L');


		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Position',0,0,'L');
		$pdf->Cell(54,	3,'Specialty Areas',0,0,'L');
		$pdf->Cell(20,3,'Began',0,0,'L');
		$pdf->Cell(20,3,'Ended',0,0,'L');
		$pdf->Cell(20,3,'Reason for Leaving',0,1,'L');

	/*  Supervisor 2 */	
		$pdf->Line(10, 232, 200, 232);
		
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Maria Torres - RN, BSN',0,0,'L');
		$pdf->Cell(30,7,'(*************',0,0,'L');
		$pdf->Cell(25,7,'Per Diem',0,0,'L');
		$pdf->Cell(70,7,'Specilalty Staffing',0,0,'L');



		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(7);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Supervisor Name/Position',0,0,'L');
		$pdf->Cell(30,	3,'Phone',0,0,'L');
		$pdf->Cell(25,	3,'Assign. Type',0,0,'L');
		$pdf->Cell(40,	3,'Agency Name',0,1,'L');

		$pdf->Ln(2);

	/*  Employment # 3 */	
	
		$pdf->Line(10, 239, 200, 239);
		$pdf->Line(10, 239, 10, 273);		
		$pdf->Line(10, 273, 200, 273);	
		$pdf->Line(200, 239, 200, 273);	

		$pdf->Ln(2);

	/*  Employer 3 */	
		
		$pdf->Line(10, 250, 200, 250);

		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(57,7,'Brooklyn Hospital',0,0,'L');
		$pdf->Cell(57,7,'121 DeKalb Avenue',0,0,'L');
		$pdf->Cell(34,7,'Brooklyn',0,0,'L');
		$pdf->Cell(14,7,'NY',0,0,'L');
		$pdf->Cell(20,7,'10034',0,0,'L');
		

		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(58,3,'Employer',0,0,'L');
		$pdf->Cell(57,	3,'Address',0,0,'L');
		$pdf->Cell(33,3,'City',0,0,'L');
		$pdf->Cell(14,3,'State',0,0,'L');
		$pdf->Cell(20,3,'Zip Code',0,1,'L');

	/*  Position 3 */	
		$pdf->Line(10, 261, 200, 261);
		
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Nurse Supervisor',0,0,'L');
		$pdf->Cell(54,7,'ER, Med Surg',0,0,'L');
		$pdf->Cell(20,7,'10/11/2013',0,0,'L');
		$pdf->Cell(20,7,'04/01/2015',0,0,'L');
		$pdf->Cell(40,7,'Switched Jobs',0,0,'L');


		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Position',0,0,'L');
		$pdf->Cell(54,	3,'Specialty Areas',0,0,'L');
		$pdf->Cell(20,3,'Began',0,0,'L');
		$pdf->Cell(20,3,'Ended',0,0,'L');
		$pdf->Cell(20,3,'Reason for Leaving',0,1,'L');

	/*  Supervisor 3 */	
		$pdf->Line(10, 271, 200, 271);
		
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Maria Torres - RN, BSN',0,0,'L');
		$pdf->Cell(30,7,'(*************',0,0,'L');
		$pdf->Cell(25,7,'Per Diem',0,0,'L');
		$pdf->Cell(70,7,'Specilalty Staffing',0,0,'L');



		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(7);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Supervisor Name/Position',0,0,'L');
		$pdf->Cell(30,	3,'Phone',0,0,'L');
		$pdf->Cell(25,	3,'Assign. Type',0,0,'L');
		$pdf->Cell(40,	3,'Agency Name',0,1,'L');
	 
 	
/* Page 2 */
/*========*/
	 
		$pdf->AddPage();
		$pdf->SetFont('Arial','B',12);
 		$pdf->Ln(20);

 		$pdf->Cell(0,4,'Personal Data',0,1,'C');


/* Emergency Contact */

		$pdf->Ln(7);
		$pdf->SetFont('Arial','B',10);
		$pdf->Cell(5);	
		$pdf->Cell(22,7,'Emergency Contact:',0,1,'L');
		$pdf->Ln(3);
		$pdf->SetFont('Arial','',9);

			
		$pdf->Line(10, 53, 200, 53);
		$pdf->Line(10, 53, 10, 92);
		$pdf->Line(10, 92, 200, 92);
		$pdf->Line(200, 53, 200, 92);
	 
		$pdf->SetFont('Arial','',10);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Marsha Gomez',0,0,'L');
		$pdf->Cell(90,7,'1423 9th Ave, Brooklyn, NY 11234',0,0,'L');
		$pdf->Cell(30,7,'(*************',0,0,'L');


		$pdf->Line(10, 63, 200, 63);
		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(50,  3,'Name',0,0,'L');
		$pdf->Cell(90,	3,'Address',0,0,'L');
		$pdf->Cell(25,	3,'Phone Number',0,0,'L');


		$pdf->SetFont('Arial','',10);
		$pdf->Ln(3);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Yes',0,0,'L');
		$pdf->Cell(50,7,'89009890998',0,0,'L');


		$pdf->Line(10, 74, 200, 74);
		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(50,  3,'Use your car for work?',0,0,'L');
		$pdf->Cell(90,	3,'Drivers License #',0,0,'L');
	

		$pdf->SetFont('Arial','',10);
		$pdf->Ln(4);
		$pdf->Cell(3);
		$pdf->Cell(50,7,'Allstate Insurance ',0,0,'L');
		$pdf->Cell(90,7,'WE6787678776',0,0,'L');
		$pdf->Cell(30,7,'07/2016',0,0,'L');



		$pdf->Line(10, 86, 200, 86);
		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(8);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'Car Insurance Carrier',0,0,'L');
		$pdf->Cell(90,3,'Car insurance Policy #',0,0,'L');
		$pdf->Cell(25,3,'Expiration Date',0,0,'L');

/* Legal Questions*/		
		
		$pdf->Ln(10);

		$pdf->SetFont('Arial','B',12);	
		$astr = '*';
		
/* Investigation */
		$pdf->Cell(3);	
		$pdf->Cell(3,4,$astr,0,0,'L');
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(120,4,'Has your license or certification ever been under investigation?',0,0,'L');
		$pdf->Cell(4,4,'',1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');

		$pdf->Cell(4,4,'X',1,0,'C');
		$pdf->Cell(10,4,'No',0,0,'C');

		$pdf->Line(14, 106, 180, 106);
		
		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(10);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'If YES, Please Explain',0,0,'L');

/* Suspention */
		$pdf->Ln(5);
		$pdf->Cell(3);	
		$pdf->SetFont('Arial','B',12);	
		$pdf->Cell(3,4,$astr,0,0,'L');
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(120,4,'Has your license or certification ever been Revoked or under Suspension?',0,0,'L');
		$pdf->Cell(4,4,'',1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');

		$pdf->Cell(4,4,'X',1,0,'C');
		$pdf->Cell(10,4	,'No',0,0,'C');

		$pdf->Line(14, 121, 180, 121);
			
		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(10);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'If YES, Please Explain',0,0,'L');

/* Prof. Liability */
		$pdf->Ln(5);
		$pdf->Cell(3);	
		$pdf->SetFont('Arial','B',12);	
		$pdf->Cell(3,4,$astr,0,0,'L');
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(120,4,'Have your ever been named as defendant in a professional liability action?',0,0,'L');
		$pdf->Cell(4,4,'',1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');

		$pdf->Cell(4,4,'X',1,0,'C');
		$pdf->Cell(10,4	,'No',0,0,'C');

		$pdf->Line(14, 136, 180, 136);
			
		$pdf->SetFont('Times','B',6);	
		$pdf->Ln(10);

		$pdf->Cell(3);	
		$pdf->Cell(50,3,'If YES, Please Explain',0,0,'L');

/* Personal Info */		

		$pdf->SetFont('Arial','B',12);
 		$pdf->Ln(6);
 		$pdf->Cell(0,4,'Additional Information',0,1,'C');

	
 		$pdf->Ln(6);
		$pdf->SetFont('Arial','',9);
 		$pdf->Cell(4);	
		$pdf->Cell(30,4,'Shift Preference:',0,0,'L');
		
		$pdf->SetFont('Arial','',9);

		$pdf->Cell(4,4,'',1,0,'C');
		$pdf->Cell(10,4,'AM',0,0,'C');

		$pdf->Cell(4,4,'X',1,0,'C');
		$pdf->Cell(10,4	,'PM',0,0,'C');

		$pdf->SetFont('Arial','',9);
 		$pdf->Cell(4);	
		$pdf->Cell(32,4,'Days Of The Week:',0,0,'L');
		$pdf->SetFont('Arial','U',9);

		$pdf->Cell(30,4,'                        	        ',0,0,'L');

 		$pdf->Ln(6);
		
 		$pdf->Cell(4);	
		$pdf->SetFont('Arial','',	9);
		$pdf->Cell(120,4,'Are you currently employed?',0,0,'L');
		$pdf->SetFont('Arial','',9);

		$pdf->Cell(4,4,'X'	,1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(10,4	,'No',0,0,'C');


 		$pdf->Ln(5);
		
 		$pdf->Cell(4);	
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(120,4,'If YES, may we contact your employer?',0,0,'L');
		$pdf->SetFont('Arial','',9);

		$pdf->Cell(4,4,'X'	,1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(10,4	,'No',0,0,'C');


 		$pdf->Ln(5);
		
 		$pdf->Cell(4);	
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(120,4,'Do you have one year of acute care experience in the past two years?',0,0,'L');
		$pdf->SetFont('Arial','',9);

		$pdf->Cell(4,4,'X'	,1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(10,4	,'No',0,0,'C');

 		$pdf->Ln(5);
		
 		$pdf->Cell(4);	
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(50,4,'What is the best time to call you.',0,0,'L');
		$pdf->SetFont('Arial','U',9);

		$pdf->Cell(30,4,'                        	        ',0,0,'L');
		$pdf->Cell(4);
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(40,4,'Date Available to work',0,0,'L');
		$pdf->SetFont('Arial','U',9);

		$pdf->Cell(30,4,'                        	        ',0,0,'L');


 		$pdf->Ln(8);
		$pdf->SetFont('Arial','',9);
 		$pdf->Cell(4);	
		$pdf->Cell(120,4,'Do you have experience working with Electronic Medical Records:',0,0,'L');
		$pdf->Cell(4,4,'X'	,1,0,'C');
		$pdf->Cell(10,4,'Yes',0,0,'C');
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(10,4	,'No',0,0,'C');


		$pdf->Ln(8);
		$pdf->SetFont('Arial','B',9);
 		$pdf->Cell(4);	
		$pdf->Cell(50,4,	'How did you hear about Gotham',0,0,'L');
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(55,4,'( Please select one of the following choices ):',0,0,'L');

		$pdf->Ln(6);
 		$pdf->Cell(4);	
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(40,4	,'Direct Mail',0,0,'L');

		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(40,4	,'Internet',0,0,'L');

		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(40,4	,'Magazine/Newspaper',0,0,'L');

 
		$pdf->Ln(6);
 		$pdf->Cell(4);	
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(40,4	,'Personal Referral',0,0,'L');
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(26,4,'Name of Referrer',0,0,'L');
		$pdf->SetFont('Arial','U',9);
		$pdf->Cell(30,4,'                                    	        ',0,0,'L');

		$pdf->Ln(6);
 		$pdf->Cell(4);	
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(4,4,'',1	,0,'C');
		$pdf->Cell(40,4	,'Job Fair',0,0,'L');
		$pdf->SetFont('Arial','',9);
		$pdf->Cell(12,4,'Name',0,0,'L');
		$pdf->SetFont('Arial','U',9);
		$pdf->Cell(30,4,'                                    	        ',0,0,'L');


	//+++++++++++++++++++++++++++++++++++++++++++++++++

		$pdf->AddPage();
		$pdf->SetFont('Arial','B',10);
		$pdf->Ln(106);

 		$pdf->Cell(18,4,'Signature:',0,0,'L');
		$pdf->SetFont('Arial','U',9);
		$pdf->Cell(30,4,'                                            	        ',0,0,'L');
	 
		$pdf->SetFont('Arial','B',10);
	   	$sign_date = date("m/d/Y");
	    $pdf->Cell(40);	
 		$pdf->Cell(10,4,'Date:',0,0,'L');
		$pdf->SetFont('Arial','',10);

 		$pdf->Cell(20,4,$sign_date,0,0,'L');

	
	$pdf->Output();
	 
 
	 	
?>
