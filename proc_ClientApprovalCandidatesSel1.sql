

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_ClientApprovalCandidatesSel1$$

CREATE PROCEDURE proc_ClientApprovalCandidatesSel1 (IN 	p_client_id int, 
														p_client_unit_id int,
														p_service_type_id int,
														p_specialties_list varchar(96))  

BEGIN

 
	create temporary table tmp 

	(
		RegistrantId int
	
	);
 
	 
	SET @myArrayOfValue = p_specialties_list;

    WHILE (LOCATE(',', @myArrayOfValue) > 0)
    DO
        SET @value = LEFT(@myArrayOfValue, LOCATE(',',@myArrayOfValue) - 1);    
        SET @myArrayOfValue = SUBSTRING(@myArrayOfValue, LOCATE(',',@myArrayOfValue) + 1);
		
		insert into tmp
		SELECT a.RegistrantId FROM RegistrantAttchedSpecialties a, Registrants b 
		WHERE SpecialtyId = @value
		AND a.RegistrantId = b.Id
		and b.StatusId = '1'
		AND NOT EXISTS (SELECT 1 from ClientApprovedRegistrants c
			WHERE a.RegistrantId = c.RegistrantId
			AND c.ClientId = p_client_id);
		 
		
		
    END WHILE; 
	 
	/*==========================================*/	


	
	/*++++++++++++++++++++++++++++++++++++++++++++++*/
	SELECT 	a.Id as RegistrantId,
			CONCAT( trim( a.LastName) , ', ', trim( a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
       
			(SELECT max(ServiceDate) from WeeklyServices g
				where a.Id = g.RegistrantId
				AND ScheduleStatusId = 7 ) as LastSchedDate,  
			a.City,
			SUBSTRING(a.ZipCode,1,5) as ZipCode,
			CONCAT ( trim(COALESCE(MobilePhone,'')) , ' ',  trim(COALESCE(HomePhone,''))) as PhoneNumbers,
			(SELECT  CASE count(*)
						   WHEN 0 THEN ''
							   ELSE group_concat( CredItemDesc SEPARATOR ', ' )
						   END  
					   FROM RegistrantCredItems h, CredentialingItems k
						   WHERE h.RegistrantId = a.Id
						   AND k.Id = h.CredItemId
						   AND ComplianceLevelId =1 )     as NonComplList	
		FROM    Registrants a,
				RegistrantTypes f
		WHERE   a.TypeId = f.Id 
		AND EXISTS (SELECT 1 FROM tmp b
						WHERE a.Id = b.RegistrantId);
						
	/*==========================================*/	
	
	drop temporary table if exists tmp;
	
 
END $$

DELIMITER ;	