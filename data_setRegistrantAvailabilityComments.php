<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId = $_POST['RegistrantId'];
	$ServiceDate = $_POST['ServiceDate'];
	$ShiftId = $_POST['ShiftId'];
	$Comments = $_POST['Comments'];
	$UserId = $_POST['UserId'];

	
	
	$result = $rcr_transaction->setRegistrantAvailabilityComments(	$RegistrantId, 
																	$ServiceDate,
																	$ShiftId,
																	$Comments,
																	$UserId); 
	
 
	
	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
