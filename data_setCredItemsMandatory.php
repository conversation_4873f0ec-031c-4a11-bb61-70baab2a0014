<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$RegistrantTypeId = $_POST['RegistrantTypeId'];
	$UserId = $_POST['UserId'];
	$CredItems = $_POST['CredItems'];
	$CredItems=json_decode($CredItems,true);

	$result = $rcr_transaction->setCredItemsMandatory(	$RegistrantTypeId,	
													   	$CredItems,	
														$UserId); 

	$result1 = $rcr_transaction->setUpdateRegistrantsMandatoryCredItems($RegistrantTypeId, $UserId);	
	$rcr_transaction->disconnectDB (); 


		
		
	echo $result;

?>
