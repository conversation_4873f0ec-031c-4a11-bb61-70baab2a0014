

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_ClientWklyScheduleSummary$$

CREATE PROCEDURE proc_ClientWklyScheduleSummary (IN p_client_id INT, p_we_date DATE)  

BEGIN


   
   DECLARE v_TotalSchedShifts,  v_CancelledShifts,   v_ConfPendingShifts,  v_ConfFullShifts, v_VerifiedShifts, v_PendingShifts INT;
   
   
   /* Total Scheduled Shifts  */
   /*==========================================*/
   Select count( Id ) into v_TotalSchedShifts
       From WeeklyServices
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatusId not in (2,3,4);

	 
 	 /* Total Cancelled Shifts */
   /*==========================================*/
     
   Select count( Id ) into v_CancelledShifts
       From WeeklyServices
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatusId in (2,3,4);


   /* Total Confirmed By Client  */
   /*==========================================*/
     
   Select count( Id )  into v_ConfPendingShifts
       From WeeklyServices
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatusId in (5,6);

  

   /* Total Fully Confirmed */
   /*==========================================*/

	 Select count( Id )  into v_ConfFullShifts
       From WeeklyServices
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatusId = 7;

   /* Total Verified */
   /*==========================================*/

	 Select count( Id )  into v_VerifiedShifts
       From WeeklyServices
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatusId > '7';
     
   /* Total Pending */
   /*==========================================*/

	 Select count( Id )  into v_PendingShifts
       From WeeklyServices
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatusId = '0';
    
 
   
   Select v_TotalSchedShifts as TotalShifts,
          v_CancelledShifts as CancelledShifts,
          v_ConfPendingShifts as ConfPendingShifts,
          v_ConfFullShifts as ConfFullShifts,
		  v_VerifiedShifts as VerifiedShifts,
		  v_PendingShifts as PendingShifts			
          ;    
   
   
   
END $$

DELIMITER ;	