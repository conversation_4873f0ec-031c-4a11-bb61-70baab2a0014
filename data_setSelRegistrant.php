<?php 


require "ewDataHandler.php";   
  
$rcr_transaction = new dataHandler();  

$form_data = json_decode(file_get_contents('php://input'));

$result = $rcr_transaction->setSelRegistrant(	$form_data->{'Id'},
												$form_data->{'SearchId'},
												$form_data->{'StatusId'},	
												$form_data->{'ExtId'},
												$form_data->{'TerminationType'},
												$form_data->{'TerminationReason'},

												$form_data->{'TerminationDate'},
												$form_data->{'BirthDate'},
												$form_data->{'HireDate'},
												$form_data->{'Gender'},
												$form_data->{'Race'},
												$form_data->{'CheckType'},
												$form_data->{'W4Status'},
												$form_data->{'Exemptions'},

												$form_data->{'TypeId'},
												$form_data->{'FirstName'},
												$form_data->{'LastName'},
												$form_data->{'MiddleInitial'},
												$form_data->{'StreetAddress1'},
												$form_data->{'StreetAddress2'},
												$form_data->{'City'},
												$form_data->{'State'},
												$form_data->{'ZipCode'},
												$form_data->{'MobilePhone'},
												$form_data->{'HomePhone'},
												$form_data->{'Fax'},
												$form_data->{'Email'},
												$form_data->{'Availability'},
												$form_data->{'HospitalExp'},
												$form_data->{'Shifts'},
												$form_data->{'NextDayPay'},
												$form_data->{'PerDiem'},
												$form_data->{'ThrnWeekContract'},
												$form_data->{'NewGraduate'},
												$form_data->{'ForeignTrained'},
												$form_data->{'UserId'} ); 


	/* If Existing Registrant's  "TypeId" was Changed - Change Credentialing Items
	 ============================*/  

	$registrant_id =  $form_data->{'Id'};	

	if ((is_numeric($registrant_id)) && ($form_data->{'TypeId'} != $form_data->{'OrigTypeId'})) {

		 $result1 = $rcr_transaction->setRegistrantTypeCredItemsChange (	$registrant_id,
																			$form_data->{'OrigTypeId'},
																			$form_data->{'TypeId'},
																			$form_data->{'UserId'}	
		 																);			

	}

	$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result1;
?>
