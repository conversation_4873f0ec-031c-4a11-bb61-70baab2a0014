<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 


$RegistrantId = $_POST['RegistrantId'];
$UserId = $_POST['UserId'];
$Specialties = $_POST['Specialties'];
$Specialties=json_decode($Specialties,true);

$result = $rcr_transaction->setRegistrantSpecialties($RegistrantId,	
											$Specialties,	
											$UserId); 

$rcr_transaction->disconnectDB (); 

//$json_a=json_decode($Specialties,true);


//echo $json_a[0]['id'];

	
	
echo $result;
/*$data = $grid_data[0];

echo $data->{'id'};*/ 
?>
