#!/usr/bin/env python3
"""
Example usage of the PHP Pear DB to MySQLi Converter
"""

import os
import sys
from php_pear_to_mysqli_converter import PearToMySQLiConverter

def convert_single_file():
    """Example: Convert a single PHP file"""
    print("=== Converting Single File ===")
    
    converter = PearToMySQLiConverter()
    
    # Convert the test sample file
    if os.path.exists('test_sample.php'):
        try:
            converted_content = converter.convert_file('test_sample.php')
            output_file = converter.save_converted_file('test_sample.php', converted_content)
            print(f"✓ Converted file saved as: {output_file}")
            converter.print_conversion_summary()
        except Exception as e:
            print(f"✗ Error converting file: {e}")
    else:
        print("✗ test_sample.php not found")

def convert_multiple_files():
    """Example: Convert multiple PHP files in a directory"""
    print("\n=== Converting Multiple Files ===")
    
    # List of PHP files to convert (you can modify this list)
    php_files = []
    
    # Find all PHP files in current directory
    for file in os.listdir('.'):
        if file.endswith('.php') and not file.endswith('_mysqli.php') and not file.endswith('_pear_backup.php'):
            php_files.append(file)
    
    if not php_files:
        print("No PHP files found to convert")
        return
    
    print(f"Found {len(php_files)} PHP files to convert:")
    for file in php_files:
        print(f"  - {file}")
    
    # Convert each file
    for php_file in php_files:
        print(f"\nConverting {php_file}...")
        converter = PearToMySQLiConverter()
        
        try:
            converted_content = converter.convert_file(php_file)
            output_file = converter.save_converted_file(php_file, converted_content)
            print(f"✓ Converted: {php_file} -> {output_file}")
            
            # Show summary for each file
            if converter.conversions_made:
                conversion_counts = {}
                for conversion in converter.conversions_made:
                    conversion_counts[conversion] = conversion_counts.get(conversion, 0) + 1
                
                print("  Conversions made:")
                for conversion, count in conversion_counts.items():
                    if count > 1:
                        print(f"    - {conversion} ({count} times)")
                    else:
                        print(f"    - {conversion}")
            else:
                print("  No Pear DB patterns found")
                
        except Exception as e:
            print(f"✗ Error converting {php_file}: {e}")

def analyze_file_without_converting():
    """Example: Analyze what would be converted without actually converting"""
    print("\n=== Analysis Mode (No Conversion) ===")
    
    if not os.path.exists('test_sample.php'):
        print("✗ test_sample.php not found")
        return
    
    converter = PearToMySQLiConverter()
    
    # Read file content
    with open('test_sample.php', 'r') as f:
        content = f.read()
    
    # Check for Pear DB patterns
    pear_patterns = [
        ('Pear DB include', r"require_once\s*\(\s*['\"]DB\.php['\"]\s*\)"),
        ('DB::connect()', r'DB::connect\s*\('),
        ('getAll() method', r'->getAll\s*\('),
        ('DB::isError()', r'DB::isError\s*\('),
        ('DB::errorMessage()', r'DB::errorMessage\s*\('),
        ('escapeSimple()', r'->escapeSimple\s*\('),
        ('disconnect()', r'->disconnect\s*\('),
    ]
    
    import re
    
    print("Pear DB patterns found:")
    found_any = False
    
    for pattern_name, pattern_regex in pear_patterns:
        matches = re.findall(pattern_regex, content)
        if matches:
            print(f"  ✓ {pattern_name}: {len(matches)} occurrence(s)")
            found_any = True
    
    if not found_any:
        print("  No Pear DB patterns detected")

def main():
    """Main example function"""
    print("PHP Pear DB to MySQLi Converter - Usage Examples")
    print("=" * 50)
    
    # Example 1: Convert single file
    convert_single_file()
    
    # Example 2: Analyze without converting
    analyze_file_without_converting()
    
    # Example 3: Convert multiple files (uncomment to use)
    # convert_multiple_files()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nTo use the converter from command line:")
    print("  python3 php_pear_to_mysqli_converter.py your_file.php")
    print("  python3 php_pear_to_mysqli_converter.py your_file.php --no-backup")
    print("  python3 php_pear_to_mysqli_converter.py your_file.php --output custom_name.php")

if __name__ == "__main__":
    main()
