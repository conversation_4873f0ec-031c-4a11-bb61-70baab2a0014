

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getClientAssignmentRegistrantsSelection$$

CREATE PROCEDURE proc_getClientAssignmentRegistrantsSelection (IN p_client_id int, 
																  p_client_unit_id int,
																  p_service_type_id int,   
																  p_start_date date,
																  p_end_date date)
																 

BEGIN

 
	create temporary table tmp 

		(
			RegistrantId INT,
			RegistrantName VARCHAR(25),
			SpecialtiesList VARCHAR(256),
			CredItems VARCHAR(512),
			PhoneNumbers VARCHAR(128)
		);

	INSERT INTO tmp
	SELECT 	a.RegistrantId,
			CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
			(SELECT  CASE count(*) 
						WHEN 0 THEN ''
					ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
				END as SpecialtiesList
				FROM RegistrantAttchedSpecialties h, Specialties i
				where i.Id = h.SpecialtyId
				  and  h.RegistrantId = c.Id),
			COALESCE((Select group_concat( CredItemDesc
											SEPARATOR ', ' ) 
											FROM RegistrantCredItems k,  CredentialingItems h
											WHERE a.RegistrantId = k.RegistrantId
											  AND  k.StatusId in (1,2) 	
                                              AND  k.CredItemId = h.Id ),''),
			COALESCE(CONCAT ( trim(c.MobilePhone) , ' ',  trim(c.HomePhone), ' ', trim(c.Email)),'')
		FROM 	ClientApprovedRegistrantsNonRest a, 
				Users b,
				Registrants c,
				RegistrantTypes f
			
		WHERE a.UserId = b.UserId
		AND a.ClientId = p_client_id 
		AND a.ClientUnitId = p_client_unit_id 
		AND a.ServiceTypeId = p_service_type_id 

		AND a.RegistrantId = c.Id
		AND c.TypeId = f.Id
	Order By c.LastName, c.FirstName ;  
     
	/*=========================================================================*/
	/* Remove Registrants if already on Assignment between Start and End Dates 
	/*=========================================================================*/
	 
	DELETE FROM tmp
	WHERE EXISTS (SELECT 1 FROM ClientAssignments a
					WHERE a.StatusId = '1'
					AND a.RegistrantId = tmp.RegistrantId
					AND p_start_date BETWEEN a.StartDate and a.EndDate);
	
	/*=========================================================================*/
	/* Remove Registrants if already Scheduled (Per-Diem) between Start and End Dates 
	/*=========================================================================*/
	 
	DELETE FROM tmp
	WHERE EXISTS (SELECT 1 FROM WeeklyServices a
					WHERE a.ScheduleStatusId > '5'
					AND a.RegistrantId = tmp.RegistrantId
					AND a.ServiceDate BETWEEN p_start_date and p_end_date);
	 
	
	SELECT * FROM tmp ;
	 
	drop temporary table if exists tmp;	
	
END $$

DELIMITER ;	