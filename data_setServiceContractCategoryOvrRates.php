<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RequestedAction  =  $_POST['RequestedAction'];
	$ContractCategoryRatesOvrId  =  $_POST['ContractCategoryRatesOvrId'];
	$ContractCategoryId  =  $_POST['ContractCategoryId'];
	$ServiceTypeId  =  $_POST['ServiceTypeId'];
	$RateDateRangeId  =  $_POST['RateDateRangeId'];
	$WeekDayId  =  $_POST['WeekDayId'];
	$ShiftId  =  $_POST['ShiftId'];
	$BillPayFL  =  $_POST['BillPayFL'];
	$OverrideRate  =  $_POST['OverrideRate'];
	$UserId  =  $_POST['UserId'];


	if ($RequestedAction == 'edit') {
	
		$result = $rcr_transaction->setServiceContractCategoryOvrRates(	$ContractCategoryRatesOvrId,
																	$ContractCategoryId,
																	$ServiceTypeId,
																	$RateDateRangeId,
																	$WeekDayId,
																	$ShiftId,
																	$BillPayFL,
																	$OverrideRate,
																	$UserId
																  ); 


	}
	
	if ($RequestedAction == 'delete') {
	
		$result = $rcr_transaction->setDeleteServiceContractCategoryOvrRate($ContractCategoryRatesOvrId); 
																   
	}
	
	$rcr_transaction->disconnectDB (); 

	echo $result;

?>
