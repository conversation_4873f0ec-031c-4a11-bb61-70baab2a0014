
/*=========================================*/

DELIMITER $$

DROP TRIGGER IF EXISTS trig_ClientInvoiceStatusUpdate  $$

CREATE TRIGGER trig_ClientInvoiceStatusUpdate BEFORE UPDATE ON InvoiceHeader

FOR EACH ROW BEGIN

	 
        IF NEW.PaidAmount > 0 THEN
			IF NEW.PaidAmount =  NEW.TotalAmount THEN    	
				SET NEW.StatusId = 2;
			ELSE SET NEW.StatusId = 3;
			END IF;  
        END IF;
     
	
		 
	
END; $$

DELIMITER ;