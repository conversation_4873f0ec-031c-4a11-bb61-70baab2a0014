
/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getServiceContractCategoryRates$$

CREATE PROCEDURE proc_getServiceContractCategoryRates (p_contract_category_id int, p_service_type_id int , p_rate_date_range_id int)  

BEGIN


   
   
   /*============================================*/
   
   create temporary table tmp

   (
       ContractCategoryId INT,
       ServiceTypeId INT,
	   RateDateRangeId INT,
       WDBillRateShift1 DEC(6,2),
       WDPayRateShift1 DEC(6,2),
       WEBillRateShift1 DEC(6,2),
       WEPayRateShift1 DEC(6,2),
       
       WDBillRateShift2 DEC(6,2),
       WDPayRateShift2 DEC(6,2),
       WEBillRateShift2 DEC(6,2),
       WEPayRateShift2 DEC(6,2),

       WDBillRateShift3 DEC(6,2),
       WDPayRateShift3 DEC(6,2),
       WEBillRateShift3 DEC(6,2),
       WEPayRateShift3 DEC(6,2),
       
       WDBillRateShift4 DEC(6,2),
       WDPayRateShift4 DEC(6,2),
       WEBillRateShift4 DEC(6,2),
       WEPayRateShift4 DEC(6,2),
       
       WDBillRateShift5 DEC(6,2),
       WDPayRateShift5 DEC(6,2),
       WEBillRateShift5 DEC(6,2),
       WEPayRateShift5 DEC(6,2)
   
   );
   
   /* Create "Empty Rates"
   ================================================*/
   
   INSERT INTO tmp
   VALUES (
               p_contract_category_id,
               p_service_type_id,
			   p_rate_date_range_id,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00,
               0.00
               
           );

   /* Set Day Shift (1) WeekDay Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WDBillRateShift1 = b.BillRate,
           a.WDPayRateShift1 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 1
   AND b.ShiftId = 1    ;

   /* Set Day Shift (1) WeekEnd Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WEBillRateShift1 = b.BillRate,
           a.WEPayRateShift1 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 2
   AND b.ShiftId = 1    ;

   /* Set Evening Shift (2) WeekDay Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WDBillRateShift2 = b.BillRate,
           a.WDPayRateShift2 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
    AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 1
   AND b.ShiftId = 2    ;

   /* Set Evening Shift (2) WeekEnd Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WEBillRateShift2 = b.BillRate,
           a.WEPayRateShift2 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 2
   AND b.ShiftId = 2    ;
   
   /* Set Night Shift (3) WeekDay Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WDBillRateShift3 = b.BillRate,
           a.WDPayRateShift3 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
    AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
  AND b.RateTypeId = 1
   AND b.ShiftId = 3    ;

   /* Set Night Shift (3) WeekEnd Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WEBillRateShift3 = b.BillRate,
           a.WEPayRateShift3 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 2
   AND b.ShiftId = 3    ;

   /* Set 12 Hr Day Shift (4) WeekDay Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WDBillRateShift4 = b.BillRate,
           a.WDPayRateShift4 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 1
   AND b.ShiftId = 4    ;

   /* Set 12 Hr Day Shift (4) WeekEnd Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WEBillRateShift4 = b.BillRate,
           a.WEPayRateShift4 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 2
   AND b.ShiftId = 4    ;

   /* Set 12 Hr Night Shift (5) WeekDay Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WDBillRateShift5 = b.BillRate,
           a.WDPayRateShift5 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 1
   AND b.ShiftId = 5    ;

   /* Set 12 Hr Night Shift (5) WeekEnd Rates
   ==========================================*/          
   
   UPDATE tmp a, ServiceContractCategoryRates b
       SET a.WEBillRateShift5 = b.BillRate,
           a.WEPayRateShift5 = b.PayRate
   WHERE a.ContractCategoryId = b.ContractCategoryId
   AND a.ServiceTypeId = b.ServiceTypeId
   AND a.RateDateRangeId = b.RateDateRangeId
   AND b.RateTypeId = 2
   AND b.ShiftId = 5    ;

   /*===================================*/
   SELECT * FROM tmp;
   
   DROP TEMPORARY TABLE IF  EXISTS tmp;
   
END $$

DELIMITER ;	