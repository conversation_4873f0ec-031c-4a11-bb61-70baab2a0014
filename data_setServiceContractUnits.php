<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$ContractCategoryId = $_POST['ContractCategoryId']; 
	
	$UnassigUnitsArray = $_POST['UnassigUnitsArray'];
	$UnassigUnitsArray=json_decode($UnassigUnitsArray,true);

	$AssigUnitsArray = $_POST['AssigUnitsArray'];
	$AssigUnitsArray=json_decode($AssigUnitsArray,true);

	$UserId = $_POST['UserId'];

	/* Set Service Contract Un-Assigned Units
	================================================*/
	
	$result = $rcr_transaction->setServiceContractUnassignedUnits(	$ContractCategoryId,	
																	$UnassigUnitsArray,	
																	$UserId); 


	/* Set Service Contract Assigned Units
	================================================*/
	
	$result1 = $rcr_transaction->setServiceContractAssignedUnits(	$ContractCategoryId,	
																	$AssigUnitsArray,	
																	$UserId); 

																	
	$rcr_transaction->disconnectDB (); 

		
		
	echo $result1;
  
?>
