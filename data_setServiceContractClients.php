<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$ContractId = $_POST['ContractId'];
	
	$UnassigClientsArray = $_POST['UnassigClientsArray'];
	$UnassigClientsArray=json_decode($UnassigClientsArray,true);

	$AssigClientsArray = $_POST['AssigClientsArray'];
	$AssigClientsArray=json_decode($AssigClientsArray,true);

	$UserId = $_POST['UserId'];

	/* Set Service Contract Un-Assigned Clients
	================================================*/
	
	$result = $rcr_transaction->setServiceContractUnassignedClients($ContractId,	
																	$UnassigClientsArray,	
																	$UserId); 


	/* Set Service Contract Assigned Clients
	================================================*/
	
	$result1 = $rcr_transaction->setServiceContractAssignedClients(	$ContractId,	
																	$AssigClientsArray,	
																	$UserId); 

																	
	$rcr_transaction->disconnectDB (); 

		
		
	echo $result1;
  
?>
