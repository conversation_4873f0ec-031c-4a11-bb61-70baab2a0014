<?php  

       
	// error_reporting(E_ALL);
	// ini_set('display_errors', TRUE);
	// ini_set('display_startup_errors', TRUE);
	         


	ini_set("memory_limit","-1");

	require_once("db_GetSetData.php");

	 	
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


    $RegistrantId = $_GET['RegistrantId'];
    $RegistrantLastName = $_GET['RegistrantLastName']; 
    $RegistrantFirstName = $_GET['RegistrantFirstName'];     
    $StoredFileName = $_GET['StoredFileName'];
    $FileExt = $_GET['FileExt'];
    $UserId = $_GET['UserId'];
	
 
    
	// $RegistrantId = '306';
 //   $RegistrantLastName = 'Lastihenos'; 
 //   $RegistrantFirstName = 'Christy'; 

 //    $FileExt = 'xlsx';

 
   if ($FileExt == 'xlsx') {

	 	$inputFileType = 'Excel2007';
	 
	} else {
 
		$inputFileType = 'Excel5';

 	}	
	 
 	// echo 'Step 01<br>';

	// $sheetname = 'Sheet1';


	$inputFileName = '../hr/'.$StoredFileName.'.'.$FileExt;
	// $inputFileName = '../hr/hlLnc1kKMTD7ekIX3OLy.xlsx';

 
 // 	$StoredFileName = '60HA9jpO5cgalIpm11mM';
	// $StoredFileName = 'hlLnc1kKMTD7ekIX3OLy';

 	// Delete entried for a give file Name
 	// ================================


   $conn = getCon();


 	$query = " DELETE FROM SchSessionNotesTrans
 				WHERE SessionNoteFileName = '{$StoredFileName}'
 	"; 
              			       

	 $result =  mysqli_query($conn, $query);

	setDisConn($conn); 

	/**  Create a new Reader of the type defined in $inputFileType  **/
	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	/**  Load $inputFileName to a PHPExcel Object  **/
	
	// $objReader->setLoadSheetsOnly($sheetname);
	$objReader->setReadDataOnly(true);


	$objPHPExcel = $objReader->load($inputFileName);


	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	 
	
	$i = 0;	
	$linecount = 0;	
    $matched_sessions = 0;
 
  
	$x = 0;

	foreach ($sheetData as &$row1) { // read excel - start  

		// var_dump($row1); 
		// echo '<br><br>';
 			 	
 			$x++;

		// Verify Provider Name Info
		//=========================================== 
 

			// $provider_notes_last_name = mysql_real_escape_string($row1["AN"]);	
			// $provider_notes_first_name = mysql_real_escape_string($row1["AO"]);	


			// if  (($RegistrantLastName != $provider_notes_last_name) || ($RegistrantFirstName != $provider_notes_first_name)) {


			// 		echo 'Provider Name from SESIS Session Notes DOES NOT MATCH Selected Provider from eWebStaffing!!!';
			// 		exit();

 
			// }



 
	 
		$i = 0;

 
 

		// Extract Session Info
		//=========================================== 

			do { // DO - START 

			if ($x > 1) {

				$start_time_frm = $row1["AG"];
				$start_time_check = date("H:i:s", strtotime($start_time_frm));	
				if ($start_time_check == '00:00:00') {
				 	   echo 'SESIS Session Notes file WAS NOT created via "Export To Excel"!!!';
					   exit();

				}



				// $provider_notes_last_name = mysql_real_escape_string($row1["AN"]);	
				// $provider_notes_first_name = mysql_real_escape_string($row1["AO"]);	
				$provider_notes_last_name = $row1["AO"];	
				$provider_notes_first_name = $row1["AP"];	


			if  ((trim(strtoupper($RegistrantLastName)) != trim(strtoupper($provider_notes_last_name))) || (trim(strtoupper($RegistrantFirstName)) != trim(strtoupper($provider_notes_first_name)))) {


					// if ($x == 2) {

						
					// 	if  ($RegistrantLastName != $provider_notes_last_name) {


					// 		echo 'RegistrantLastName: '.'"'.$RegistrantLastName.trim().'"'.' s.n. last name: '.'"'.$provider_notes_last_name.trim().'"'.'<br><br>';

					// 	}	


						// echo 'RegistrantLastName: '.$RegistrantLastName.trim().' s.n. last name: '.$provider_notes_last_name.trim().'<br><br>';
						// echo 'RegistrantFirstName: '.$RegistrantFirstName.trim().' s.n. first name: '.$provider_notes_first_name.trim().'<br><br>';


					// }	



				goto skip;
 
			}



			}


		 	// if (($row1["AD"] == 'ServPr')  || ($row1["AD"] == 'SerPrR') || ($row1["AD"] == 'ServPrM') || ($row1["AD"] == 'Service Provided') || ($row1["AD"] == 'Make-Up') || ($row1["AD"] == 'Makeup')) { // Sessions Section - Start  
			// if (strpos($row1["AD"], 'Service Provided') !== false) {				
			if ((strpos($row1["AD"], 'Service Provided') !== false) || (strpos($row1["AD"], 'Service provided') !== false)) {				
				

				$i++;

				/*=== Student ID ======*/
				$student_ext_id = $row1["A"];

				/*=== Student Last Name ======*/
				$student_last_name = $row1["B"];

				/*=== Student First Name ======*/
				$student_first_name = $row1["C"];

				$StudentNameDisp = $student_first_name.' '.$student_last_name; 

				/*=== Session Date ======*/


				$session_date_x = PHPExcel_Shared_Date::ExcelToPHPObject($row1["Y"]);
 	 			$session_date = $session_date_x->format('Y-m-d');		 
 	 			$session_date_disp = $session_date_x->format('m/d/Y');		 


				$dow = date('w',strtotime($session_date));

				if ($dow == '6') {

					$PayrollWeek = $session_date;

				} else {

					$PayrollWeek = date('Y-m-d', strtotime("next saturday", strtotime($session_date)));
			 

				}
 

				/*=== Session Start Time ======*/
 
				$start_time_frm = $row1["AH"];
 	 			$start_time = date("H:i:s", strtotime($start_time_frm));	

				/*=== Session End Time ======*/


				$end_time_frm = $row1["AI"];
 	 			$end_time = date("H:i:s", strtotime($end_time_frm));


				/*=== Service Type ======*/
				$service_type = $row1["Z"];


				/*=== Delivery Method ======*/
				$delivery_mode = $row1["AD"];
				$delivery_mode_id = 'I';
				 
				// $tele_therapy_fl =  strpos($delivery_mode,"Tele");
				// if (is_numeric($tele_therapy_fl)) {
				if (( stripos( $delivery_mode, 'Tele' ) !== false) || ( stripos( $delivery_mode, 'Remote' ) !== false) ) { 

					$delivery_mode_id = 'V';


				} 

				// echo " delivery_mode: ".$delivery_mode.' tele_therapy_fl: '.$tele_therapy_fl.' delivery_mode_id: '.$delivery_mode_id.'</br>';


				/*========================*/

				$grp_desc =  $row1["AF"]; 

				/*=== Group Size ======*/

			 	if ($grp_desc == 'Individual')  {
					$group_size = '1';

			 	
			 	} else {

					$group_size = $row1["AG"];
			 	}

				// $grp_desc =  $row1["AF"]; 


				// /*=== Group Size ======*/

			 // 	if ($grp_desc == 'Individual')  {
				// 	$group_size = '1';

			 	
			 // 	} else {

				// 	$group_size = preg_replace('/\D/', '', $grp_desc);
			 // 	}

				/*=== Duration ======*/

			 	$duration = $row1["AJ"];
				$duration = preg_replace('/\D/', '', $duration);
			  	
				/*=== Mandate Freq/Duration/Group ======*/

			 	$mand_desc = $row1["O"];
		    $arr_mand = explode(":",$mand_desc); 	

		    // print_r($arr_mand);
		    // echo  ' mand desc: '.$mand_desc.'<br><br>';

		    $mand_freq = $arr_mand[3];
		    $mand_freq_int = (int) filter_var($mand_freq, FILTER_SANITIZE_NUMBER_INT);

		    $mand_dur = $arr_mand[4];
		    $mand_group = $arr_mand[5];

		   //  echo  ' mand desc: '.$mand_desc.'<br><br>';
			  // echo ' freq: '.$mand_freq_int.' dur: '.$mand_dur.' group: '.$mand_group.'<br><br>';



				// echo $x.' student id:'.$student_ext_id.' service date: '.$session_date_disp. ' grp/ind: '.$grp_desc.'<br>'; 

	 			$conn = getCon();

			  $student_last_name = mysqli_real_escape_string($conn, $student_last_name); 

				$query = " INSERT INTO SchSessionNotesTrans
				( 
					StudentExtId,
					StudentFirstName,
					StudentLastName,
					StudentNameDisp,
					PayrollWeek,
					SessionDate,
					SessionDateDisp,
					StartTime,
					StartTimeDisp,
					EndTime,
					EndTimeDisp,
					RegistrantId,
					SessionDeliveryMode,
					GroupingDesc,
					SessionLength,
					SessionGrpSize,
					SessionNoteFileName,
					MandSessionFrequency,
					MandSessionLength,
					MandSessionGrpSize
		        )
			VALUES
			( 
				'{$student_ext_id}',
				'{$student_first_name}',
				'{$student_last_name}',
				'{$StudentNameDisp}',
				'{$PayrollWeek}',
				'{$session_date}',
				'{$session_date_disp}',
				'{$start_time}',
				'{$start_time_frm}',
				'{$end_time}',
				'{$end_time_frm}',
				'{$RegistrantId}',
				'{$delivery_mode_id}',
				'{$grp_desc}',
				'{$duration}',
				'{$group_size}', 
				'{$StoredFileName}',
				'{$mand_freq_int}',
				'{$mand_dur}',
				'{$mand_group}'

			) 
              			       
			"; 	

			  // echo ' query: '.$query .'<br><br>';


				$result =  mysqli_query($conn, $query);
   

 			setDisConn($conn); 
 
			$linecount++;	
 			 
		} // / Sessions Section - Start	 

	   	skip:	

	   } while (0); // // DO - END 

	}	//read excel - end 


	if ($linecount == 0) {

		echo 'The File you are tying to upload does contain any "NEW" SESIS Session Notes Transactions!!!';
		exit();


	}


	$conn = getCon();


	$query = " UPDATE SchSessionNotesTrans a
		   set a.MandatesList = (select group_concat( b.Id SEPARATOR ',' )
				from SchStudentMandates b	 
		      where a.StudentExtId = b.StudentExtId
		      and SessionDate between b.StartDate and b.EndDate
		      
		      and a.MandSessionFrequency = b.SessionFrequency 
		      and a.MandSessionLength = b.SessionLength 
		      and a.MandSessionGrpSize = b.SessionGrpSize 
		      
		      and b.StatusId = '1' 
		      and a.RegistrantId = b.RegistrantId 
		   ),
			a.SessionFrequencyList = (select group_concat( b.SessionFrequency SEPARATOR ',' )
				from SchStudentMandates b	 
		      where a.StudentExtId = b.StudentExtId
		      and SessionDate between b.StartDate and b.EndDate

		      and a.MandSessionFrequency = b.SessionFrequency 
		      and a.MandSessionLength = b.SessionLength 
		      and a.MandSessionGrpSize = b.SessionGrpSize 
		      
		      and b.StatusId = '1' 
		      and a.RegistrantId = b.RegistrantId 
		   ) 		   
		  WHERE SessionNoteFileName = '{$StoredFileName}'
		  "; 

  // echo ' query:'.$query.'<br><br>';


	 $result =  mysqli_query($conn, $query);

	 setDisConn($conn); 

	 

   //==============
   // Process Duplicate Mandates
   //==============

	class Imp_Trans
			{
		    	public $Id;
			    public $StudentExtId;
			    public $SessionDate;
			    public $PayrollWeek;
			    public $MandatesList;
			    public $SessionFrequencyList;
			    public $MandateId;



			}



	$session_arr = array();

	$conn = getCon();

	$query = "  SELECT Id, StudentExtId, SessionDate, PayrollWeek, MandatesList, SessionFrequencyList from SchSessionNotesTrans
				   where INSTR (MandatesList, ',') 
				   and SessionNoteFileName = '{$StoredFileName}'
				   -- and StudentExtId = '245817804'
				   Order by PayrollWeek
				"; 

     $result =  mysqli_query($conn, $query);


    $save_payroll_week = '';

     while ($row = $result->fetch_assoc()) { // While - Start 
 
 
			$trans = new Imp_Trans();
			$trans->Id = $row['Id'];
			$trans->StudentExtId = $row['StudentExtId'];
			$trans->SessionDate = $row['SessionDate'];
			$trans->PayrollWeek = $row['PayrollWeek'];
 			$trans->MandatesList = $row['MandatesList'];
			$trans->SessionFrequencyList = $row['SessionFrequencyList'];
			$trans->MandateId = '';
			

			$session_arr[] = $trans;


     } 	

    setDisConn($conn); 

   $saved_payroll_week = '';
   $saved_student_id = '';
	

  // echo ' before - session_arr: ';
  // print_r($session_arr);  
  // echo '<br><br>'; 

	foreach ($session_arr as $session) { // for each
	  
        if (!$saved_payroll_week) {

				$arr = array();

			    $arr_mand = explode(",",$session->MandatesList); 	
			    $arr_freq = explode(",",$session->SessionFrequencyList); 	

			    $arr = array(
			  	  array('mand' => $arr_mand[0], 'freq' => $arr_freq[0], 'used_freq' => 0),
			  	  array('mand' => $arr_mand[1], 'freq' => $arr_freq[1], 'used_freq' => 0) 

			     ); 

			  	shuffle($arr);


				$saved_payroll_week =  $session->PayrollWeek;    	
				$saved_student_id =  $session->StudentExtId;    	
		    
		     }
			  
		     if (($saved_payroll_week !=  $session->PayrollWeek) || ($saved_student_id !=  $session->StudentExtId)) {

			   	$arr = array();

			    $arr_mand = explode(",",$session->MandatesList); 	
			    $arr_freq = explode(",",$session->SessionFrequencyList); 	

			    $arr = array(
			  	  array('mand' => $arr_mand[0], 'freq' => $arr_freq[0], 'used_freq' => 0),
			  	  array('mand' => $arr_mand[1], 'freq' => $arr_freq[1], 'used_freq' => 0) 

			     ); 

			  	shuffle($arr);
		 

				$saved_payroll_week =  $session->PayrollWeek;    	
				$saved_student_id =  $session->StudentExtId;    	
		     
		     } 


			  
			  foreach ($arr as $key => $field) {

			  	 if ($field['used_freq'] <  $field['freq']) {

			  	 	$session->MandateId = $field['mand'];
					$arr[$key]['used_freq']   = $arr[$key]['used_freq'] + 1;
					break;		  	 	

			  	 }

			  }	 	
 

   } // for each

 
  // echo ' after - session_arr: ';
  // print_r($session_arr);  
  // echo '<br><br>'; 

	

	foreach ($session_arr as $session_upd) {

 		//===============
 	    // Update Mandate Id (Multi Mundates) 

		// echo ' session_upd: '.var_dump($session_upd); 
		// echo '<br><br>';  


		$Id = $session_upd->Id;
		$MandateId = $session_upd->MandateId;
		$MandatesList = $session_upd->MandatesList;

		$conn1 = getCon();


		 $query = "  UPDATE SchSessionNotesTrans
		 			   SET 	MandateId = '{$MandateId}'
		 			WHERE Id = '{$Id}'   
		 			AND SessionNoteFileName = '{$StoredFileName}'


		  		 "; 	


 
 		$result1 =  mysqli_query($conn1, $query);	


 		setDisConn($conn1); 


 	}

 		//===============
 	    // Update Mandate Id

		$conn1 = getCon();


		$query1 = "  UPDATE SchSessionNotesTrans
		 			   SET 	MandateId = MandatesList
		 			WHERE MandateId = ''   
		 			AND SessionNoteFileName = '{$StoredFileName}'

		  		 "; 	

 
 		$result1 =  mysqli_query($conn1, $query1);	

 		setDisConn($conn1); 


		//===============
 	  // Update Mandate's Freq/Duration/Grp Size 

		$conn1 = getCon();


		$query1 = "  UPDATE SchSessionNotesTrans a, SchStudentMandates b
		 			   SET 	a.MandSessionFrequency = b.SessionFrequency,
		 			        a.MandSessionLength = b.SessionLength,
		 			        a.MandSessionGrpSize = b.SessionGrpSize 
		 			WHERE a.MandateId = b.Id   
		 			AND a.SessionNoteFileName = '{$StoredFileName}'

		  		 "; 	


 
 		$result1 =  mysqli_query($conn1, $query1);	

 		setDisConn($conn1); 

		//===============
 	  // Adjust Mandate's Grp Size if Mandate is Blank 

		$conn1 = getCon();


		$query1 = "  UPDATE SchSessionNotesTrans 
		 			          SET SessionGrpSize = 1,
		 			              GroupingDesc = '' 
		 			WHERE SessionNoteFileName = '{$StoredFileName}'
		 			AND SessionGrpSize > 1 
		 			AND MandateId = ''

		  		 "; 	


 
 		$result1 =  mysqli_query($conn1, $query1);	

 		setDisConn($conn1); 

    //=================


       $fields_list = array('SessionNotesFileName' => $StoredFileName,
                           'Userid' => $Userid
                            );


        $curl = curl_init();
       
    	$url_script_path = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/";

    	$scipt_name = "data_setSchProcessSessionNotesImportFile.php";
    	$url_script = $url_script_path.$scipt_name;

 
         
        curl_setopt($curl, CURLOPT_URL, $url_script);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $fields_list); 
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); //needed so that the $result=curl_exec() output is the file and isn't just true/false


        $err_mesages_arr=json_decode(curl_exec($curl),true);


        
        curl_close($curl);
 
        // var_dump($err_mesages_arr);


 	//=================	



   foreach ($err_mesages_arr as $err_message) {

   		echo $err_message.'<br>';

   }

	 
?>