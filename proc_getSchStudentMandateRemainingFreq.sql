	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchStudentMandateRemainingFreq$$

	CREATE PROCEDURE proc_getSchStudentMandateRemainingFreq (IN  p_mandate_id INT, 
                                                                 p_payroll_week DATE,
                                                                 p_service_date DATE
														    )  

	BEGIN

		 DECLARE v_SessionFreqType varchar(45);
   DECLARE v_SessionFreq, v_PostedSessions int;



   /* Get Madnate Freq.Type  */
   /*==========================================*/
   Select  SessionFrequency,
           SessionFrequencyType

    into v_SessionFreq, v_SessionFreqType
       From SchStudentMandates
   Where Id  = p_mandate_id ;
 
	 

 	 IF v_SessionFreqType = 'Weekly' THEN 

		SELECT count(*) into v_PostedSessions
              FROM WeeklyServices  
              where MandateId = p_mandate_id
              and ScheduleStatusId > 6
              and  PayrollWeek  = p_payroll_week 
    ;          

   ELSEIF v_SessionFreqType = 'Monthly'  THEN 

		SELECT count(*) into v_PostedSessions
              FROM WeeklyServices  
              where MandateId = p_mandate_id
              and ScheduleStatusId > 6
	       	  	AND MONTH(ServiceDate) = MONTH(p_service_date)   
	       	  	AND YEAR(ServiceDate) = YEAR(p_service_date)   
    ;          

   ELSEIF v_SessionFreqType = 'In' THEN 

		SELECT count(*) into v_PostedSessions
              FROM WeeklyServices  
              where MandateId = p_mandate_id
              and ScheduleStatusId > 6
    ;          

 	 END IF;

   select (v_SessionFreq - v_PostedSessions) as AvailableFreq ;
		
	END $$

	DELIMITER ;		
	 