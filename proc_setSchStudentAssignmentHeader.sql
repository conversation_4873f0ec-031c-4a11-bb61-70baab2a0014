

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_setSchStudentAssignmentHeader$$

	CREATE PROCEDURE proc_setSchStudentAssignmentHeader (INp_student_id INT, 
															p_status_id INT,
															p_assignment_id INT,
															p_assignment_type_id INT,
															p_conf_number VARCHAR(32),
															p_start_date DATE,
															p_end_date DATE,
															p_service_type_id INT,
															p_user_id BIGINT)  


BEGIN


	DECLARE v_Existing_ServType_AssignId INT;


	/* Get Assigment ID if Service Type Already Exists 
	  =================================================*/ 	

 	
	SELECT count(*) INTO v_Existing_ServType_AssignId
		FROM SchStudentAssignmentHeader
	WHERE StudentId = p_student_id
	AND   ServiceTypeId = p_service_type_id ;	  

 
	IF (v_Existing_ServType_AssignId > 0) THEN /* Existing Service Type - Update  */
 	
		/*====*/
			UPDATE SchStudentAssignmentHeader
			 
			SET		StatusId = p_status_id,
					AssignmentTypeId = p_assignment_type_id,
					ConfirmationNumber = p_conf_number,
					StartDate = p_start_date,
					EndDate = p_end_date,
					UserId = p_user_id,
					TransDate = NOW()
			WHERE Id = v_Existing_ServType_AssignId ;



		/*====*/


 	ELSE /* New Service Type - Insert */
	
		/*====*/
 					INSERT INTO SchStudentAssignmentHeader
					( 
					StudentId,
					ServiceTypeId,
					StatusId,
					AssignmentTypeId,
					ConfirmationNumber,
					StartDate,
					EndDate,
					UserId,
					TransDate)
					VALUES
					(
					p_student_id,
					p_service_type_id,
					p_status_id,
					p_assignment_type_id,
					p_conf_number,
					p_start_date,
					p_end_date,
					p_user_id,
					NOW() );



		/*====*/
	END IF;
 
END $$

	DELIMITER ;	