

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getClientApprovedRegistrantsSelection$$

CREATE PROCEDURE proc_getClientApprovedRegistrantsSelection (IN p_client_id int)  

BEGIN

 

	
	DECLARE v_Client_Zipcode VARCHAR(5);
	DECLARE v_Client_Latitude VARCHAR(8);
	DECLARE v_Client_Longitude VARCHAR(9);
	DECLARE v_Registrant_Latitude VARCHAR(8);
	DECLARE v_Registrant_Longitude VARCHAR(9);
	
	DECLARE done INT DEFAULT 0;	
	
	/* Get Client Zipcode*/
	/*=================================*/
	Select ZipCode into v_Client_Zipcode
		from Clients
	Where Id = p_client_id;	
	
	/* Get School Latitude/Longitude  */
	/*=================================*/
	Select Latitude, Longitude
		into v_Client_Latitude, v_Client_Longitude
		from ZipCodes
	Where SUBSTRING(v_Client_Zipcode,1,5) = ZipCode;	

	

	create temporary table tmp engine=memory

SELECT a.Id as RegistrantId,
		CONCAT( trim( a.LastName) , ', ', trim( a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
		a.City,
		COALESCE(MobilePhone,'') as MobilePhone,
		COALESCE(HomePhone,'') as HomePhone,
		TypeId,
		Latitude,
		Longitude,
		SUBSTRING(a.ZipCode,1,5) as ZipCode,
        0000.00 as ProximityToClient,
		
		(SELECT count(*) from RegistrantCredItems d
			where a.Id = d.RegistrantId
			AND ComplianceLevelId = 1 ) as CredItemsNonCompliant,    
		(SELECT count(*) from RegistrantCredItems d
            where a.Id = d.RegistrantId
			AND ComplianceLevelId != 1 ) as CredItemsCompliant

		
	FROM 	Registrants a,
			Clients b,
			ZipCodes c,
            RegistrantTypes f
	where 	a.TypeId = f.Id
			and a.StatusId = 1
			and b.Id = p_client_id 
			and SUBSTRING(a.ZipCode,1,5)  = c.ZipCode
           and not exists (SELECT 1 from ClientApprovedRegistrants d
                         WHERE d.ClientId = p_client_id 
                         AND a.Id = d.RegistrantId ) ;  
                          	
 
	
UPDATE tmp a, ZipCodes c   
	Set ProximityToClient = (
	3959 *
	acos(
		cos(radians(v_Client_Latitude)) *
		cos(radians(c.Latitude)) *
		cos(radians(c.Longitude) - radians(v_Client_Longitude)) +
		sin(radians(v_Client_Latitude)) *
		sin(radians(c.Latitude))
		) 
	) 
Where a.ZipCode = c.ZipCode;
	
	
	SELECT 	RegistrantId,
			RegistrantName,
 			CredItemsNonCompliant,
			CredItemsCompliant,
			City,
			CONCAT ( '<p>' , trim(MobilePhone) , '</br> ',  trim(HomePhone), '</p>' ) as PhoneNumbers,
			TypeId,
			ZipCode,
			ProximityToClient,
			(SELECT  CASE count(*) 
                WHEN 0 THEN ''
					ELSE group_concat( CredItem SEPARATOR ', ' )
				END as NonComplList
			FROM RegistrantCredItems a, CredentialingItems b
				WHERE a.RegistrantId = tmp.RegistrantId
				AND b.Id = a.CredItemId
				AND ComplianceLevelId =1 )	 as NonComplList						
						
	From tmp
	Order By ProximityToClient, RegistrantName;

	drop temporary table if exists tmp;	
	
END $$

DELIMITER ;	