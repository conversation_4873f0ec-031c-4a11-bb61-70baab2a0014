

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getCredItemsClientMandatorySelected1$$


CREATE  PROCEDURE proc_getCredItemsClientMandatorySelected1 (IN p_client_id INT, 															
																p_service_type_id INT )
											  
BEGIN


	
	/*==========================================================================================*/
	/* Get All Unselected Client Mandatory Credentialing Items for a gived Client/Client Unit   */ 
	/*==========================================================================================*/
	
	create temporary table tmp 

	(
		id int,
		CredItemDesc varchar(96),
		CondFL int,
		AllUnits int,
		UnitIds varchar(96),
		UnitNames varchar(256)	
	);
		
		INSERT INTO tmp

		SELECT DISTINCT a.Id as id, 
		a.CredItemDesc,
		'0' as CondFL,
		'1',
		'',
		''
		FROM  CredentialingItems a
		WHERE  EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b
						WHERE b.ClientId = p_client_id
                        AND a.id = b.MandatoryCredItemId
						AND b.ServiceTypeId = p_service_type_id
						AND CondFL = 0)  ;						
	 
		INSERT INTO tmp
		
		SELECT  DISTINCT c.Id as id, 
		c.ConditionalItemDesc as CredItemDesc,
		'1' as CondFL,
		'1',
		'',
		''		
		FROM  CredentialingItemsConditionalHeader c
		WHERE  EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b
						WHERE b.ClientId = p_client_id
						AND b.ServiceTypeId = p_service_type_id
                        AND c.id = b.MandatoryCredItemId
						AND CondFL = 1)  	;					
		
		
		/* Set Selected for All Units Flaf
		===================================== */
		 
		UPDATE tmp a, CredentialingItemsClientMandatory b
		 	SET a.AllUnits = '1',
				a.UnitIds = '',
				a.UnitNames = ''	
			WHERE b.ClientId = p_client_id
			AND   b.ServiceTypeId = p_service_type_id
			AND   a.id = b.MandatoryCredItemId
			AND   b.ClientUnitId = '0'
				;
		 
		/* Set Selected for Specific Units Flag
		========================================= */
		 
		UPDATE tmp a, CredentialingItemsClientMandatory d
		 	SET a.AllUnits = '0',
				
				a.UnitIds = (Select group_concat( b.ClientUnitId
                                         SEPARATOR ', ' ) 
								FROM CredentialingItemsClientMandatory b
							WHERE b.ClientId = p_client_id
							AND   b.ServiceTypeId = p_service_type_id
							AND   a.id = b.MandatoryCredItemId
							),	
				a.UnitNames = (Select group_concat( UnitName
                                         SEPARATOR ', ' ) 
								FROM ClientUnits c, CredentialingItemsClientMandatory b
							WHERE b.ClientId = p_client_id
							AND   b.ServiceTypeId = p_service_type_id
							AND   a.id = b.MandatoryCredItemId
							AND   b.ClientUnitId = c.Id
							)	
		
						WHERE d.ClientId = p_client_id
						AND   d.ServiceTypeId = p_service_type_id
						AND   a.id = d.MandatoryCredItemId
						AND   d.ClientUnitId != '0'
					;
			 
		
	
	SELECT * FROM tmp  
	ORDER BY CredItemDesc ;	
	 
	
	
	drop temporary table if exists tmp;
	
 
END$$

DELIMITER ;	
