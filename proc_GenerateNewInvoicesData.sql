

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_GenerateNewInvoicesData$$

CREATE PROCEDURE proc_GenerateNewInvoicesData ()  

BEGIN
	
	DECLARE v_SearchId VARCHAR(25);
	DECLARE v_PayrollWeek DATE;
	DECLARE v_TotalUnits DEC(6,2);
	DECLARE v_TotalAmount DEC(10,2);
	DECLARE v_InvoiceNumber BIGINT;
	DECLARE v_ClientId INT;

	DECLARE done INT DEFAULT 0;

	
	/*============================================*/
	
	DECLARE cur1 CURSOR FOR
      SELECT  DISTINCT ClientId 
	FROM WeeklyServices
		WHERE ScheduleStatusId = 8
		AND BillingExtractDate IS NULL					 
		;
       
	declare continue handler for not found set done := true;   
	
	create temporary table tmp 

	(
		ScheduleId INT,
		ClientId INT, 
		ClientUnitId INT, 
		ServiceTypeId INT, 
		ServiceTypeDesc VARCHAR(25),
		RegistrantId INT,
		PayrollWeek DATE, 
		ServiceDate DATE, 
		TotalHours DEC(6,2), 
		ShiftId INT,
		ShiftName VARCHAR(25),
		UnitName VARCHAR(30),
		ContractCategoryId INT,
		BillRate DEC(6,2),
		PayRate DEC(6,2),
		DateRangeTypeId INT,
		ShiftType VARCHAR(2)	
	);


	OPEN cur1;
	
	read_loop: LOOP
 
       FETCH cur1 INTO v_ClientId;
   
		IF done THEN
			LEAVE read_loop;
		END IF;	

	/* Load WD Rates
	=======================*/	
	
	INSERT INTO tmp
	
	SELECT 	a.Id as ScheduleId,
			a.ClientId, 
			a.ClientUnitId, 
			a.ServiceTypeId, 
			ServiceTypeDesc,
			RegistrantId,
			PayrollWeek, 
			ServiceDate, 
			TotalHours, 
			a.ShiftId,
			ShiftName,
			UnitName,
			b.ContractCategoryId,
			BillRate,
			c.PayRate,
			d.DateRangeTypeId,
			'WD' as ShiftType
	FROM WeeklyServices a,
	     ClientUnits b,
		 ServiceContractCategoryRates c,
		 ServiceContractCategoryRateDateRanges d,
		 ServiceTypes f,
		 Shifts e
	WHERE ScheduleStatusId = 8
	AND a.ClientUnitId = b.Id 
	AND BillingExtractDate IS NULL
	AND b.ContractCategoryId = c.ContractCategoryId
	AND a.ShiftId = c.ShiftId 
	AND RateTypeId = 1
	AND a.ServiceTypeId = c.ServiceTypeId
	AND DAYOFWEEK(ServiceDate) in (2,3,4,5,6)	
	AND ServiceDate between d.StartDate and d.EndDate
	AND c.RateDateRangeId = d.Id
	AND a.ServiceTypeId = f.Id
	AND a.ShiftId = e.ShiftId
	AND a.ClientId = v_ClientId
	;	
	
	
	/* Load WE Rates
	=======================*/	
	
	INSERT INTO tmp
	
	SELECT 	a.Id as ScheduleId,
			a.ClientId, 
			a.ClientUnitId, 
			a.ServiceTypeId, 
			ServiceTypeDesc,
			RegistrantId,
			PayrollWeek, 
			ServiceDate, 
			TotalHours, 
			a.ShiftId,
			ShiftName,
			UnitName,
			b.ContractCategoryId,
			BillRate,
			c.PayRate,
			d.DateRangeTypeId,
			'WE' as ShiftType
	FROM WeeklyServices a,
	     ClientUnits b,
		 ServiceContractCategoryRates c,
		 ServiceContractCategoryRateDateRanges d,
		 ServiceTypes f,
		 Shifts e
	WHERE ScheduleStatusId = 8
	AND a.ClientUnitId = b.Id 
	AND BillingExtractDate IS NULL
	AND b.ContractCategoryId = c.ContractCategoryId
	AND a.ShiftId = c.ShiftId 
	AND RateTypeId = 2
	AND a.ServiceTypeId = c.ServiceTypeId
	AND DAYOFWEEK(ServiceDate) in (1,7)	
	AND ServiceDate between d.StartDate and d.EndDate
	AND c.RateDateRangeId = d.Id 
	AND a.ServiceTypeId = f.Id
	AND a.ShiftId = e.ShiftId
	AND a.ClientId = v_ClientId
	
	;	

	/* Add New Invoice Header Record
	====================================*/

	SET v_SearchId =  RAND( );
	SET v_PayrollWeek = adddate(curdate(), INTERVAL 7-DAYOFWEEK(curdate()) DAY);  
	
	INSERT INTO InvoiceHeader
	(
		ClientId,
		SearchId,
		StatusId,
		InvoiceDate,
		PayrollWeek
	)	
	VALUES (
				v_ClientId,
				v_SearchId,
				'1',
				curdate(),
				v_PayrollWeek
			) ;

			
	SET @mycount := 0; 
	 
	INSERT INTO InvoiceDetails
	(
		InvoiceNumber,
		LineNumber, 
		StatusId,
		ScheduleId,
		ShiftType,
		BillRate,
		PayRate,
		Units,
		Amount
	)	
		SELECT b.Id,
		(@mycount := @mycount+1),
		'1',
		a.ScheduleId,
		a.ShiftType,
		a.BillRate,
		a.PayRate,
		a.TotalHours,
		ROUND((a.BillRate * a.TotalHours),2)		
	FROM tmp a, InvoiceHeader b
	WHERE b.SearchId = v_SearchId  ;
	
	
	/*============================*/
	SET v_InvoiceNumber = (SELECT Id from InvoiceHeader WHERE SearchId = v_SearchId );
	/*============================*/
	
	SET v_TotalUnits =  (SELECT SUM(Units) 
	                       FROM  InvoiceDetails 
						   WHERE  InvoiceNumber = v_InvoiceNumber);
	
	SET v_TotalAmount =  (SELECT SUM(Amount) 
	                       FROM  InvoiceDetails 
						   WHERE  InvoiceNumber = v_InvoiceNumber);

	
	UPDATE InvoiceHeader 
		SET TotalUnits = v_TotalUnits,
			TotalAmount = v_TotalAmount
	WHERE Id = v_InvoiceNumber;
	  	
 	
	
	/*==================================*/
	
	
	/*
	SELECT 	DATE_FORMAT( a.ServiceDate, '%m/%d/%y' ) as ServiceDateString,
			CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,
			UnitName,
			ServiceTypeDesc,
			ShiftName,
			ShiftType,
			BillRate,
			TotalHours,
			ROUND((BillRate * TotalHours),2) as Amount
	FROM tmp a, Registrants b 
	WHERE a.RegistrantId = b.Id
	ORDER BY ServiceDate, ShiftId
	;
	*/
	
   END LOOP;
   CLOSE cur1; 
	
	

	/*==================================*/
	
	UPDATE WeeklyServices a
		SET BillingExtractDate = NOW()
	WHERE EXISTS (SELECT 1 FROM tmp b
					WHERE a.Id = b.ScheduleId
				 );	
	 
	
	Select * from  WeeklyServices a
	WHERE EXISTS (SELECT 1 FROM tmp b
					WHERE a.Id = b.ScheduleId
				 );	


				 /*==================================*/

	
	
	drop temporary table if exists tmp;		

	
END $$

DELIMITER ;	