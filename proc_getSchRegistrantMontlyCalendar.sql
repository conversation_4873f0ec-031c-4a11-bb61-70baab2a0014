


/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchRegistrantMontlyCalendar$$

CREATE PROCEDURE proc_getSchRegistrantMontlyCalendar (IN 	p_registrant_id INT, 
															p_start_date DATE
												)  

BEGIN

	DECLARE v_RowNumber, v_ColumnNum, v_DayIndex, v_StartDayIndex, v_EndDayIndex, v_GrpSize, v_StudentId INT;
	DECLARE v_Date, v_Date1,  v_LastDate, v_ServiceDate DATE;
	DECLARE v_ServiceDesc, v_TextColor, v_BackgroundColor  VARCHAR(256);
	DECLARE done INT DEFAULT 0;
	DECLARE v_CellContent, vTableCell_1, vTableCell_2, vTableCell_3, vTableCell_4, vTableCell_5, vTableCell_6, vTableCell_7 VARCHAR(512);
	DECLARE vTableCell_8, vTableCell_9, vTableCell_10, vTableCell_11, vTableCell_12, vTableCell_13, vTableCell_14 VARCHAR(512);
	DECLARE vTableCell_15, vTableCell_16, vTableCell_17, vTableCell_18, vTableCell_19, vTableCell_20, vTableCell_21 VARCHAR(512);
	DECLARE vTableCell_22, vTableCell_23, vTableCell_24, vTableCell_25, vTableCell_26, vTableCell_27, vTableCell_28 VARCHAR(512);
	DECLARE vTableCell_29, vTableCell_30, vTableCell_31, vTableCell_32, vTableCell_33, vTableCell_34, vTableCell_35  VARCHAR(512);
	DECLARE vTableCell_36, vTableCell_37, vTableCell_38, vTableCell_39, vTableCell_40, vTableCell_41, vTableCell_42  VARCHAR(512);
	
	/*============================================*/

	DECLARE cur1 CURSOR FOR
	SELECT  COUNT(*) as GroupSize,
			ServiceDate,
			CONCAT('(',DATE_FORMAT( StartTime, '%l:%i %p' ),'-',DATE_FORMAT( EndTime, '%l:%i %p' ),') ',TotalHours,' Hrs - ', b.ServiceTypeDesc, ' (', f.RegistrantTypeDesc,')') as ServiceDesc,	
			TextColor,
			BackgroundColor,
			StudentId
	FROM 	WeeklyServices a, 
			SchServiceTypes b,
			RegistrantTypes f, 
			ScheduleStatuses c
	WHERE RegistrantId = p_registrant_id
	AND ServiceDate between p_start_date and v_LastDate	
	AND ScheduleStatusId = c.Id
	AND a.ServiceTypeId = b.Id
	AND b.RegistrantTypeId = f.Id
	GROUP BY ServiceDate, StartTime
	;

	declare continue handler for not found set done := true;   
	
	
 	
	/*============================================*/
	
	create temporary table tmp 

	(
		RowNumber int,
		ColumnNum int,
		CellContent text,
		ServiceDate date
	
	);
	
	
	/* Set Starting Date/Day Index  
	===============================*/
	 
	SELECT DAYOFWEEK(p_start_date) into v_DayIndex;
	SELECT DAYOFWEEK(p_start_date) into v_StartDayIndex;
	SELECT p_start_date  into v_Date;
	SELECT LAST_DAY(p_start_date) into v_LastDate;
	SELECT 1 into v_RowNumber;
	SELECT DAYOFMONTH(v_Date) into v_CellContent;  
	 
	/*====================================================*/


	
	WHILE v_Date  <= v_LastDate DO

		 
		/*Insert into tmp table
		============================ */ 
		INSERT INTO tmp
		(
			RowNumber,
			ColumnNum,
			CellContent,
			ServiceDate
		)		 
		VALUES	(
					v_RowNumber,
					v_DayIndex,
					/*v_CellContent,*/
					CONCAT( '<span class="calendar-date">', v_CellContent, '</span>'),
					v_Date
				);	

		/*  Increment v_DayIndex, 
		If > 7, set v_DayIndex, v_RowNumber + 1  
		============================  */
	
		SET v_DayIndex = v_DayIndex + 1;
		SET v_Date =  DATE_ADD( v_Date, INTERVAL 1 DAY);
		SET v_CellContent = DAYOFMONTH(v_Date);  
		 
		IF (v_DayIndex > 7) THEN 
		
			SET v_DayIndex = 1;
			SET v_RowNumber = v_RowNumber + 1;
		
		END IF;
		 
		 
		
	END WHILE;	
	 
	 
	/* Save End Day Index   
	======================================= */
	 SET v_EndDayIndex = v_DayIndex - 1;
	
	/* Add "Missing" Columns from 1st Row  
	======================================= */
	
	IF (v_StartDayIndex > 1) THEN 

		SET v_DayIndex = 1;
	
		WHILE v_DayIndex  < v_StartDayIndex DO
	
			INSERT INTO tmp
			(
			RowNumber,
			ColumnNum,
			CellContent
			)		 
			VALUES	(
					1,
					v_DayIndex,
					'<td><span class="calendar-date">&nbsp;</span></td>'
					);	
		
			SET v_DayIndex = v_DayIndex + 1;
			
		END WHILE;
		
	END IF;
	
	/* Add "Missing" Columns from Last Month Row  
	======================================= */
	
	IF (v_EndDayIndex < 7) THEN 

		SET v_DayIndex = v_EndDayIndex + 1;
	
		WHILE v_DayIndex  <= 7 DO
	
			INSERT INTO tmp
			(
			RowNumber,
			ColumnNum,
			CellContent
			)		 
			VALUES	(
					v_RowNumber,
					v_DayIndex,
					'<td><span class="calendar-date">&nbsp;</span></td>'
					);	
		
			SET v_DayIndex = v_DayIndex + 1;
			
		END WHILE;
		
	END IF;

	/* Add "Missing" Columns to Row 6 if Last Month Row = 5  
	======================================= */
	
	IF (v_RowNumber < 6) THEN 

		SET v_DayIndex = 1;
		SET v_RowNumber = 6;
	
		WHILE v_DayIndex  <= 7 DO
	
			INSERT INTO tmp
			(
			RowNumber,
			ColumnNum,
			CellContent
			)		 
			VALUES	(
					v_RowNumber,
					v_DayIndex,
					'<td><span class="calendar-date">&nbsp;</span></td>'
					);	
		
			SET v_DayIndex = v_DayIndex + 1;
			
		END WHILE;
		
	END IF;
	
	
	/* Add Registrant's Service Description to Table Cells       
	======================================================= */	
	
		 
	OPEN cur1;
	
	read_loop: LOOP
 
       FETCH cur1 INTO v_GrpSize, v_ServiceDate, v_ServiceDesc, v_TextColor, v_BackgroundColor, v_StudentId;
 
		IF done THEN
			LEAVE read_loop;
		END IF;

		IF (v_StudentId != 0) THEN

			SET v_ServiceDesc = CONCAT(v_ServiceDesc, ' (',v_GrpSize, ')'); 

		END IF;

	
		UPDATE tmp
			SET CellContent = CONCAT(CellContent, '</br><div style= "font-size: 10px; padding: 5px; border:2px solid #a1a1a1; box-shadow: 10px 10px 5px #888888; border-radius:15px;  background:',v_BackgroundColor,';color:',v_TextColor,';">',v_ServiceDesc,'</div>')
		WHERE ServiceDate = v_ServiceDate;
		
	END LOOP;
    CLOSE cur1; 

	/* Add Formatting Table Cells       
	======================================================= */	
		UPDATE tmp
			SET CellContent = CONCAT('<td>',CellContent,'</td>')
		WHERE ServiceDate IS NOT NULL;
	
	
	
	/*======================================================*/
	
	/* Load Table Row # 1
	========================== */
	 
	SELECT CellContent into vTableCell_1 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 1; 
	 
	SELECT CellContent into vTableCell_2 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 2; 
	
	SELECT CellContent into vTableCell_3 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 3; 

	SELECT CellContent into vTableCell_4 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 4; 
	
	SELECT CellContent into vTableCell_5 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 5; 

	SELECT CellContent into vTableCell_6 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 6; 

	SELECT CellContent into vTableCell_7 
		FROM tmp 
	WHERE RowNumber = 1 and ColumnNum = 7; 
	 
	/* Load Table Row # 2
	========================== */
	 
	SELECT CellContent into vTableCell_8 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 1; 
	 
	SELECT CellContent into vTableCell_9 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 2; 
	
	SELECT CellContent into vTableCell_10 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 3; 

	SELECT CellContent into vTableCell_11 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 4; 
	
	SELECT CellContent into vTableCell_12 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 5; 

	SELECT CellContent into vTableCell_13 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 6; 

	SELECT CellContent into vTableCell_14 
		FROM tmp 
	WHERE RowNumber = 2 and ColumnNum = 7; 
	 
	/* Load Table Row # 3
	========================== */
	 
	SELECT CellContent into vTableCell_15 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 1; 
	 
	SELECT CellContent into vTableCell_16 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 2; 
	
	SELECT CellContent into vTableCell_17 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 3; 

	SELECT CellContent into vTableCell_18 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 4; 
	
	SELECT CellContent into vTableCell_19 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 5; 

	SELECT CellContent into vTableCell_20 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 6; 

	SELECT CellContent into vTableCell_21 
		FROM tmp 
	WHERE RowNumber = 3 and ColumnNum = 7; 
	 
	/* Load Table Row # 4
	========================== */
	 
	SELECT CellContent into vTableCell_22 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 1; 
	 
	SELECT CellContent into vTableCell_23 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 2; 
	
	SELECT CellContent into vTableCell_24 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 3; 

	SELECT CellContent into vTableCell_25 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 4; 
	
	SELECT CellContent into vTableCell_26 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 5; 

	SELECT CellContent into vTableCell_27 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 6; 

	SELECT CellContent into vTableCell_28 
		FROM tmp 
	WHERE RowNumber = 4 and ColumnNum = 7; 
	 
	/* Load Table Row # 5
	========================== */
	 
	SELECT CellContent into vTableCell_29 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 1; 
	 
	SELECT CellContent into vTableCell_30 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 2; 
	
	SELECT CellContent into vTableCell_31 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 3; 

	SELECT CellContent into vTableCell_32 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 4; 
	
	SELECT CellContent into vTableCell_33 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 5; 

	SELECT CellContent into vTableCell_34 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 6; 

	SELECT CellContent into vTableCell_35 
		FROM tmp 
	WHERE RowNumber = 5 and ColumnNum = 7; 

	/* Load Table Row # 6
	========================== */
	 
	SELECT CellContent into vTableCell_36 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 1; 
	 
	SELECT CellContent into vTableCell_37 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 2; 
	
	SELECT CellContent into vTableCell_38 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 3; 

	SELECT CellContent into vTableCell_39 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 4; 
	
	SELECT CellContent into vTableCell_40 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 5; 

	SELECT CellContent into vTableCell_41 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 6; 

	SELECT CellContent into vTableCell_42 
		FROM tmp 
	WHERE RowNumber = 6 and ColumnNum = 7; 	
	/*=======================================*/
	
	  
	 
	SELECT CONCAT(MONTHNAME(p_start_date),' ',SUBSTRING(p_start_date, 1, 4))  as MonthYearName,
		   vTableCell_1 as TableCell_1,
		   vTableCell_2 as TableCell_2,	
		   vTableCell_3 as TableCell_3,	
		   vTableCell_4 as TableCell_4,	
		   vTableCell_5 as TableCell_5,	
		   vTableCell_6 as TableCell_6,	
		   vTableCell_7 as TableCell_7,	
		   vTableCell_8 as TableCell_8,	
		   vTableCell_9 as TableCell_9, 	
		   vTableCell_10 as TableCell_10, 	
		   vTableCell_11 as TableCell_11,	
		   vTableCell_12 as TableCell_12,	
		   vTableCell_13 as TableCell_13,	
		   vTableCell_14 as TableCell_14,	
		   vTableCell_15 as TableCell_15,	
		   vTableCell_16 as TableCell_16,	
		   vTableCell_17 as TableCell_17,	
		   vTableCell_18 as TableCell_18,	
		   vTableCell_19 as TableCell_19,	
		   vTableCell_20 as TableCell_20,	
		   vTableCell_21 as TableCell_21,	
		   vTableCell_22 as TableCell_22,	
		   vTableCell_23 as TableCell_23,	
		   vTableCell_24 as TableCell_24,	
		   vTableCell_25 as TableCell_25,	
		   vTableCell_26 as TableCell_26,	
		   vTableCell_27 as TableCell_27,	
		   vTableCell_28 as TableCell_28,	
		   vTableCell_29 as TableCell_29,	
		   vTableCell_30 as TableCell_30,	
		   vTableCell_31 as TableCell_31,	
		   vTableCell_32 as TableCell_32,	
		   vTableCell_33 as TableCell_33,	
		   vTableCell_34 as TableCell_34,	
		   vTableCell_35 as TableCell_35,
		   vTableCell_36 as TableCell_36,
		   vTableCell_37 as TableCell_37,
		   vTableCell_38 as TableCell_38,
		   vTableCell_39 as TableCell_39,
		   vTableCell_40 as TableCell_40,
		   vTableCell_41 as TableCell_41,
		   vTableCell_42 as TableCell_42
		   ;	
	 
		
	drop temporary table if exists tmp;


	 
	
END $$

DELIMITER ;	