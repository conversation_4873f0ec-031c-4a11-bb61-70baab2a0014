

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSpecialtiesClientMandatorySelected1$$


CREATE  PROCEDURE proc_getSpecialtiesClientMandatorySelected1 (IN p_client_id INT, 															
																p_service_type_id INT )
											  
BEGIN


	
	/*==========================================================================================*/
	/* Get All Unselected Client Mandatory Credentialing Items for a gived Client/Client Unit   */ 
	/*==========================================================================================*/
	
	create temporary table tmp 

	(
		id int,
		SpecialtyDesc varchar(96),
		AllUnits int,
		UnitIds varchar(96),
		UnitNames varchar(256)	
	);
		
		INSERT INTO tmp

		SELECT DISTINCT a.Id as id, 
		a.SpecialtyDesc,
		'1',
		'',
		''
		FROM  Specialties a
		WHERE  EXISTS (SELECT 1 FROM SpecialtiesClientMandatory b
						WHERE b.ClientId = p_client_id
                        AND a.id = b.SpecialtyId
						AND b.ServiceTypeId = p_service_type_id )  ;
					 					
	 
		
		
		/* Set Selected for All Units Flag
		===================================== */
		 
		UPDATE tmp a, SpecialtiesClientMandatory b
		 	SET a.AllUnits = '1',
				a.UnitIds = '',
				a.UnitNames = ''	
			WHERE b.ClientId = p_client_id
			AND   b.ServiceTypeId = p_service_type_id
			AND   a.id = b.SpecialtyId
			AND   b.ClientUnitId = '0'
				;
		 
		/* Set Selected for Specific Units Flag
		========================================= */
		 
		UPDATE tmp a, SpecialtiesClientMandatory d
		 	SET a.AllUnits = '0',
				
				a.UnitIds = (Select group_concat( b.ClientUnitId
                                         SEPARATOR ', ' ) 
								FROM SpecialtiesClientMandatory b
							WHERE b.ClientId = p_client_id
							AND   b.ServiceTypeId = p_service_type_id
							AND   a.id = b.SpecialtyId
							),	
				a.UnitNames = (Select group_concat( UnitName
                                         SEPARATOR ', ' ) 
								FROM ClientUnits c, SpecialtiesClientMandatory b
							WHERE b.ClientId = p_client_id
							AND   b.ServiceTypeId = p_service_type_id
							AND   a.id = b.SpecialtyId
							AND   b.ClientUnitId = c.Id
							)	
		
						WHERE d.ClientId = p_client_id
						AND   d.ServiceTypeId = p_service_type_id
						AND   a.id = d.SpecialtyId
						AND   d.ClientUnitId != '0'
					;
			 
		
	
	SELECT * FROM tmp  
	ORDER BY SpecialtyDesc ;	
	 
	
	
	drop temporary table if exists tmp;
	
 
END$$

DELIMITER ;	
