<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$SchoolId = $_POST['SchoolId'];
	$StatusId = $_POST['StatusId'];	
	$AssignmentId = $_POST['AssignmentId'];
	$AssignmentTypeId = $_POST['AssignmentTypeId'];
	$ConfirmationNumber = $_POST['ConfirmationNumber'];
	$StartDate = $_POST['StartDate'];
	$EndDate = $_POST['EndDate'];
	$ServiceTypeId = $_POST['ServiceTypeId'];

	$UserId = $_POST['UserId'];


	$result = $rcr_transaction->setSchSchoolAssignmentHeader(	$SchoolId,
																$StatusId,
																$AssignmentId,
																$AssignmentTypeId,
																$ConfirmationNumber,
																$StartDate,
																$EndDate,
																$ServiceTypeId,
																$UserId ); 

	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
