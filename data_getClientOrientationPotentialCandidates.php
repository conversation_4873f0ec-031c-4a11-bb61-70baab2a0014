<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$ClientId = $_GET['ClientId'];
	$ClientUnitId = $_GET['ClientUnitId'];
	$ServiceTypeId = $_GET['ServiceTypeId'];


	$result = $rcr_transaction->getClientOrientationPotentialCandidates($ClientId, $ClientUnitId, $ServiceTypeId);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
  

?>
