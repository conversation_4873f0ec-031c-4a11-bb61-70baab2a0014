<?php
require_once('DB.php');

class TestDataHandler {
    var $connection;
    
    function TestDataHandler() {
        include('db_login.php');
        
        $this->connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
        if (DB::isError($this->connection)){
            die("Could not connect to the database: <br />".DB::errorMessage($this->connection));
        }
        
        $this->connection->query('SET NAMES utf8');
    }
    
    function getUsers() {
        $query = "SELECT id, name, email FROM users";
        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
        
        if (DB::isError($result)){
            die("Could not query the database:<br />$query ".DB::errorMessage($result));
        }
        
        return $result;
    }
    
    function addUser($name, $email) {
        $name = $this->connection->escapeSimple($name);
        $email = $this->connection->escapeSimple($email);
        
        $query = "INSERT INTO users (name, email) VALUES ('$name', '$email')";
        $result = $this->connection->query($query);
        
        if (DB::isError($result)){
            die("Could not insert user:<br />$query ".DB::errorMessage($result));
        }
        
        return $result;
    }
    
    function closeConnection() {
        $this->connection->disconnect();
    }
}
?>
