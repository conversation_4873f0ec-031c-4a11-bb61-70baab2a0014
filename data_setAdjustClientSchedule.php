<?php 


require "ewDataHandler.php";
  
$rcr_transaction = new dataHandler(); 

$ScheduleId =  $_POST['ScheduleId'];
$StartTime =  $_POST['StartTime'];
$EndTime =  $_POST['EndTime'];	
$LunchHour =  $_POST['LunchHour'];	
$TotalHours =  $_POST['TotalHours'];	
$ServiceTypeId =  $_POST['ServiceTypeId'];	
$ClientUnitId =  $_POST['ClientUnitId'];	


$UserId = $_POST['UserId'];

if (!$PatientId ) {
	$PatientId = 0;
}


$result = $rcr_transaction->setAdjustClientSchedule($ScheduleId,
													$StartTime,
													$EndTime,
													$LunchHour,
													$TotalHours,
													$ClientUnitId,
													$ServiceTypeId,
													$UserId); 
												
												
// Add New Schedule Message
//================================= 
$Msg = 'Schedule was MODIFIED!';
$result1 = $rcr_transaction->setClientWklyScheduleMsg(
								$ScheduleId, 
								$Msg,
								$HighPriority,	  
								$UserId); 												

$rcr_transaction->disconnectDB (); 

echo $result;

?>
