

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_UpdateRegistrantsClientRequiredCredItems$$

CREATE PROCEDURE proc_UpdateRegistrantsClientRequiredCredItems (p_user_id INT)
																

BEGIN

	create temporary table tmp engine=memory

	SELECT	b.ClientId,
	        b.ClientUnitId,
	        b.ServiceTypeId,
	        b.MandatoryCredItemId, 
	        c.RegistrantId,
	        0 as RegistrantTypeId,
	        b.CondFL
	  	 
	FROM	CredentialingItemsClientMandatory b,
			ClientApprovedRegistrants c
				  	
                WHERE   b.ClientId = c.ClientId
                AND   	((b.ClientUnitId = c.ClientUnitId) || (b.ClientUnitId = '0'))                
                AND   	b.ServiceTypeId = c.ServiceTypeId
				AND   	c.Status != '2'	;
 		

 	UPDATE tmp a, Registrants b
 		SET a.RegistrantTypeId = b.TypeId
 	WHERE a.RegistrantId = b.Id;		

 	/*=========================================*/
	/* Do not ADD if Cred. Item IS MANDATORY  */
	/*=========================================*/
	
	/*
 	DELETE FROM tmp
 	WHERE EXISTS (SELECT 1 FROM CredentialingItemsMandatory a 
 					WHERE tmp.RegistrantTypeId = a.RegistrantTypeId
 					AND   tmp.MandatoryCredItemId = a.MandatoryCredItemId );
 	*/				 	
	/*============================================================*/
	/* Do not ADD if Cred. Item ALREADY EXISTS for the Registrant */
	/*============================================================*/

	DELETE FROM tmp
 	WHERE	 EXISTS (SELECT 1 FROM RegistrantCredItems a 
 					WHERE tmp.RegistrantId = a.RegistrantId
 					AND   tmp.MandatoryCredItemId = a.CredItemId );


	/* Get All Registrants Approved for given Client/Unit/Service Type 
     =====================================*/

	/*==========================*/
	/* Insert New Cred. Items  te*/
	/* (Non-Conditional Items ONLY) */
	/*==========================*/
	 
	INSERT INTO RegistrantCredItems
			(
			RegistrantId,
			StatusId, 
			CredItemId,
			UserId,
			TransDate
			)
		
		SELECT DISTINCT RegistrantId,
						'2',
						MandatoryCredItemId,
						p_user_id,
						NOW()  
		FROM tmp
		WHERE CondFL = '0'; 

		DELETE FROM tmp
			WHERE CondFL = '0';
	 			
	/*==========================*/
	/* Set Cred. Item to Header Value*/
	/* (Conditional Items ONLY) */
	/*==========================*/

	/*
	UPDATE tmp a, CredentialingItemsConditionalDetails c
 		SET a.MandatoryCredItemId = c.ConditionalItemId
 	WHERE a.MandatoryCredItemId = c.CredItemId ;
 	*/

	/*============================================================*/
	/* Do not ADD if Cred. Item ALREADY EXISTS for the Registrant */
	/*============================================================*/

	DELETE FROM tmp
 	WHERE	 EXISTS (SELECT 1 FROM RegistrantCredentialingItemsConditional a 
 					WHERE tmp.RegistrantId = a.RegistrantId
 					AND   tmp.MandatoryCredItemId = a.ConditionalItemId );


	
	/*================================================================================== */
	/* Insert into "RegistrantCredentialingItemsConditional" DEFAULT Conditonal Items      */
	/*================================================================================== */
	
	 
	INSERT INTO RegistrantCredentialingItemsConditional
	
	SELECT RegistrantId,
		   a.Id,
		   a.ConditionalSwitchDefault	
	FROM CredentialingItemsConditionalHeader a, tmp b
		WHERE a.Id = b.MandatoryCredItemId ;

	/*============================================================================= */
	/* Insert Cred. Items (Conditonal )   */
	/*============================================================================= */

	   
	INSERT INTO RegistrantCredItems
		(
		RegistrantId,
		CredItemId,
		UserId,
		TransDate
		)
	SELECT 	 
			a.RegistrantId,
			c.CredItemId,
			p_user_id,
			NOW()
	FROM  	tmp a,
			RegistrantCredentialingItemsConditional b, 
		  	CredentialingItemsConditionalDetails c
		WHERE b.RegistrantId = a.RegistrantId
		AND b.ConditionalItemId = a.MandatoryCredItemId
		AND b.ConditionalItemId = c.ConditionalItemId
		AND b.ConditionalSwitch = c.ConditionalSwitch ;


	drop temporary table if exists tmp;
	

END $$

DELIMITER ;	