<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$ClientId = $_POST['ClientId'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	
	$UserId = $_POST['UserId'];
	
	$CredItems = $_POST['CredItems'];
	$CredItems=json_decode($CredItems,true);

	$CredItemsRem = $_POST['CredItemsRem'];
	$CredItemsRem=json_decode($CredItemsRem,true);

	 
	$Specialties = $_POST['Specialties'];
	$Specialties=json_decode($Specialties,true);

	$SpecialtiesRem = $_POST['SpecialtiesRem'];
	$SpecialtiesRem=json_decode($SpecialtiesRem,true);
	 
	
	/* Set Client service Type Mandatory Cred. Items  
	--------------------------------------------------*/
	
	$result = $rcr_transaction->setCredItemsClientMandatory($ClientId,
															$ServiceTypeId,
															$CredItems,	
															$CredItemsRem,
															$UserId); 

	/* Set Client Service Type Mandatory Specialties 
	------------------------------------------------*/
	 
	$result = $rcr_transaction->setSpecialtiesClientMandatory(	$ClientId,
																$ServiceTypeId,
																$Specialties,	
																$SpecialtiesRem,
																$UserId); 
	
	$result1 = $rcr_transaction->setUpdateRegistrantsClientRequiredCredItems( $UserId ); 
																			 

	 
	$rcr_transaction->disconnectDB (); 


		
		
	echo $result;

?>
