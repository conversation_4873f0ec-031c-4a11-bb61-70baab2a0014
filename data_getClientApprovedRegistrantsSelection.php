<?php 

require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$ClientId = $_GET['ClientId'];
$RegistrantTypeId = $_GET['RegistrantTypeId'];
$SpecialtyId = $_GET['SpecialtyId'];
$NameSeach = $_GET['NameSeach'];


$result = $rcr_transaction->getClientApprovedRegistrantsSelection(	$ClientId,
																	$RegistrantTypeId,
																	$SpecialtyId,
																	$NameSeach
																 );

$rcr_transaction->disconnectDB (); 

echo  "{ success: true,  data: ".json_encode($result)."}";  

?>
