#!/usr/bin/env python3
"""
PHP Pear DB to MySQLi Converter
===============================

This script converts PHP files from using Pear DB to native MySQLi.
It handles common patterns found in Pear DB usage and converts them to MySQLi equivalents.

Usage: python php_pear_to_mysqli_converter.py <php_file_path>
"""

import sys
import re
import os
import argparse
from typing import List, Tuple


class PearToMySQLiConverter:
    def __init__(self):
        self.conversions_made = []
        
    def convert_file(self, file_path: str) -> str:
        """Convert a PHP file from Pear DB to MySQLi"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        original_content = content
        
        # Apply all conversions in order
        content = self._convert_includes(content)
        content = self._convert_connection(content)
        content = self._convert_queries(content)
        content = self._convert_error_handling(content)
        content = self._convert_result_methods(content)
        content = self._convert_escape_methods(content)
        content = self._convert_disconnect(content)
        content = self._convert_mysql_functions(content)
        content = self._clean_remaining_pear_references(content)
        content = self._add_connection_validation(content)
        
        # Add mysqli connection properties if needed
        if '$this->connection' in content and 'var $connection;' in content:
            content = self._add_mysqli_properties(content)
            
        return content
        
    def _convert_includes(self, content: str) -> str:
        """Convert Pear DB includes to MySQLi"""
        # Remove Pear DB require/include statements
        patterns = [
            r"require_once\s*\(\s*['\"]DB\.php['\"]\s*\)\s*;",
            r"require\s*\(\s*['\"]DB\.php['\"]\s*\)\s*;",
            r"include_once\s*\(\s*['\"]DB\.php['\"]\s*\)\s*;",
            r"include\s*\(\s*['\"]DB\.php['\"]\s*\)\s*;"
        ]
        
        for pattern in patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, "// MySQLi is built into PHP - no include needed", content)
                self.conversions_made.append("Removed Pear DB include statement")
                
        return content
        
    def _convert_connection(self, content: str) -> str:
        """Convert DB::connect() to MySQLi connection"""
        # More comprehensive patterns to catch all DB::connect variations
        patterns = [
            # Pattern 1: Class-based connection ($this->connection)
            r'\$this->connection\s*=\s*DB::connect\s*\(\s*["\']mysqli://[^"\']*["\']\s*\)',
            r'\$this->connection\s*=\s*DB::connect\s*\(\s*["\']mysqli://[^"\']*\$[^"\']*["\']\s*\)',
            r'\$this->connection\s*=\s*DB::connect\s*\(\s*["\']mysqli://["\']\s*\.[^)]+\)',
            r'\$this->connection\s*=\s*DB::connect\s*\([^)]+\)',
            # Pattern 2: Procedural connection ($connection)
            r'\$connection\s*=\s*DB::connect\s*\(\s*["\']mysqli://[^"\']*["\']\s*\)',
            r'\$connection\s*=\s*DB::connect\s*\(\s*["\']mysqli://[^"\']*\$[^"\']*["\']\s*\)',
            r'\$connection\s*=\s*DB::connect\s*\(\s*["\']mysqli://["\']\s*\.[^)]+\)',
            r'\$connection\s*=\s*DB::connect\s*\([^)]+\)',
            # Pattern 3: Any variable = DB::connect
            r'\$([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*DB::connect\s*\([^)]+\)',
        ]

        def replace_connection(match):
            # Determine if this is class-based or procedural
            full_match = match.group(0)
            if '$this->connection' in full_match:
                replacement = """$this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
        if ($this->connection->connect_error) {
            die("Connection failed: " . $this->connection->connect_error);
        }"""
            elif '$connection' in full_match:
                replacement = """$connection = new mysqli($db_host, $db_username, $db_password, $db_database);
        if ($connection->connect_error) {
            die("Connection failed: " . $connection->connect_error);
        }"""
            else:
                # Extract variable name from match
                var_match = re.search(r'\$([a-zA-Z_][a-zA-Z0-9_]*)', full_match)
                var_name = var_match.group(1) if var_match else 'connection'
                replacement = f"""${var_name} = new mysqli($db_host, $db_username, $db_password, $db_database);
        if (${var_name}->connect_error) {{
            die("Connection failed: " . ${var_name}->connect_error);
        }}"""

            self.conversions_made.append("Converted DB::connect() to MySQLi connection")
            return replacement

        # Apply patterns in order - most specific first
        for pattern in patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replace_connection, content)

        # Fix broken double function call syntax like: new mysqli(...)("mysqli://...")
        broken_syntax_pattern = r'new mysqli\s*\([^)]+\)\s*\([^)]+\)'
        if re.search(broken_syntax_pattern, content):
            content = re.sub(broken_syntax_pattern, 'new mysqli($db_host, $db_username, $db_password, $db_database)', content)
            self.conversions_made.append("Fixed broken MySQLi connection syntax")

        # Handle retry connection patterns more comprehensively
        retry_patterns = [
            # Pattern 1: if (DB::isError($this->connection)) { $this->connection = DB::connect(...) }
            r'if\s*\(\s*DB::isError\s*\(\s*\$this->connection\s*\)\s*\)\s*\{\s*\$this->connection\s*=\s*DB::connect\s*\([^}]+\}',
            # Pattern 2: if (DB::isError($connection)) { $connection = DB::connect(...) }
            r'if\s*\(\s*DB::isError\s*\(\s*\$connection\s*\)\s*\)\s*\{\s*\$connection\s*=\s*DB::connect\s*\([^}]+\}',
            # Pattern 3: Multiline retry pattern
            r'if\s*\(\s*DB::isError\s*\(\s*\$([^)]+)\s*\)\s*\)\s*\{[^}]*\$\1\s*=\s*DB::connect[^}]+\}',
        ]

        def replace_retry_connection(match):
            full_match = match.group(0)
            if '$this->connection' in full_match:
                replacement = """if ($this->connection->connect_error) {
            $this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
            if ($this->connection->connect_error) {
                die("Connection failed: " . $this->connection->connect_error);
            }
        }"""
            else:
                replacement = """if ($connection->connect_error) {
            $connection = new mysqli($db_host, $db_username, $db_password, $db_database);
            if ($connection->connect_error) {
                die("Connection failed: " . $connection->connect_error);
            }
        }"""
            self.conversions_made.append("Converted DB::connect() retry logic to MySQLi")
            return replacement

        for pattern in retry_patterns:
            content = re.sub(pattern, replace_retry_connection, content, flags=re.DOTALL)

        return content
        
    def _convert_queries(self, content: str) -> str:
        """Convert query methods"""
        # Convert getAll() method with proper indentation handling
        pattern = r'(\s*)\$([^=\s]+)\s*=\s*\$this->connection->getAll\s*\(\s*\$([^,\s]+)(?:\s*,\s*DB_FETCHMODE_ASSOC)?\s*\)\s*;'

        def replace_getall(match):
            indent = match.group(1)
            result_var = match.group(2)
            query_var = match.group(3)
            replacement = f"""{indent}$result_temp = $this->connection->query(${query_var});
{indent}if ($result_temp) {{
{indent}    ${result_var} = [];
{indent}    while ($row = $result_temp->fetch_assoc()) {{
{indent}        ${result_var}[] = $row;
{indent}    }}
{indent}    $result_temp->free();
{indent}}} else {{
{indent}    ${result_var} = false;
{indent}}}"""
            self.conversions_made.append("Converted getAll() to MySQLi query with fetch_assoc()")
            return replacement

        content = re.sub(pattern, replace_getall, content)

        # Convert simple query() method - be more specific to avoid conflicts
        pattern = r'(\s*)\$([^=\s]+)\s*=\s*\$this->connection->query\s*\(\s*\$([^)]+)\s*\)\s*;'

        def replace_query(match):
            indent = match.group(1)
            result_var = match.group(2)
            query_var = match.group(3)
            replacement = f"{indent}${result_var} = $this->connection->query(${query_var});"
            self.conversions_made.append("Converted query() method")
            return replacement

        content = re.sub(pattern, replace_query, content)

        # Convert SET NAMES query
        if re.search(r'\$this->connection->query\s*\(\s*["\']SET NAMES utf8["\']\s*\)', content):
            content = re.sub(
                r'\$this->connection->query\s*\(\s*["\']SET NAMES utf8["\']\s*\)',
                '$this->connection->set_charset("utf8")',
                content
            )
            self.conversions_made.append("Converted SET NAMES to set_charset()")

        return content
        
    def _convert_error_handling(self, content: str) -> str:
        """Convert DB::isError() to MySQLi error handling"""
        # Convert DB::isError() checks with proper context
        error_pattern = r'if\s*\(\s*DB::isError\s*\(\s*\$([^)]+)\s*\)\s*\)'

        def replace_error_check(match):
            var_name = match.group(1)
            # For connection errors
            if var_name == 'this->connection':
                replacement = 'if ($this->connection->connect_error)'
            elif var_name == 'connection':
                replacement = 'if ($connection->connect_error)'
            else:
                # For query result errors - determine connection variable
                if '$this->connection' in content:
                    replacement = f'if (!${var_name} || $this->connection->error)'
                else:
                    replacement = f'if (!${var_name} || $connection->error)'
            self.conversions_made.append("Converted DB::isError() check")
            return replacement

        content = re.sub(error_pattern, replace_error_check, content)

        # Convert DB::errorMessage() calls
        error_msg_patterns = [
            # Class-based patterns
            (r'DB::errorMessage\s*\(\s*\$this->connection\s*\)',
             '$this->connection->connect_error ?: $this->connection->error'),
            (r'DB::errorMessage\s*\(\s*\$([^)]+)\s*\)',
             '$this->connection->error'),
            # Procedural patterns
            (r'DB::errorMessage\s*\(\s*\$connection\s*\)',
             '$connection->connect_error ?: $connection->error'),
        ]

        # Determine which pattern to use based on content
        if '$this->connection' in content:
            # Class-based code
            for pattern, replacement in error_msg_patterns[:2]:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    self.conversions_made.append("Converted DB::errorMessage()")
        else:
            # Procedural code
            for pattern, replacement in [(error_msg_patterns[2][0], '$connection->error')]:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    self.conversions_made.append("Converted DB::errorMessage()")

            # Also handle generic DB::errorMessage calls in procedural code
            generic_pattern = r'DB::errorMessage\s*\(\s*\$([^)]+)\s*\)'
            if re.search(generic_pattern, content):
                content = re.sub(generic_pattern, '$connection->error', content)
                self.conversions_made.append("Converted DB::errorMessage()")

        # Handle die() statements with database errors
        die_patterns = [
            (r'die\s*\(\s*"([^"]*)"[^"]*\.\s*DB::errorMessage\s*\([^)]+\)\s*\)',
             r'die("\1" . $connection->error)'),
            (r'die\s*\(\s*"([^"]*)"[^"]*DB::errorMessage\s*\([^)]+\)\s*\)',
             r'die("\1" . $connection->error)'),
            # Handle PEAR DB specific error calls
            (r'die\s*\(\s*\$db->getMessage\s*\(\s*\)\s*\)',
             'die($connection->error)'),
        ]

        for pattern, replacement in die_patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                self.conversions_made.append("Converted die() statement with DB error")

        return content
        
    def _convert_result_methods(self, content: str) -> str:
        """Convert result set methods"""
        # Convert numRows()
        if re.search(r'\$([^-]+)->numRows\s*\(\s*\)', content):
            content = re.sub(
                r'\$([^-]+)->numRows\s*\(\s*\)',
                r'$\1->num_rows',
                content
            )
            self.conversions_made.append("Converted numRows() to num_rows")

        # Convert fetchRow() - handle different patterns
        # Pattern 1: Simple assignment
        fetchrow_pattern1 = r'\$([^=\s]+)\s*=\s*\$([^-]+)->fetchRow\s*\(\s*\)'
        if re.search(fetchrow_pattern1, content):
            content = re.sub(fetchrow_pattern1, r'$\1 = $\2->fetch_assoc()', content)
            self.conversions_made.append("Converted fetchRow() to fetch_assoc()")

        # Pattern 2: Reference assignment (PEAR DB style)
        fetchrow_pattern2 = r'\$([^=\s]+)\s*=&\s*\$([^-]+)->fetchRow\s*\(\s*\)'
        if re.search(fetchrow_pattern2, content):
            content = re.sub(fetchrow_pattern2, r'$\1 = $\2->fetch_assoc()', content)
            self.conversions_made.append("Converted fetchRow() reference assignment to fetch_assoc()")

        # Pattern 3: While loop with fetchRow
        while_fetchrow_pattern = r'while\s*\(\s*\$([^=\s]+)\s*=&?\s*\$([^-]+)->fetchRow\s*\(\s*\)\s*\)'
        if re.search(while_fetchrow_pattern, content):
            def replace_while_fetchrow(match):
                var_name = match.group(1)
                result_var = match.group(2)
                return f'while (${var_name} = ${result_var}->fetch_assoc())'
            content = re.sub(while_fetchrow_pattern, replace_while_fetchrow, content)
            self.conversions_made.append("Converted while fetchRow() loop to fetch_assoc()")

        # Convert fetch_row() to fetch_assoc() for better compatibility
        if re.search(r'\$([^-]+)->fetch_row\s*\(\s*\)', content):
            content = re.sub(
                r'\$([^-]+)->fetch_row\s*\(\s*\)',
                r'$\1->fetch_assoc()',
                content
            )
            self.conversions_made.append("Converted fetch_row() to fetch_assoc()")

        return content
        
    def _convert_escape_methods(self, content: str) -> str:
        """Convert escape methods"""
        # Convert escapeSimple()
        pattern = r'\$this->connection->escapeSimple\s*\(\s*\$([^)]+)\s*\)'
        replacement = r'$this->connection->real_escape_string($\1)'
        
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            self.conversions_made.append("Converted escapeSimple() to real_escape_string()")
            
        return content
        
    def _convert_disconnect(self, content: str) -> str:
        """Convert disconnect method"""
        content = re.sub(
            r'\$this->connection->disconnect\s*\(\s*\)',
            '$this->connection->close()',
            content
        )
        
        if 'disconnect()' in content:
            self.conversions_made.append("Converted disconnect() to close()")
            
        return content

    def _convert_mysql_functions(self, content: str) -> str:
        """Convert old mysql_* functions to MySQLi equivalents"""
        mysql_conversions = [
            (r'mysql_real_escape_string\s*\(\s*\$([^)]+)\s*\)',
             r'$this->connection->real_escape_string($\1)'),
            (r'mysql_num_rows\s*\(\s*\$([^)]+)\s*\)',
             r'$\1->num_rows'),
            (r'mysql_fetch_array\s*\(\s*\$([^)]+)\s*\)',
             r'$\1->fetch_array()'),
            (r'mysql_fetch_assoc\s*\(\s*\$([^)]+)\s*\)',
             r'$\1->fetch_assoc()'),
            (r'mysql_free_result\s*\(\s*\$([^)]+)\s*\)',
             r'$\1->free()'),
        ]

        for pattern, replacement in mysql_conversions:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                self.conversions_made.append(f"Converted mysql_* function to MySQLi")

        return content

    def _clean_remaining_pear_references(self, content: str) -> str:
        """Clean up any remaining PEAR/DB references that might cause issues"""
        # More aggressive cleanup of DB:: references
        pear_patterns = [
            # Remove any remaining DB::connect calls that weren't caught (most aggressive)
            (r'DB::connect\s*\([^)]+\)', 'new mysqli($db_host, $db_username, $db_password, $db_database)'),
            # Remove any remaining DB::isError calls
            (r'DB::isError\s*\(\s*\$([^)]+)\s*\)', r'(!$\1 || $this->connection->error)'),
            # Remove any remaining DB::errorMessage calls
            (r'DB::errorMessage\s*\([^)]+\)', '$this->connection->error'),
            # Remove any remaining PEAR constants
            (r'DB_FETCHMODE_ASSOC', ''),
            (r'DB_FETCHMODE_ORDERED', ''),
            # Fix any remaining set_charset issues
            (r'PEAR::set_charset', '$this->connection->set_charset'),
            # Remove any other DB:: method calls
            (r'DB::\w+\s*\([^)]*\)', '$this->connection->error'),
        ]

        # Also check for any assignment to DB::connect that might have been missed
        db_connect_assignments = [
            r'\$\w+\s*=\s*DB::connect\s*\([^)]+\)',
            r'\$this->\w+\s*=\s*DB::connect\s*\([^)]+\)',
        ]

        for pattern in db_connect_assignments:
            if re.search(pattern, content):
                content = re.sub(pattern,
                               lambda m: m.group(0).replace('DB::connect', 'new mysqli($db_host, $db_username, $db_password, $db_database)').replace('DB::connect(', 'new mysqli($db_host, $db_username, $db_password, $db_database'),
                               content)
                self.conversions_made.append("Cleaned remaining DB::connect assignment")

        for pattern, replacement in pear_patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                self.conversions_made.append(f"Cleaned remaining PEAR reference")

        return content

    def _add_connection_validation(self, content: str) -> str:
        """Add connection validation where needed"""
        # Look for places where connection might be used without validation
        if '$this->connection' in content and 'new mysqli(' in content:
            # Add a method to validate connection if it doesn't exist
            if 'function validateConnection' not in content:
                validation_method = """

    /**
     * Validate MySQLi connection
     */
    private function validateConnection() {
        if ($this->connection->connect_error) {
            die("Connection failed: " . $this->connection->connect_error);
        }
    }"""

                # Insert before the last closing brace
                class_end_pattern = r'(\s*)\}\s*$'
                if re.search(class_end_pattern, content):
                    content = re.sub(class_end_pattern, validation_method + r'\1}', content)
                    self.conversions_made.append("Added connection validation method")

        return content

    def _add_mysqli_properties(self, content: str) -> str:
        """Add MySQLi-specific properties and methods if needed"""
        # Add helper methods for common MySQLi operations
        helper_methods = """

    /**
     * Helper method to execute a query and return all results as associative array
     * Replaces Pear DB's getAll() functionality
     */
    private function executeGetAll($query) {
        $result_temp = $this->connection->query($query);
        if ($result_temp) {
            $results = [];
            while ($row = $result_temp->fetch_assoc()) {
                $results[] = $row;
            }
            $result_temp->free();
            return $results;
        }
        return false;
    }

    /**
     * Helper method to safely escape strings for SQL queries
     * Replaces Pear DB's escapeSimple() functionality
     */
    private function escapeString($string) {
        return $this->connection->real_escape_string($string);
    }
"""

        # Find the last method in the class and add helper methods before the closing brace
        class_end_pattern = r'(\s*)\}\s*$'
        if re.search(class_end_pattern, content):
            content = re.sub(class_end_pattern, helper_methods + r'\1}', content)
            self.conversions_made.append("Added MySQLi helper methods")

        return content
        
    def save_converted_file(self, original_file: str, converted_content: str, backup: bool = True) -> str:
        """Save the converted content to a new file"""
        base_name = os.path.splitext(original_file)[0]
        extension = os.path.splitext(original_file)[1]
        
        # Create backup if requested
        if backup:
            backup_file = f"{base_name}_pear_backup{extension}"
            if not os.path.exists(backup_file):
                with open(original_file, 'r', encoding='utf-8') as f:
                    backup_content = f.read()
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(backup_content)
                print(f"Backup created: {backup_file}")
        
        # Save converted file
        converted_file = f"{base_name}_mysqli{extension}"
        with open(converted_file, 'w', encoding='utf-8') as f:
            f.write(converted_content)
            
        return converted_file
        
    def print_conversion_summary(self):
        """Print summary of conversions made"""
        print("\n" + "="*50)
        print("CONVERSION SUMMARY")
        print("="*50)

        if self.conversions_made:
            # Group similar conversions
            conversion_counts = {}
            for conversion in self.conversions_made:
                conversion_counts[conversion] = conversion_counts.get(conversion, 0) + 1

            for i, (conversion, count) in enumerate(conversion_counts.items(), 1):
                if count > 1:
                    print(f"{i}. {conversion} ({count} times)")
                else:
                    print(f"{i}. {conversion}")
        else:
            print("No Pear DB patterns found to convert.")

        print("="*50)


def main():
    parser = argparse.ArgumentParser(
        description="Convert PHP files from Pear DB to MySQLi",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python php_pear_to_mysqli_converter.py myfile.php
  python php_pear_to_mysqli_converter.py myfile.php --no-backup
  python php_pear_to_mysqli_converter.py myfile.php --output converted.php
        """
    )
    
    parser.add_argument('php_file', help='Path to the PHP file to convert')
    parser.add_argument('--no-backup', action='store_true', 
                       help='Do not create a backup of the original file')
    parser.add_argument('--output', '-o', 
                       help='Output file path (default: original_name_mysqli.php)')
    
    args = parser.parse_args()
    
    try:
        converter = PearToMySQLiConverter()
        
        print(f"Converting {args.php_file}...")
        converted_content = converter.convert_file(args.php_file)
        
        if args.output:
            output_file = args.output
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(converted_content)
        else:
            output_file = converter.save_converted_file(
                args.php_file, 
                converted_content, 
                backup=not args.no_backup
            )
        
        print(f"Converted file saved as: {output_file}")
        converter.print_conversion_summary()
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
