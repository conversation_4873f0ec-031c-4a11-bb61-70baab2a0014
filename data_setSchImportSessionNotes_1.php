<?php  

       
// error_reporting(E_ALL);
// ini_set('display_errors', TRUE);
// ini_set('display_startup_errors', TRUE);
         


	ini_set("memory_limit","-1");


	require('fpdf/fpdf.php'); 
	 	
    require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }


    $RegistrantId = $_GET['RegistrantId'];
    $RegistrantLastName = $_GET['RegistrantLastName']; 
    $RegistrantNameDisp = $_GET['RegistrantName']; 
    $StoredFileName = $_GET['StoredFileName'];
    $FileExt = $_GET['FileExt'];
    $UserId = $_GET['UserId'];
	
 
 
   if ($FileExt == 'xlsx') {

	 	$inputFileType = 'Excel2007';
	 
	} else {
 
		$inputFileType = 'Excel5';

 	}	
	 

	$sheetname = 'Sheet1';


	$inputFileName = '../hr/'.$StoredFileName.'.'.$FileExt;
	//$inputFileName = '../hr/2yGCSv1zHDK3P5lUyVeU.xlsx';

 
	/**  Create a new Reader of the type defined in $inputFileType  **/
	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	/**  Load $inputFileName to a PHPExcel Object  **/
	
	$objReader->setLoadSheetsOnly($sheetname);
	$objReader->setReadDataOnly(true);


	$objPHPExcel = $objReader->load($inputFileName);


	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	 
	
	$i = 0;	
	$linecount = 0;	
    $matched_sessions = 0;
 
    //========

	class PDF extends FPDF
	{
		//Page header
		function Header()
		{	
			$this->SetLeftMargin(5);
			$this->SetRightMargin(5);

			$this->Ln(4);

			$this->SetFont('Arial','B',14);

			$this->Cell(0,5,'Session Notes Verification Results',0,1,'C');
			$this->Ln(2);



			// $this->Cell(0,5,'Provider: '.$RegistrantNameDisp,0,0,'C');
			// $this->Cell(40,5,$RegistrantNameDisp,0,0,'L');


			$this->Ln(10);
			$this->SetFont('Arial','',11);
			
		}

		//Page footer
		function Footer()
		{
			//Position at 1.5 cm from bottom
			$this->SetY(-15);
			//Arial italic 8
			
			$this->SetFont('Arial','B',11);
			//$this->SetX(5);
			$this->Cell(0,5,'_______________________________________________________________________________________',0,1,'C');

			$this->SetFont('Arial','I',8);
			//Page number
			$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
		}
	}




	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	

	$RegistrantNameDisp = 'Braff';

	$pdf->AddPage();
	//$pdf->SetFont('Times','',12);
	$pdf->SetFont('Arial','',9);


    //==========

  
	$x = 0;

	foreach ($sheetData as &$row) { // read excel - start  

 			 	
 			$x++;



		// Extract Notes Date Info
		//=========================================== 
			$date_field_signature  = 'Services (as of';
			if  (strstr($row["A"], $date_field_signature)) {

				
				$upload_file_desc = $row["A"];


				getMonthStartEndDate($upload_file_desc);

				$pdf->SetFont('Arial','I',11);

				$pdf->Cell(0,5,'Session Notes Date: '.$row["A"],0,0,'C');


				$pdf->Ln(10);
				$pdf->SetFont('Arial','',11);



			}



		// Extract Provider Name Info
		//=========================================== 
			$provider_check  = 'Services Completed by';
			if  (strstr($row["A"], $provider_check)) {

				
				 

				$provider_name = $row["A"];	
		

				$name_arr = explode(" ", $provider_name);
				$n = sizeof($name_arr);

 							
				$sim = similar_text(strtolower($RegistrantLastName), strtolower($name_arr[$n-1]), $perc);
				
				//echo "similarity: $sim ($perc %)";
				//echo 'lastName: '.$RegistrantLastName.', Arr: '.$name_arr[$n-1].'' ;

				if ($perc < 60) {

					$err_msg =  'Provider Name from SESIS Session Notes DOES NOT MATCH Selected Provider from eWebStaffing!!!';
					

					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);
					$connection->disconnect();
					$pdf->Output();	

					exit();

				}
 
 


			}



		// Extract Notes Date Info
		//=========================================== 
			

 
			$date_field_signature  = '(as of';
			if  (strstr($row["A"], $date_field_signature)) {

				
				 

				$upload_file_desc = $row["A"];


				getMonthStartEndDate($upload_file_desc);

 


			}



		//$arr_ind = [];	
		//$arr_grp = [];	
		$i = 0;

		// Extract Session Info
		//=========================================== 

			do {

		 	if (($row["M"] == 'Service Provided') || ($row["M"] == 'Service provided - Make-up')) { // Sessions Section - Start  
				

				/*=== Student ID ======*/
				$student_ext_id = $row["A"];

				/*=== Student Last Name ======*/
				$student_last_name = $row["B"];

				/*=== Student First Name ======*/
				$student_first_name = $row["C"];

				$StudentNameDisp = $student_first_name.' '.$student_last_name; 

				/*=== Session Date ======*/


				$service_date_x = PHPExcel_Shared_Date::ExcelToPHPObject($row["H"]);
 	 			$service_date = $service_date_x->format('Y-m-d');		 
 	 			$service_date_disp = $service_date_x->format('m/d/Y');		 


				$dow = date('w',strtotime($service_date));

				if ($dow == '6') {

					$PayrollWeek = $service_date;

				} else {

					$PayrollWeek = date('Y-m-d', strtotime("next saturday", strtotime($service_date)));
			 

				}
 

				/*=== Session Start Time ======*/
 
				$start_time_x = PHPExcel_Shared_Date::ExcelToPHPObject($row["I"]);
 	 			$start_time = $start_time_x->format('H:i:s');		 
 	 			$start_time_frm = $start_time_x->format('g:i A');		 

				/*=== Session End Time ======*/


				$end_time_x = PHPExcel_Shared_Date::ExcelToPHPObject($row["J"]);
 	 			$end_time = $end_time_x->format('H:i:s');		 
 	 			$end_time_frm = $end_time_x->format('g:i A');		 


				/*=== Service Type ======*/
				$service_type = $row["K"];


				/*=== Delivery Method ======*/
				$delivery_mode = $row["R"];
				$delivery_mode_id = 'I';
				 
				$tele_therapy_fl =  strpos($delivery_mode,"Tele-");
				if (is_numeric($tele_therapy_fl)) {

					$delivery_mode_id = 'V';


				} 

				//echo " delivery_mode: ".$delivery_mode.' tele_therapy_fl: '.$tele_therapy_fl.' delivery_mode_id: '.$delivery_mode_id.'';

				/*=== Group Size ======*/

			 	if (!is_numeric($row["Q"])) {
					$group_size = '1';

			 	
			 	} else {

					$group_size = $row["Q"];		 		

			 	}

			 	$duration = $row["O"];
			  	
				/*========================*/

				$grp_desc =  $row["P"]; 


			 	/*========= Skip if Student Mandate Exists/Session Date not within Start/End Dates =========*/

				$mandate_id = checkStudentMandate($RegistrantId, $student_ext_id,  $service_date, $grp_desc, $connection); 



				if ($mandate_id == '0') {

					$err_msg = ' Active Mandate for Student: '.$StudentNameDisp.' , Session Date: '.$service_date_disp.' was NOT FOUND in eWebStaffing!'; 
 					
				//	$err_msg = 'Student: '.$StudentNameDisp.', Date: '.$service_date_disp.'. Grouping size entered does not match mandate on file!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					break;

				}



			 	/*========= Skip if Session Already Exists =========*/

				$skip = checkIfSessionAlreadyExists($RegistrantId, $student_ext_id, $service_date, $grp_desc, $connection); 


				if ($skip == '1') {

					$err_msg 	= ' Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' Already Exists!'; 
					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					break;

				}


			 	/*========= Skip if Session Overlaps Existing Student Session =========*/

				$skip = checkSessionsOverlap($RegistrantId, $student_ext_id, $service_date, $start_time, $connection); 

				if ($skip == '1') {

					$err_msg = ' 1 Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' Overlaps Existing Session!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					break;

				}


			 	/*========= Skip if (Ind ONLY) Registrant's Session Exists for Given Date/Start Time =========*/

			 	if ($group_size == '1') {

					$skip = checkIndSessionsExists($RegistrantId, $service_date, $start_time, $connection); 

					if ($skip == '1') {

						$err_msg = ' 2 Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' Overlaps Existing Session!'; 
	 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

						break;

					}


			 	}



			 	/*========= Skip if Session Duration is Higher than Mandated =========*/

				$skip = checkSessionDuration($RegistrantId, $student_ext_id, $grp_desc, $start_time, $duration, $connection); 

				if ($skip == '1') {

					$err_msg = ' Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' posted Duration which is HIGHER than allowed under the Mandate!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					break;

				}


			 	/*========= Skip if Session Frequency is Higher than Mandated =========*/


				//$skip = checkSessionFrequency($RegistrantId, $student_ext_id, $PayrollWeek, $grp_desc, $connection); 
				$skip = checkSessionFrequency($mandate_id, $PayrollWeek, $connection); 


				if ($skip == '1') {

					$err_msg = ' Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' posted Frequency which is HIGHER than allowed under the Mandate!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					break;

				}


			 	/*========= Skip if Session Over Group Limit compared to  Mandated =========*/


				if ($grp_desc == 'Group') {

					$skip = checkSessionGroupLimit($RegistrantId, $student_ext_id, $group_size,  $connection); 

					if ($skip == '1') {

						$err_msg = ' Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' posted Group Size which is HIGHER than allowed under the Mandate!'; 
	 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

						break;

					}


				}	


			 	/*========= Skip if Session already posted for Mandate for give Date =========*/



				//$skip = checkSessionFrequency($RegistrantId, $student_ext_id, $PayrollWeek, $grp_desc, $connection); 
				$skip = checkMoreThanOneSessionPerDate($mandate_id, $service_date, $connection); 


 

				if ($skip == '1') {

					$err_msg = ' Session on: '.$service_date_disp.' for Student: '.$StudentNameDisp.' Already posted. Only 1 Session Per Date per Mandate is allowed!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					break;

				}


				$mandate_id = 'Not Found...';
				$mand_session_frequency =  '';
				$mand_session_length = '';
				$mand_session_grp_size = '';
				$mand_student_last_name = '';

			 	/*========= Get Student Mandate Info =========*/

			 	if ($row["P"] == 'Individual') { // Ind Mandate - Start

 			 		
 
			 		$query_mand_ind = " SELECT a.Id as MandateId,
			 		                               a.SessionFrequency,
			 		                               a.SessionLength,
			 		                               a.SessionGrpSize,
			 		                               a.StudentId,
			 		                               concat(b.FirstName, ' ',  b.LastName) as StudentName
                                       FROM SchStudentMandates a, SchStudents b
                                    WHERE b.ExtId = '{$student_ext_id}'
                                    AND   a.StudentId = b.Id 
                                    AND   a.RegistrantId  =  '{$RegistrantId}'
                                    AND   a.SchoolId != 0 

                                    AND   a.StatusId = '1'
                                    AND   '{$service_date}' BETWEEN a.StartDate and a.EndDate 
                                    AND   a.SessionGrpSize = '1'                 
			 		 				";

					$result_mand_ind = $connection->query ($query_mand_ind);

					if ($result_mand_ind->numRows() > 0)  { // numRows - Start	

						while ($row_mand_ind =& $result_mand_ind->fetchRow (DB_FETCHMODE_ASSOC)) {
						
								$mandate_id = $row_mand_ind['MandateId'];
								$student_id = $row_mand_ind['StudentId'];
								$mand_session_frequency = $row_mand_ind['SessionFrequency'];
								$mand_session_length = $row_mand_ind['SessionLength'];
								$mand_session_grp_size = $row_mand_ind['SessionGrpSize'];
								$mand_student_name = $row_mand_ind['StudentName'];
						
						}		
							 
					 		$arr_ind[] =  array('MandateId'=> $mandate_id, 
					 							'StudentId'=> $student_id,
					 							'StudentName'=> $mand_student_name,
					 							'SessionLength'=>$duration,
					 							'MandSessionLength'=>$mand_session_length,
					 							'MandSessionFrequency'=>$mand_session_frequency,
					 							'ServiceDate'=> $service_date, 					 							
					 							'ServiceDateDisp'=> $service_date_disp, 
					 							'PayrollWeek'=> $PayrollWeek,
					 							'StartTime'=> $start_time,
					 							'EndTime'=> $end_time,
					 							'SessionDeliveryModeId'=> $delivery_mode_id  
					 							
				 							)  ;
					

					} // numRows - End				

						 

			 	} // Ind Mandate - End
			 	
			 	else { // Group Mandate - Start





			 		$query_mand_grp = " 	SELECT a.Id as MandateId,
			 		                               a.SessionFrequency,
			 		                               a.SessionLength,
			 		                               a.SessionGrpSize,
			 		                               a.StudentId,
			 		                               concat(b.FirstName, ' ',  b.LastName) as StudentName

                                       FROM SchStudentMandates a, SchStudents b
                                    WHERE b.ExtId = '{$student_ext_id}'
                                    AND   a.StudentId = b.Id 
                                    AND   a.RegistrantId  =  '{$RegistrantId}'
                                    AND   a.StatusId = '1'
                                    AND   a.SchoolId != 0 
                                    AND   '{$service_date}' BETWEEN a.StartDate and a.EndDate 
                                    AND   a.SessionGrpSize != '1' LIMIT 1 
               
			 		 				";
					$result_mand_grp = $connection->query ($query_mand_grp);

					if ($result_mand_grp->numRows() > 0)  { // numRows - Start

						while ($row_mand_grp =& $result_mand_grp->fetchRow (DB_FETCHMODE_ASSOC)) {
						
								$mandate_id = $row_mand_grp['MandateId'];
								$student_id = $row_mand_grp['StudentId'];
								$mand_session_frequency = $row_mand_grp['SessionFrequency'];
								$mand_session_length = $row_mand_grp['SessionLength'];
								$mand_session_grp_size = $row_mand_grp['SessionGrpSize'];
								$mand_student_name = $row_mand_grp['StudentName'];
						
						}		


				 		//if ($group_size > 1 ) {
				 		if ($grp_desc == 'Group' ) {



					 		$arr_grp[] =  array('MandateId'=> $mandate_id, 
					 							'StudentId'=> $student_id,
					 							'StudentName'=> $mand_student_name,
					 							'SessionGrpSize'=>$group_size, 
					 							'MandSessionGrpSize'=>$mand_session_grp_size, 
					 							'SessionLength'=>$duration,
					 							'MandSessionLength'=>$mand_session_length,
					 							'MandSessionFrequency'=>$mand_session_frequency,
					 							'ServiceDate'=> $service_date, 					 							
					 							'ServiceDateDisp'=> $service_date_disp, 
					 							'PayrollWeek'=> $PayrollWeek,
					 							'StartTime'=> $start_time, 
					 							'EndTime'=> $end_time,
					 							'SessionDeliveryModeId'=> $delivery_mode_id
					 							)  ;
					 		 

				 		} else {


					 		$arr_ind[] =  array('MandateId'=> $mandate_id, 
					 							'StudentId'=> $student_id,	
					 							'StudentName'=> $mand_student_name,
					 							'SessionLength'=>$duration,
					 							'MandSessionLength'=>$mand_session_length,
					 							'MandSessionFrequency'=>$mand_session_frequency,
					 							'ServiceDate'=> $service_date, 					 							
					 							'ServiceDateDisp'=> $service_date_disp, 
					 							'PayrollWeek'=> $PayrollWeek,
					 							'StartTime'=> $start_time, 
					 							'EndTime'=> $end_time,
					 							'SessionDeliveryModeId'=> $delivery_mode_id

					 							)  ;
				 		}


					} // numRows - End



					 

			 	} // Group Mandate - End




 
			 	/*============================================*/
 
				//echo 'Student ID: '.$student_ext_id.' Name: '.$student_first_name.' '.$student_last_name.' Service Date: '.$service_date.' Start Time: '.$start_time_frm.' End Time: '.$end_time.' Group Size: '.$group_size.' Mand Group Size: '.$mand_session_grp_size.' Duration: '.$duration.' MandateId: '.$mandate_id.'';

 			 




				//================================================= 
				//  Check if Session Exists in eWeb for Registrant/Student  
				//================================================= 
			  	
				$query1 = "SELECT  	a.Id as ScheduleId,
									a.StartTime, 
									a.EndTime, 
									DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTimeFrm,
									DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTimeFrm,

									a.SessionGrpSize,
									CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as RegistrantName, 
									CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
									b.ExtId as StudentExtId

					FROM WeeklyServices a,
					     SchStudents b,
					     Registrants c
					WHERE c.Id = '{$RegistrantId}' 
					AND   a.RegistrantId = c.Id
					AND   b.ExtId = '{$student_ext_id}' 

					AND   a.StudentId = b.Id 
					AND   a.ServiceDate = '{$service_date}' 
					AND   a.ScheduleStatusId = 7  

		                ";

			//echo '$query1: '.$query1.'';
			$result1 = $connection->query ($query1);

		 	
			if (DB::isError($result1)){
						die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
			}			
	 	 	
			$session_exists = $result1->numRows();			
	 		


			if ($session_exists > 0) { // session_exists - start



			} // session_exists - end


 	  
		
			$linecount++;

 			 
		}	

	} while (0);

	}	//read excel - end 


	// No Transactions
	// if ($linecount == 0) {

	// 	$err_msg =  'The File you are tying to upload does contain any "NEW" SESIS Session Notes Transactions!!!';
	// 	$pdf->Cell(100,5,$err_msg,0,1,'L');
	// 	$pdf->Ln(2);

	// 	$connection->disconnect();
	// 	$pdf->Output();

	// 	exit();


	// }

	// Check Ind Array for duplicate entries
	//=====================================

  	if (sizeof($arr_ind) > 0) { // arr_grp size - start
  		//==========

			array_multisort(array_column($arr_ind, 'ServiceDate'), SORT_ASC,
		                array_column($arr_ind, 'StartTime'),      SORT_ASC,                
		                array_column($arr_ind, 'EndTime'),      SORT_ASC,
		                $arr_ind);



 
			foreach ($arr_ind as $row_i) {

				/*=========== First Row ==============*/
				if (!$Saved_i_ServiceDate) {



					$x_i = 0;
 


				} else {

					$x_i++;
				
				}


 
				/*=========== Group Break  ==============*/

				if (($Saved_i_ServiceDate == $row_i['ServiceDate']) && ($Saved_i_StartTime == $row_i['StartTime'])) {



							$student_names = $Saved_i_StudentName.', '.$row_i['StudentName'];

							$err_msg = $x_i.' 2	Individal Sessions on: '.$Saved_i_ServiceDate.' that starts at: '.$Saved_i_StartTime.' and includes Student(s): '.$student_names.' Overlap!'; 
				 			$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

				 			unset($arr_ind[$x_i]);
				 			unset($arr_ind[$x_i - 1]);



				} else {


					$Saved_i_ServiceDate = $row_i['ServiceDate'];
					$Saved_i_ServiceDateDisp = $row_i['ServiceDateDisp'];
					$Saved_i_SessionGrpSize = $row_i['SessionGrpSize'];
					$Saved_i_ServiceDate = $row_i['ServiceDate'];
					$Saved_i_StartTime = $row_i['StartTime'];
					$Saved_i_EndTime = $row_i['EndTime'];
					$Saved_i_StudentName = $row_i['StudentName'];

				}	



			}	

	}		



  	if (sizeof($arr_grp) > 0) { // arr_grp size - start
  		//==========

			array_multisort(array_column($arr_grp, 'ServiceDate'), SORT_ASC,
		                array_column($arr_grp, 'StartTime'),      SORT_ASC,                
		                array_column($arr_grp, 'EndTime'),      SORT_ASC,
		                $arr_grp);


			$grp_trn_cnt = 0;
			$grp_size_cnt = 0;
			$g = 0; 
			$arr_grp_ok = array();
			unset($arr_grp_tmp);
			
			$reg_names = '';

			foreach ($arr_grp as $row_g) {
			    
 

				/*=========== First Row ==============*/
				if (!$Saved_ServiceDate) {

					$Saved_ServiceDate = $row_g['ServiceDate'];
					$Saved_ServiceDateDisp = $row_g['ServiceDateDisp'];
					$Saved_SessionGrpSize = $row_g['SessionGrpSize'];
					$Saved_ServiceDate = $row_g['ServiceDate'];
					$Saved_StartTime = $row_g['StartTime'];
					$Saved_EndTime = $row_g['EndTime'];
					$Saved_StudentName = $row_g['StudentName'];




				}



				/*=========== Group Break  ==============*/

				if (($Saved_ServiceDate != $row_g['ServiceDate']) || ($Saved_StartTime != $row_g['StartTime'])) {


					//echo ' Trans Count: '.pow($grp_trn_cnt,$grp_trn_cnt).' Group Count: '.$grp_size_cnt.'<br>'; 
					//echo 'Temp Arr Size: '.sizeof($arr_grp_tmp).'<br>';

					
					/*======= Group Size Discrepancy =======*/
					if (($grp_trn_cnt * sizeof($arr_grp_tmp)) != $grp_size_cnt) {

 
							$err_msg = ' 1Group Sessions on: '.$Saved_ServiceDateDisp.' that starts at: '.$arr_grp_tmp[0]['StartTime'].' and includes Student(s): '.$reg_names.' were posted with Incorrect Group Size!'; 
				 			$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

 

					} else {

						$arr_grp_ok = array_merge($arr_grp_ok, $arr_grp_tmp);
					}

					unset($arr_grp_tmp);
					$grp_trn_cnt = 0;
					$grp_size_cnt = 0;

					$Saved_ServiceDate = $row_g['ServiceDate'];
					$Saved_ServiceDateDisp = $row_g['ServiceDateDisp'];
					$Saved_SessionGrpSize = $row_g['SessionGrpSize'];
					$Saved_StartTime = $row_g['StartTime'];
					$Saved_EndTime = $row_g['EndTime'];
					$Saved_StudentName = $row_g['StudentName'];
					$reg_names = '';



				}

				$arr_grp_tmp[] = $row_g;
				//array_push($arr_grp_tmp,$g);

				if (!$reg_names) {

					$reg_names = $row_g['StudentName'];

				} else {

					$reg_names = $reg_names.', '.$row_g['StudentName'];

				}

				$g++;
			    //echo '#: '.$g.' '.$row_g['StudentName'].' '.$row_g['MandateId'].' '.$row_g['SessionGrpSize'].' '.$row_g['ServiceDate'].' '.$row_g['StartTime'].' '.$row_g['EndTime'].'';
		 

				$grp_trn_cnt++;
				$grp_size_cnt = $grp_size_cnt + (int)$row_g['SessionGrpSize']; 

		 	} 
		     
					/*======= Group Size Discrepancy =======*/
					if (($grp_trn_cnt * sizeof($arr_grp_tmp)) != $grp_size_cnt) {

							$err_msg = ' 2	Group Sessions on: '.$Saved_ServiceDateDisp.' that starts at: '.$arr_grp_tmp[0]['StartTime'].' and includes Student(s): '.$reg_names.' were posted with Incorrect Group Size!'; 
				 			$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					} else {

						$arr_grp_ok = array_merge($arr_grp_ok, $arr_grp_tmp);
					}

			$arr_all = $arr_grp_ok;


  		//==========
  	} // arr_grp size - End 

  	if (sizeof($arr_ind) > 0) { // arr_ind size - start

		if (sizeof($arr_all) > 0)  {

			$arr_all = array_merge($arr_all, $arr_ind);


		} else {

			$arr_all = $arr_ind;

		}			


 	}




 	// echo '================================='; 	 	 
	foreach ($arr_all as $row_i) {
	    
		$total_hours =  $row_i['SessionLength'] / 60;
		$total_hours =  number_format($total_hours, 2, '.', ''); 

	    if ($row_i['SessionGrpSize']) {

	    	$group_size = $row_i['SessionGrpSize'];
	    } else {

	    	$group_size = '1';

	    }


	    //echo $row_i['MandateId'].' '.$total_hours.' '.$row_i['ServiceDate'].' '.$row_i['StartTime'].' '.$row_i['EndTime'].' '.$group_size.'';

				//================================================= 
				//  Check if Session Exists in eWeb for Registrant/Student  
				//================================================= 


				$skip = checkSessionFrequency($row_i['MandateId'], $row_i['PayrollWeek'], $connection); 


				if ($skip == '1') {

					$err_msg = ' Session on: '.$row_i['ServiceDate'].' for Student: '.$row_i['StudentName'].' posted Frequency which is HIGHER than allowed under the Mandate!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

					 

				}  

				
                if ($skip!= '1') {

				$skip = checkMoreThanOneSessionPerDate($row_i['MandateId'], $row_i['ServiceDate'], $connection); 


					if ($skip == '1') {

						$err_msg = ' Session on: '.$row_i['ServiceDate'].' for Student: '.$row_i['StudentName'].' Already posted. Only 1 Session Per Date per Mandate is allowed!'; 
	 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);


					}
				}	


			 	/*========= Skip if Session Overlaps Existing Student Session =========*/

				$skip = checkSessionsOverlapWithinSet($row_i['MandateId'], $row_i['ServiceDate'], $row_i['StartTime'], $connection); 

				if ($skip == '1') {

					$err_msg = ' 1 Session on: '.$row_i['ServiceDate'].' for Student: '.$row_i['StudentName'].' that starts at: '.$row_i['StartTime'].' Overlaps Existing Session!'; 
 					$pdf->Cell(100,5,$err_msg,0,1,'L');
$pdf->Ln(2);

 
				}


                if ($skip!= '1') {
			  	
				$query_in = "INSERT INTO WeeklyServices
							                  (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay,
												 
												RegistrantId, 
												SchoolId,
												StudentId,
												ServiceTypeId,
												MandateId,
												SessionGrpSize,
												SessionDeliveryModeId,
												UserId,
												TransDate )		
												
						    SELECT
										'{$row_i['PayrollWeek']}',
										a.Id,
										b.Id,
										'7',
										'{$row_i['ServiceDate']}',	 
										'{$row_i['StartTime']}',	 
                                        '{$row_i['EndTime']}', 		
                                        '{$total_hours}', 
										substr(dayname('{$ServiceDate}'),1,3),
                                        c.RegistrantId, 
										c.SchoolId,
										c.StudentId,
									  	c.ServiceTypeId,
									  	'{$row_i['MandateId']}',
									 	'{$group_size}',
									 	'{$row_i['SessionDeliveryModeId']}',
 									 	'{$UserId}',
                                         now()	 
                            FROM Clients a, ClientUnits b, SchStudentMandates c             
                            WHERE a.SchoolFL = '1'
                            AND   b.ClientId = a.Id
                            AND   c.RegistrantId != 0
                            AND   c.SchoolId != 0 
                            AND   c.Id =  '{$row_i['MandateId']}' LIMIT 1
		                ";

			//echo '$query_in: '.$query_in.'';
			$result_in = $connection->query ($query_in);

		 	
			if (DB::isError($result_in)){
						die("Could not query the database:<br />$query_in ".DB::errorMessage($result_in));
			}		


			++$matched_sessions;	
		}	
 


 	} 
 	   

 
	$connection->disconnect();

	$pdf->Ln(5);
	$pdf->Cell(0,5,'_____________________________________________________________________________',0,1,'L');
	$pdf->Cell(10,5,'',0,0,'L');
	$pdf->Cell(50,5,'Total Uploaded Sessions: ',0,0,'L');
	$pdf->Cell(20,5,$matched_sessions,1,0,'R');


	$pdf->Output();

//========================================================================     	
 
	function getMonthStartEndDate($upload_file_desc) {


		$date_arr = explode(" ", $upload_file_desc);

		$from_date = '01 '.$date_arr[0].' '.$date_arr[1];


		$from_date_ufm = strtotime($from_date);
		
		$GLOBALS['ReportFromDate'] = date("Y-m-d",$from_date_ufm);
		$GLOBALS['ReportToDate'] = date("Y-m-t",$from_date_ufm);


	}	

	function array_sort($array, $on, $order=SORT_ASC)
	{
	    $new_array = array();
	    $sortable_array = array();

	    if (count($array) > 0) {
	        foreach ($array as $k => $v) {
	            if (is_array($v)) {
	                foreach ($v as $k2 => $v2) {
	                    if ($k2 == $on) {
	                        $sortable_array[$k] = $v2;
	                    }
	                }
	            } else {
	                $sortable_array[$k] = $v;
	            }
	        }

	        switch ($order) {
	            case SORT_ASC:
	                asort($sortable_array);
	            break;
	            case SORT_DESC:
	                arsort($sortable_array);
	            break;
	        }

	        foreach ($sortable_array as $k => $v) {
	            array_push($new_array, $array[$k]);
	        }
	    }

	    return $new_array;
	}
 
	
	function checkStudentMandate($RegistrantId, $student_ext_id, $service_date, $grp_desc, $connection) {

		$mandate_id = '0';

		if ($grp_desc == 'Group') {

		 $query = " SELECT a.Id as MandateId FROM SchStudentMandates a, SchStudents b
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.SessionGrpSize > 1
                    AND   a.SchoolId != 0 

					AND   '{$service_date}' BETWEEN a.StartDate and a.EndDate
					AND   a.StatusId = '1'  		 "; 	


		} else {

		 $query = " SELECT a.Id as MandateId FROM SchStudentMandates a, SchStudents b
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.SessionGrpSize = 1
                    AND   a.SchoolId != 0 
					AND   '{$service_date}' BETWEEN a.StartDate and a.EndDate
					AND   a.StatusId = '1'  		 "; 	


		} 	

 
		$result = $connection->query ($query);
 		

		while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		
				$mandate_id = $row['MandateId'];
		
		}		



 		return $mandate_id;

 


	}  

	function checkIfSessionAlreadyExists($RegistrantId, $student_ext_id, $service_date, $grp_desc, $connection) {

		if ($grp_desc == 'Group') {

		 $query = " SELECT 1 FROM WeeklyServices a, SchStudents b, SchStudentMandates c
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.StudentId = c.StudentId 
					AND   c.SessionGrpSize > 1
					AND   c.SchoolId != 0 
					AND   a.ServiceDate = '{$service_date}' 
					AND   a.ScheduleStatusId >= 7  		 "; 	


		} else {

		 $query = " SELECT 1 FROM WeeklyServices a, SchStudents b, SchStudentMandates c
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.StudentId = c.StudentId 
					AND   c.SessionGrpSize = 1
                    AND   c.SchoolId != 0 					
					AND   a.ServiceDate = '{$service_date}' 
					AND   a.ScheduleStatusId >= 7  		 "; 	


		}	

		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}



	}


	function checkSessionsOverlap($RegistrantId, $student_ext_id, $service_date, $start_time, $connection)
	{

		 $query = " SELECT 1 FROM WeeklyServices a, SchStudents b
					WHERE a.RegistrantId != '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.ServiceDate = '{$service_date}'
					AND   (('{$start_time}' >= a.StartTime) && ('{$start_time}' <a.EndTime) ) 
					AND   a.ScheduleStatusId >= 7  		 "; 	

		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}
	}


	function checkIndSessionsExists($RegistrantId, $service_date, $start_time, $connection)
	{

		 $query = " SELECT 1 FROM WeeklyServices  
					WHERE RegistrantId = '{$RegistrantId}' 
					AND   ServiceDate = '{$service_date}'
					AND   StartTime =  '{$start_time}' 
					AND   ScheduleStatusId >= 7  		 "; 	

		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {


 			return '1';

 		} else {

 			return '0';

 		}
	}



	function checkSessionDuration($RegistrantId, $student_ext_id, $grp_desc, $start_time, $duration, $connection)
	 {


		if ($grp_desc == 'Group') {


		 $query = " SELECT 1 FROM SchStudentMandates a, SchStudents b
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.SessionGrpSize > 1
					AND   a.SchoolId != 0 
					AND   '{$duration}' > SessionLength
					AND   a.StatusId = '1'  		 "; 	

		} else {
		 $query = " SELECT 1 FROM SchStudentMandates a, SchStudents b
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$student_ext_id}' 
					AND   a.StudentId = b.Id 
					AND   a.SessionGrpSize = 1
					AND   a.SchoolId != 0 
					AND   '{$duration}' > SessionLength
					AND   a.StatusId = '1'  		 "; 	


		}	

 		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}



	} 


	function checkSessionFrequency($mandate_id, $PayrollWeek,  $connection )
	{

		$RemaininqFreq = 0; 

			 $query = " SELECT  a.SessionFrequency,
			                (SELECT count(*) 
                                  FROM WeeklyServices b
                                  where b.MandateId = '{$mandate_id}'
                                  and b.ScheduleStatusId > 6
                                  and b.PayrollWeek = '{$PayrollWeek}'
                              ) as RemaininqFreq  
 
              			 FROM SchStudentMandates a
               			WHERE a.Id = '{$mandate_id}'       
						"; 	

 
		 
						
		$result = $connection->query ($query);

  	 	

		while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		
				$SessionFrequency = $row['SessionFrequency'];
				$RemaininqFreq = $row['RemaininqFreq'];
		
		}		



 		if ($RemaininqFreq > ($SessionFrequency - 1)) {

 			return '1';

 		} else {

 			return '0';

 		}


	}  

	function checkSessionGroupLimit($RegistrantId, $student_ext_id, $group_size,  $connection )

	{

			 $query = " SELECT 1 FROM SchStudentMandates a, SchStudents b
						WHERE a.RegistrantId = '{$RegistrantId}' 
						AND   b.ExtId = '{$student_ext_id}' 
						AND   a.StudentId = b.Id 
						AND   '{$group_size}' > a.SessionGrpSize 
						AND   a.StatusId = '1'  
						AND   a.SchoolId != 0 
						AND   a.SessionGrpSize > 1
                        AND   a.Id = (select c.Id FROM  SchStudentMandates c
										where a.RegistrantId = c.RegistrantId 
						                AND   a.StudentId = c.StudentId  
                                        AND   c.SessionGrpSize > 1 
                                        ORDER BY c.SessionGrpSize DESC LIMIT 1
									
                        )						
 						"; 	


		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}


	} 

	function checkMoreThanOneSessionPerDate($mandate_id, $service_date, $connection )
	{

		 $query = " SELECT 1 FROM WeeklyServices 
					WHERE MandateId = '{$mandate_id}' 
					AND   ServiceDate = '{$service_date}'
					AND   ScheduleStatusId >= 7  		 "; 	

		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}
	}

 
	function checkSessionsOverlapWithinSet($mandate_id, $service_date, $start_time, $connection )
	{

		 $query = "SELECT 1 FROM WeeklyServices a, SchStudentMandates b
					WHERE b.Id = '{$mandate_id}' 
					AND   a.RegistrantId = b.RegistrantId  
					AND   a.ServiceDate = '{$service_date}'
				 	AND   (('{$start_time}' > a.StartTime) && ('$start_time' < a.EndTime) ) 
					AND   a.ScheduleStatusId >= 7     		 "; 	

		$result = $connection->query ($query);

	 	
		if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result1));
		}			
 	 	
		$session_exists = $result->numRows();			
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}
	}


 


	 
?>