<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId =  $_POST['RegistrantId'];
	$CredItemId =  $_POST['CredItemId'];
	$AwatingVerification =  $_POST['AwatingVerification'];
	$UserId = $_POST['UserId']; 

	$result = $rcr_transaction->setRegistrantDocumentVerificationStatus($RegistrantId,
																		$CredItemId,	
																		$AwatingVerification,							
																		$UserId
																	); 

	$rcr_transaction->disconnectDB ();  

	echo $result; 

?>
