<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$MandateId = $_POST['MandateId'];
	$StatusId = $_POST['StatusId'];
	$PlaceOfService = $_POST['PlaceOfService'];
	$StartDate = $_POST['StartDate'];	
	$EndDate = $_POST['EndDate'];
	$SchoolId = $_POST['SchoolId'];
	$RegistrantId = $_POST['RegistrantId'];
	$Comments = $_POST['Comments'];

	$UserId = $_POST['UserId'];



	/* Update Mandate Info
	 =======================*/
	$result = $rcr_transaction->setSchStudentMandate(	$MandateId,	
														$StatusId,
														$PlaceOfService,
														$StartDate,
														$EndDate,
														$SchoolId,
														$RegistrantId,
														$Comments,
														$UserId ); 


	/* Update Student's School from Assigned Mandate 
	 ===================================================*/
	$result1 = $rcr_transaction->setSchStudentSchoolFromMandate($MandateId,	
																$SchoolId,
																$UserId ); 



	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result1;

?>
