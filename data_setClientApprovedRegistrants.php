<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$Id = $_POST['Id'];
	$Status = $_POST['Status'];
	$ClientId = $_POST['ClientId'];
	$ClientUnitId = $_POST['ClientUnitId'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	
	//$RegistrantId = $_POST['RegistrantId'];
	$Data = $_POST['Data'];
	$Data=json_decode($Data,true);


	$OrientationId = $_POST['OrientationId'];
	$Comments = $_POST['Comments'];
	$Msg = $_POST['Msg'];
	$SearchId = $_POST['SearchId'];

	$UserId = $_POST['UserId'];
	
	 
	/*
	if ($Status == '2') {
		
	
		 
		$result3 = $rcr_transaction->setDeleteCandidateOrientSchedules( 	
		 																$RegistrantId,	
																		$OrientationId
																	); 
																			
		$OrientationId = '0';
		 
	}
	*/

	foreach ($Data as $RegistrantId) {
			
		//$RegistrantId = $rec['RegistrantId'];

	 
		$result = $rcr_transaction->setClientApprovedRegistrants( 	$Id,
																	$Status,
																	$ClientId,
																	$ClientUnitId,
																	$ServiceTypeId,
																	$RegistrantId,	
																	$OrientationId,
																	$Comments,	
																	$SearchId,
																	$UserId); 
																	

		if (!$Id) {
																	
		//================================================
		// Add Client Approval Message - New Submision  
		//================================================ 
		$result1 = $rcr_transaction->setNewClientApprovedRegistrantMessage(
										$SearchId, 
										$Msg,
										$UserId); 	

		} else {
		
		//===================================================
		// Add Client Approval Message - Existing Submission  
		//=================================================== 
		$result1 = $rcr_transaction->setClientApprovedRegistrantMessage($Id,	
																		$Msg,
																		$UserId); 	

		
		}


		//===================================================
		// Set Registrant's Credetialing Items  
		//=================================================== 
		$result1 = $rcr_transaction->setRegistrantRequiredCredItems($RegistrantId,	
																	$UserId);
	 
	} 
	
	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
