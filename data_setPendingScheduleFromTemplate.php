<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$RegistrantId = $_POST['RegistrantId'];
$RegistrantTypeId = $_POST['RegistrantTypeId'];
$ClientId = $_POST['ClientId'];
$ClientUnitId = $_POST['ClientUnitId'];
$PayrollWeek = $_POST['PayrollWeek'];
$ScheduleStatusId = $_POST['ScheduleStatusId'];

$Shift = $_POST['Shift'];
$StartTime = $_POST['StartTime'];
$EndTime = $_POST['EndTime'];
$Dow = $_POST['Dow'];
$ScheduleOrigBy = $_POST['ScheduleOrigBy'];
$UserId = $_POST['UserId'];

// Calculate Total Hours
//==========================================

$Unix_StartTime=strtotime($StartTime);
$Unix_EndTime=strtotime($EndTime);

$difference = $Unix_EndTime - $Unix_StartTime;
$fullDays    = floor($difference/(60*60*24)); 
$fullHours   = floor(($difference-($fullDays*60*60*24))/(60*60)); 
$fullMinutes = floor(($difference-($fullDays*60*60*24)-($fullHours*60*60))/60); 


if ($fullMinutes) {
    $TotalHours = ($fullHours.'.5'); 
} else {
	$TotalHours = $fullHours;
}


// Process for ALL Selected Week Dates
//==========================================


$dow_array = explode(",", $Dow);
$dow_count = count($dow_array);

for ($i=0; $i<$dow_count; $i++)
  {

	
	// Calculate Service Date(s)
	//==========================================
	$offset = (7 - $dow_array[$i]);
	$ServiceDate = $PayrollWeek;
	$ServiceDate = date('Y-m-d', strtotime($ServiceDate . ' - '.$offset.' day'));

	// Calculate Week Day
	//==========================================

	$wd = date('l', strtotime($ServiceDate));
	$WeekDay = substr($wd, 0, 3);
	
	/*
	// Check for Duplicate Shifts 
	//===========================================
	$dup_shift = $rcr_transaction->getRegistrantDupSchedules(	
														$ServiceDate,
														$StartTime,
														$EndTime,
														$RegistrantId);
															
	if ($dup_shift[0]['dup_shift'] > 0) {
		$rcr_transaction->disconnectDB (); 
		echo $dup_shift[0]['dup_shift'];
		break;
	}	
	//===========================================
	*/
	
	
	$result = $rcr_transaction->setClientWklySchedules(	$PayrollWeek,
														$ClientId, 
														$ClientUnitId, 
														'',
														$ScheduleStatusId,
														$ServiceDate,
														$StartTime,
														$EndTime,
														$TotalHours,
														$WeekDay, 
														$RegistrantId,
														$RegistrantTypeId,
														$ScheduleOrigBy,
														$UserId); 
		
  
	if ($i > 30) {
		break;
	}
  
  }






$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;

?>
