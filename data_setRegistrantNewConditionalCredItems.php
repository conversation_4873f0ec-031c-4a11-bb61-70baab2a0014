<?php 
	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId = $_POST['RegistrantId'];
	$ConditionalItemId = $_POST['ConditionalItemId'];
	$ConditionalSwitch = $_POST['ConditionalSwitch'];
	$UserId = $_POST['UserId'];

	$result = $rcr_transaction->setRegistrantNewConditionalCredItems(	$RegistrantId,
																		$ConditionalItemId, 
																		$ConditionalSwitch,
																		$UserId);
	
	
	
	$rcr_transaction->disconnectDB (); 

	//echo  "{ success: true,  data: ".json_encode($result)."}";
  
	echo $result;

?>
