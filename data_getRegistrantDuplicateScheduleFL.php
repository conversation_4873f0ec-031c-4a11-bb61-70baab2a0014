<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$RegistrantId = $_GET['RegistrantId'];
	$ServiceDate = $_GET['ServiceDate'];
	$StartTime = $_GET['StartTime'];
	

	$result = $rcr_transaction->getRegistrantDuplicateScheduleFL(	$RegistrantId, 
																	$ServiceDate,
																	$StartTime);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";

?>
