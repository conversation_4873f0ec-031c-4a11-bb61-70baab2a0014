

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_ClientApprrovalRegistrantSelection$$

CREATE PROCEDURE proc_ClientApprrovalRegistrantSelection (IN p_client_id int, p_registrant_type_id int, p_specialty_id int,  p_name_search varchar(10))  

BEGIN


   
   DECLARE v_Client_Zipcode VARCHAR(5);
   DECLARE v_Client_Latitude VARCHAR(8);
   DECLARE v_Client_Longitude VARCHAR(9);
   DECLARE v_Registrant_Latitude VARCHAR(8);
   DECLARE v_Registrant_Longitude VARCHAR(9);
   
   DECLARE done INT DEFAULT 0;    
   
   /* Get Client Zipcode*/
   /*=================================*/
   Select ZipCode into v_Client_Zipcode  
       from Clients
   Where Id = p_client_id;    
   
   /* Get Client Latitude/Longitude  */
   /*=================================*/
   Select Latitude, Longitude
       into v_Client_Latitude, v_Client_Longitude
       from ZipCodes
   Where SUBSTRING(v_Client_Zipcode,1,5) = ZipCode;    

   
   create temporary table tmp engine=memory

   SELECT a.Id as RegistrantId,
       CONCAT( trim( a.LastName) , ', ', trim( a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
       
       (SELECT max(ServiceDate) from WeeklyServices g
           where a.Id = g.RegistrantId
           AND ScheduleStatusId = 7 ) as LastSchedDate,  
       
           
       a.City,
       COALESCE(MobilePhone,'') as MobilePhone,
       COALESCE(HomePhone,'') as HomePhone,
       TypeId,
       Latitude,
       Longitude,
       SUBSTRING(a.ZipCode,1,5) as ZipCode,
       0000.00 as ProximityToClient,
       HospitalExp,
       (SELECT count(*) from RegistrantCredItems d
           where a.Id = d.RegistrantId
           AND ComplianceLevelId = 1 ) as CredItemsNonCompliant,    
       (SELECT count(*) from RegistrantCredItems d
           where a.Id = d.RegistrantId
           AND ComplianceLevelId != 1 ) as CredItemsCompliant
           
       
   FROM    Registrants a,
           ZipCodes c,
           RegistrantTypes f
   WHERE    a.TypeId = f.Id
           AND a.StatusId = 1
           AND SUBSTRING(a.ZipCode,1,5)  = c.ZipCode
           AND a.TypeId = p_registrant_type_id
           AND lower(a.LastName) like CONCAT( p_name_search, "%" )
		   AND NOT EXISTS (SELECT 1 from ClientApprovedRegistrants b
							WHERE a.Id = b.RegistrantId);
           
   IF (p_specialty_id != 0) THEN  
       DELETE FROM tmp        
       WHERE NOT EXISTS (SELECT 1 FROM RegistrantAttchedSpecialties b
                             WHERE tmp.RegistrantId = b.RegistrantId
                             AND   b.SpecialtyId = p_specialty_id);
   END IF;

           
       
   UPDATE tmp a, ZipCodes c  
       Set ProximityToClient = (
       3959 *
       acos(
           cos(radians(v_Client_Latitude)) *
           cos(radians(c.Latitude)) *
           cos(radians(c.Longitude) - radians(v_Client_Longitude)) +
           sin(radians(v_Client_Latitude)) *
           sin(radians(c.Latitude))
           )
       )
   Where a.ZipCode = c.ZipCode;
   
   /* If Specialty was passed - Remove Registrants without the Specialty  */
   


   
   
   SELECT    RegistrantId,
           RegistrantName,
           CredItemsNonCompliant,
           CredItemsCompliant,
           City,
           CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone)) as PhoneNumbers,
           TypeId,
           ZipCode,
           ProximityToClient,
           COALESCE(DATE_FORMAT( LastSchedDate, '%m-%d-%Y' ),'') as LastSchedDate,
           COALESCE(HospitalExp,'0') as HospitalExp,
           (SELECT  CASE count(*)
               WHEN 0 THEN ''
                   ELSE group_concat( CredItemDesc SEPARATOR ', ' )
               END as NonComplList
           FROM RegistrantCredItems a, CredentialingItems b
               WHERE a.RegistrantId = tmp.RegistrantId
               AND b.Id = a.CredItemId
               AND ComplianceLevelId =1 )     as NonComplList                        
                       
   From tmp
   Order By ProximityToClient, RegistrantName;
	drop temporary table if exists tmp;	
	
END $$

DELIMITER ;	