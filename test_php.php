
<?php


		$RegistrantLastName = 'Reinoso';
		$provider_name = 'Services Completed by Arislenia Reinoso';	



		$name_arr = explode(" ", $provider_name);
		$n = sizeof($name_arr);

		$last_name_arr = explode(" ", $RegistrantLastName);
		$x = sizeof($last_name_arr);

						
		$sim = similar_text(strtolower($last_name_arr[$x-1]), strtolower($name_arr[$n-1]), $perc);
		
		echo "similarity: $sim ($perc %)</br>";
		echo 'lastName: '.$RegistrantLastName.', Arr: '.$name_arr[$n-1].'</br>' ;

		if ($perc < 60) {

			echo 'Provider Name from SESIS Session Notes DOES NOT MATCH Selected Provider from eWebStaffing!!!';
			exit();

		}
 
 

?>	

 