<?php

	echo "Step 01";
 
	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php'); 

	$ClientId = $_GET['ClientId'];
	$ServiceDate = $_GET['ServiceDate'];
	$ServiceDateFrm =  substr($ServiceDate,5,2).'/'.substr($ServiceDate,8,2).'/'.substr($ServiceDate,0,4); 


	$ClientUnitId = $_GET['ClientUnitId'];
	$ServiceTypeId = $_GET['ServiceTypeId'];

	$GLOBALS['ServiceDate'] = $ServiceDate;	
	$GLOBALS['ServiceDateFrm'] = $ServiceDateFrm;

	// Get Company Information
	//==============================
	
   $connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	 
 
	//==================================
	// Get Company/Client Name
	//==================================
	
	$query = "SELECT 	CompanyName, 
                        ClientName
				FROM Company a, Clients b 
                    Where b.Id = '{$ClientId}' ";
				
	
	$result = $connection->query ($query);
	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$GLOBALS['Company'] = strtoupper($row['CompanyName']);
		$GLOBALS['ClientName'] = strtoupper($row['ClientName']).' DAILY COVERAGE';
	}	
	
	//$logo_image = 'kingsbrook_logo.jpg';
	$logo_image = 'D A I L Y  C O V E R A G E';
	
	$GLOBALS['Logo'] = $logo_image;
	
	$facility_name = strtoupper($GLOBALS['ClientName']).' DAILY COVERAGE';
	 
class PDF extends FPDF
{

	function PDF($orientation='L',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


	//Page header
	function Header()
	{
		//Logo
		/*$this->Image($GLOBALS['Logo'],140,8,40);*/
		//Arial bold 15
		
		//Move to the right
		//$this->Cell(80);
		//$this->Cell(50,4,'D A I L Y  C O V E R A G E',1,1,'C');
 
		//$this->Cell(140,4,$GLOBALS['Logo'],1,0,'C');
 
		$this->SetFont('Arial','B',11);
		$this->Ln(2);
		$this->Cell(100);
		$this->Cell(50,4,'D A I L Y  C O V E R A G E',0,0,'C');
		

		$this->Ln(15);
		//Title
		$this->Cell(30,4,'Vendor:',1,0,'L');
		$this->Cell(60,4,$GLOBALS['Company'],1,0,'C');
		$this->Cell(10);
		$this->Cell(30,4,'Facility:',1,0,'L');
		$this->Cell(120,4,$GLOBALS['ClientName'],1,1,'C');
		
		$this->Ln(2);
		$this->Cell(30,4,'Service Date:',1,0,'L');
		$this->Cell(60,4,$GLOBALS['ServiceDateFrm'],1,0,'C');	
		$this->Cell(10);
		$this->Cell(30,4,'Timestamp:',1,0,'L');
		$this->Cell(40,4,date('m/d/Y g:i A'),1,0,'C');

		$this->Ln(10);
		//Set Table Header
		$this->SetFont('Times','B',10);
		$this->Cell(8,4,'#',1,0,'C');
		$this->Cell(20,4,'Service Date',1,0,'C');
		$this->Cell(25,4,'Service Type',1,0,'C');
		$this->Cell(25,4,'Week Day',1,0,'C');
		$this->Cell(25,4,'Start Time',1,0,'C');
		$this->Cell(27,4,'End Time',1,0,'C');
		$this->Cell(15,4,'Tot Hrs',1,0,'C');
		$this->Cell(60,4,'Unit',1,0,'L');
		$this->Cell(60,4,'Employee Name',1,1,'C');
		$this->Ln(1);
		

		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		$this->SetFont('Times','I',9);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}
 
	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	$pdf->SetFont('Arial','B',9);
	//for($i=1;$i<=40;$i++)
		//$pdf->Cell(0,10,'Printing line number '.$i,0,1);
	
	//+++++++++++++++++++++++++++++++++++++++++++++++++
    
	 
	
	$query1 = "SELECT	DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										ServiceDate as ServiceDateOrg,
										StartTime as StartTimeSort,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										LunchHour,
										TotalHours, 
										SUBSTRING((dayname( ServiceDate)),1,3) as WeekDay,
										ServiceTypeDesc, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as RegistrantName,
										ServiceTypeId,
										SUBSTRING(UnitName,1,20) as UnitName,
										
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b,
											ServiceTypes c,
											ClientUnits d,
											Users e 							
										Where a.ClientId= '{$ClientId}'
										AND   a.ServiceDate =  '{$ServiceDate}' 
										AND   a.ClientUnitId like '{$ClientUnitId}'
										AND   a.ClientUnitId = d.Id
										AND   a.ServiceTypeId like '{$ServiceTypeId}'
										AND   a.ScheduleStatusId = '7'
										AND   a.RegistrantId = b.Id
										AND   a.ServiceTypeId = c.Id
										AND   a.UserId = e.UserId
								ORDER BY StartTimeSort, b.LastName, b.FirstName ";
														 
														  
				

	$result1 = $connection->query($query1);
	 
	$j = $result1->numRows();
	 
	
	$i = 1; 
	
	$pdf->SetFont('Arial','',10);

	while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {

		$pdf->Cell(8,4,$i,1,0,'C');
		$pdf->Cell(20,4,$row['ServiceDate'],1,0,'C');
		$pdf->Cell(25,4,$row['ServiceTypeDesc'],1,0,'C');
		
		$pdf->Cell(25,4,$row['WeekDay'],1,0,'C');

		$pdf->Cell(25,4,$row['StartTime'],1,0,'C');
		$pdf->Cell(27,4,$row['EndTime'],1,0,'C');
		$pdf->Cell(15,4,$row['TotalHours'],1,0,'C');
		$pdf->Cell(60,4,$row['UnitName'],1,0,'L');
		$pdf->Cell(60,4,$row['RegistrantName'],1,1,'L');
		

		$i++;
		
		
	} 

		 
	 

	//+++++++++++++++++++++++++++++++++++++++++++++++++
	 
	
	$pdf->Output();
	 
	$connection->disconnect();

	 	
?>
