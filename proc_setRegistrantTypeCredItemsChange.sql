

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setRegistrantTypeCredItemsChange$$

CREATE PROCEDURE proc_setRegistrantTypeCredItemsChange (IN  p_registrant_id INT,
                                                            p_source_type_id INT,
                                                            p_target_type_id INT,
                                                            p_user_id INT)  



BEGIN


 


    /* Get Cred. Items TO BE DELETED 
     ==========================================*/
    create temporary table cred_items_delete_tmp 

        (
            MandatoryCredItemId INT,
            CondFL INT
        );
 

    INSERT INTO cred_items_delete_tmp 
    SELECT a.MandatoryCredItemId, a.CondFL 
        FROM CredentialingItemsMandatory a
    WHERE a.RegistrantTypeId = p_source_type_id
    AND NOT EXISTS (SELECT 1 FROM CredentialingItemsMandatory b
    WHERE b.RegistrantTypeId = p_target_type_id
    AND a.MandatoryCredItemId = b.MandatoryCredItemId

        );  

    /* Get Cred. Items TO BE ADDED 
     ==========================================*/

    create temporary table cred_items_insert_tmp 

        (
            MandatoryCredItemId INT,
            CondFL INT
       );

    INSERT INTO cred_items_insert_tmp 
    SELECT a.MandatoryCredItemId, a.CondFL 
        FROM CredentialingItemsMandatory a
    WHERE a.RegistrantTypeId = p_target_type_id
    AND  NOT EXISTS (SELECT 1 FROM CredentialingItemsMandatory b
    WHERE b.RegistrantTypeId = p_source_type_id
    AND a.MandatoryCredItemId = b.MandatoryCredItemId
        );  
 

    /* DELETE "Conditional" Cred. Items   
     ==========================================*/


    DELETE FROM RegistrantCredItems  

    WHERE EXISTS (SELECT 1 FROM cred_items_delete_tmp a, CredentialingItemsConditionalDetails b 
                        WHERE RegistrantCredItems.RegistrantId = p_registrant_id
                        AND   RegistrantCredItems.CredItemId = b.CredItemId
                        AND   a.MandatoryCredItemId = b.ConditionalItemId
        );
 
    /* DELETE "Non-Conditional" Cred. Items   
     ==========================================*/


    DELETE FROM RegistrantCredItems  

    WHERE EXISTS (SELECT 1 FROM cred_items_delete_tmp a  
                        WHERE RegistrantCredItems.RegistrantId = p_registrant_id   
                        AND RegistrantCredItems.CredItemId = a.MandatoryCredItemId
                        AND a.CondFL = '0'
        );
 
    /*  Insert  Non-Conditional Item  
     ====================================*/
  
    INSERT INTO RegistrantCredItems
    (
        StatusId,
        RegistrantId,
        CredItemId,
        CredItemStatus,
        ComplianceLevelId,
        Comments,
        UserId,
        TransDate)

    SELECT '2',
           p_registrant_id,
           MandatoryCredItemId,
           '1',
           '1',
           'Added  via Reg. Type Conversion',
           p_user_id,
           NOW()
    FROM   cred_items_insert_tmp
    WHERE  CondFL = '0';

 

    /*  Insert "Default" Conditional Item  
     ====================================*/


    create temporary table cred_items_cond_insert_tmp 

        (
            MandatoryCredItemId INT
       );


  
    INSERT INTO cred_items_cond_insert_tmp
    SELECT b.CredItemId
        FROM cred_items_insert_tmp a, CredentialingItemsConditionalDetails b 
      
    WHERE   a.CondFL = '1'
    AND   a.MandatoryCredItemId = b.ConditionalItemId
    AND   b.ConditionalSwitch = '1'  ;

 
    INSERT INTO RegistrantCredItems
    (
        StatusId,
        RegistrantId,
        CredItemId,
        CredItemStatus,
        ComplianceLevelId,
        Comments,
        UserId,
        TransDate)

     SELECT '0',
           p_registrant_id,
           MandatoryCredItemId,
           '1',
           '1',
           'Added  via Reg. Type Conversion',
           p_user_id,
           NOW()
    FROM   cred_items_cond_insert_tmp ;
   

    select * from RegistrantCredItems
    Where RegistrantId = p_registrant_id;
 

    select * from cred_items_delete_tmp;

    drop temporary table if exists cred_items_delete_tmp;
    drop temporary table if exists cred_items_insert_tmp;
    drop temporary table if exists cred_items_cond_insert_tmp;    
    


END $$

DELIMITER ; 