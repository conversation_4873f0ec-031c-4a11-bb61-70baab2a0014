<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$PaymentId = $_POST['PaymentId'];
$InvoiceNumber = $_POST['InvoiceNumber'];
$PaymentAmount = $_POST['PaymentAmount'];
$PaymentAmountOrig = $_POST['PaymentAmountOrig'];
$ReferenceNumber = $_POST['ReferenceNumber'];
$UserId = $_POST['UserId'];


$result = $rcr_transaction->setClientInvoicePayment($PaymentId,
													$InvoiceNumber,
													$PaymentAmount,
													$ReferenceNumber,
													$UserId ); 

													

if(!is_numeric($PaymentAmountOrig)) {
	$PaymentAmountOrig = 0; 
}
													
$result1 = $rcr_transaction->setClientInvoicePaidAmount($InvoiceNumber,
														$PaymentAmount,
														$PaymentAmountOrig,
														$UserId ); 

													
													
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
