

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_ClientWklyScheduleSummary$$

CREATE PROCEDURE proc_ClientWklyScheduleSummary (IN p_client_id INT, p_we_date DATE)  

BEGIN


   
   DECLARE v_TotalSchedShifts, v_PendingShifts, v_CnlByClientShifts, v_CnlByRegistrantShifts, v_CnlByCoordShifts,  v_AvailShifts, v_ConfClientShifts, v_ConfRegistrantShifts,  v_ConfFullShifts INT;
   
   
   /* Total Scheduled Shifts  */
   /*==========================================*/
   Select count( Id ) into v_TotalSchedShifts
       From EmplWeeklySchedule
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus not in ('2','3','4');

	 
  /* Pending Shifts  */
   /*==========================================*/
	Select count( Id ) into v_PendingShifts
       From EmplWeeklySchedule
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '0' ;
	 
	 /* Total Available  */
   /*==========================================*/
     
   Select count( Id ) into v_AvailShifts
       From EmplWeeklySchedule
  Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '1' ;
	 
	 
   /* Total Cancelled By Registrant Shifts */
   /*==========================================*/
     
   Select count( Id ) into v_CnlByRegistrantShifts
       From EmplWeeklySchedule
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '2';

	 /* Total Cancelled By Client Shifts */
   /*==========================================*/
     
   Select count( Id ) into v_CnlByClientShifts
       From EmplWeeklySchedule
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '3';


	 /* Total Cancelled By Coordinator Shifts */
   /*==========================================*/
     
   Select count( Id ) into v_CnlByCoordShifts
       From EmplWeeklySchedule
   Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '4';


   /* Total Confirmed By Client  */
   /*==========================================*/
     
   Select count( Id )  into v_ConfClientShifts
       From EmplWeeklySchedule
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '5';

   /* Total Confirmed By Registrant  */
   /*==========================================*/
     
   Select count( Id )  into v_ConfRegistrantShifts
       From EmplWeeklySchedule
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '6';


   /* Total Fully Confirmed */
   /*==========================================*/

	 Select count( Id )  into v_ConfFullShifts
       From EmplWeeklySchedule
 Where ClientId  = p_client_id
     AND PayrollWeek =  p_we_date
     AND ScheduleStatus = '7';

     
    
 
   
   Select v_TotalSchedShifts as TotalShifts,
          v_PendingShifts as PendingShifts,
          v_AvailShifts as AvailShifts,
          v_CnlByRegistrantShifts as CnlByRegistrantShifts,
          v_CnlByClientShifts as CnlByClientShifts,
          v_CnlByCoordShifts as CnlByCoordShifts,
          v_ConfClientShifts as ConfClientShifts,
          v_ConfRegistrantShifts as ConfRegistrantShifts,
          v_ConfFullShifts as ConfFullShifts

          ;    
   
   
   
END $$

DELIMITER ;	