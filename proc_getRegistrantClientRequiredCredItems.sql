DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getRegistrantClientRequiredCredItems$$

CREATE PROCEDURE proc_getRegistrantClientRequiredCredItems (	p_registrant_id INT,
																p_client_id INT,
																p_client_unit_id INT,
																p_service_type_id INT
															) 		 

BEGIN


    create temporary table tmp engine=memory

    /* Get Client Required Non-Conditional Cred. Items	
     =================================================*/

	SELECT a.Id FROM CredentialingItems a
	WHERE EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b
				WHERE a.Id = b.MandatoryCredItemId	
                AND   b.ClientId = p_client_id
                AND   b.ClientUnitId in (0, p_client_unit_id)
                AND   b.ServiceTypeId = p_service_type_id
 				)
	AND ConitionalItemFL = '0'	;	

	

	INSERT INTO tmp

	SELECT a.Id FROM CredentialingItems a
	WHERE EXISTS (SELECT 1 FROM CredentialingItemsConditionalDetails c,
								CredentialingItemsClientMandatory b
									
				WHERE c.ConditionalItemId = b.MandatoryCredItemId	
                AND   b.ClientId = p_client_id
                AND   b.ClientUnitId in (0, p_client_unit_id)
                AND   b.ServiceTypeId = p_service_type_id 
                AND   a.Id = c.CredItemId    );

	/*===================*/

	
	 
	SELECT		a.CredItemId,
				CredItemDesc,
				CASE  ComplianceLevelId 
					WHEN '1' THEN 'Non-Compliant'
				ELSE 'Compliant'
				END AS ComplianceLevelDesc,
				CredItemStatusColor,
				CredItemStatusBGColor,
				CredItemStatusDesc,	
				COALESCE(b.Comments,'') as Comments,
				CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
				a.TransDate 
		FROM 	RegistrantCredItems a,
				CredentialingItems b,
				CredentialingItemStatuses g,
				tmp c,
				Users e 
		WHERE 	a.CredItemId = b.Id 
			AND a.UserId = e.UserId
			AND a.CredItemStatus = g.CredItemStatus
			AND RegistrantId =  p_registrant_id 
			AND a.CredItemId = c.Id
		 
		ORDER BY CredItemDesc ;
		 		  

	    /*SELECT group_concat( c.Id SEPARATOR ', ' ) as Ids FROM tmp c ;*/

	drop temporary table if exists tmp;

END $$

DELIMITER ;	



