

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setRegistrantNewConditionalCredItems$$

CREATE PROCEDURE proc_setRegistrantNewConditionalCredItems (IN p_registrand_id int, p_cond_item_id int, p_cond_switch int, p_user_id int)  

BEGIN

 	
	/*============================================*/
	/* Get All Cred Items Curently Attached for the Cond Item ID  */ 
	/*============================================*/
	
	create temporary table tmp engine=memory

	SELECT CredItemId FROM CredentialingItemsConditionalDetails 
		WHERE ConditionalItemId = p_cond_item_id; 	

	/*============================================*/
	/* Delete All Cred Items Curently Attached for the Cond Item ID  */ 
	/*============================================*/

	
	DELETE  FROM RegistrantCredItems	
		WHERE RegistrantId = p_registrand_id 
		AND CredItemId IN (SELECT CredItemId FROM tmp);
		

	/*============================================*/
	/* Insert New Conditional Cred Items            */ 
	/*============================================*/
	
	INSERT INTO RegistrantCredItems
									(
									RegistrantId,
									CredItemId,
									UserId,
									TransDate
									)
								SELECT 	p_registrand_id,
										CredItemId,
										p_user_id,
										NOW()
								FROM CredentialingItemsConditionalDetails 
								WHERE ConditionalItemId = p_cond_item_id
								AND ConditionalSwitch = p_cond_switch ;
	 
	/*=====================================================*/
	/* Save Registrant's New Conditional Cred Items Switch  */ 
	/*=====================================================*/

	 
	UPDATE RegistrantCredentialingItemsConditional
	  SET ConditionalSwitch = p_cond_switch
	WHERE RegistrantId = p_registrand_id 
	AND   ConditionalItemId	= p_cond_item_id ;
	 
	drop temporary table if exists tmp;
	 
	
END $$

DELIMITER ;	