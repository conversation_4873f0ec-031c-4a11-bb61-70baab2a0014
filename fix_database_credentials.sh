#!/bin/bash

# Fix Database Credentials and Access Issues
# This script helps diagnose and fix MySQL/MariaDB authentication problems

echo "🔧 Fixing Database Credentials and Access"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    case $1 in
        "error") echo -e "${RED}❌ $2${NC}" ;;
        "success") echo -e "${GREEN}✅ $2${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $2${NC}" ;;
        *) echo "$2" ;;
    esac
}

# Check if MySQL/MariaDB is running
echo ""
echo "🔍 Checking database server status..."

if systemctl is-active --quiet mysql; then
    print_status "success" "MySQL service is running"
    DB_SERVICE="mysql"
elif systemctl is-active --quiet mariadb; then
    print_status "success" "MariaDB service is running"
    DB_SERVICE="mariadb"
else
    print_status "error" "No MySQL/MariaDB service found running"
    echo "Starting MySQL service..."
    sudo systemctl start mysql || sudo systemctl start mariadb
    DB_SERVICE="mysql"
fi

# Check current database credentials from PHP file
echo ""
echo "🔍 Checking current database configuration..."

DB_LOGIN_FILE="/var/www/all-in-1-spot/data/db_login.php"
if [ -f "$DB_LOGIN_FILE" ]; then
    print_status "success" "Found db_login.php file"
    echo "Current configuration:"
    grep -E '\$db_(host|username|password|database)' "$DB_LOGIN_FILE" | sed 's/\$db_password.*/\$db_password = "[HIDDEN]";/'
else
    print_status "error" "db_login.php file not found at $DB_LOGIN_FILE"
    echo "Expected location: $DB_LOGIN_FILE"
fi

# Test database connection
echo ""
echo "🧪 Testing database connection..."

# Try to connect to MySQL as root to check user permissions
echo "Checking MySQL root access..."
if mysql -u root -p -e "SELECT 1;" 2>/dev/null; then
    print_status "success" "MySQL root access confirmed"
    MYSQL_ROOT_ACCESS=true
else
    print_status "warning" "MySQL root access requires password or is not available"
    MYSQL_ROOT_ACCESS=false
fi

# Function to create/fix database user
fix_database_user() {
    local db_user="$1"
    local db_pass="$2"
    local db_name="$3"
    local db_host="${4:-localhost}"
    
    echo ""
    echo "🔧 Fixing database user permissions..."
    
    # MySQL commands to fix user
    mysql -u root -p << EOF
-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS \`$db_name\`;

-- Create user if it doesn't exist
CREATE USER IF NOT EXISTS '$db_user'@'$db_host' IDENTIFIED BY '$db_pass';

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON \`$db_name\`.* TO '$db_user'@'$db_host';

-- Also try with '%' wildcard for remote connections
CREATE USER IF NOT EXISTS '$db_user'@'%' IDENTIFIED BY '$db_pass';
GRANT ALL PRIVILEGES ON \`$db_name\`.* TO '$db_user'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Show user grants
SHOW GRANTS FOR '$db_user'@'$db_host';
EOF
}

# Interactive credential setup
echo ""
echo "🔧 Database Credential Setup"
echo "============================"

read -p "Enter database username (current: all_in_1_spot): " DB_USER
DB_USER=${DB_USER:-all_in_1_spot}

read -s -p "Enter database password: " DB_PASS
echo ""

read -p "Enter database name (default: all_in_1_spot): " DB_NAME
DB_NAME=${DB_NAME:-all_in_1_spot}

read -p "Enter database host (default: localhost): " DB_HOST
DB_HOST=${DB_HOST:-localhost}

# Test the credentials
echo ""
echo "🧪 Testing provided credentials..."

if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME; SELECT 1;" 2>/dev/null; then
    print_status "success" "Database credentials work!"
else
    print_status "error" "Database credentials failed. Attempting to fix..."
    
    if [ "$MYSQL_ROOT_ACCESS" = true ]; then
        fix_database_user "$DB_USER" "$DB_PASS" "$DB_NAME" "$DB_HOST"
    else
        echo ""
        print_status "warning" "Cannot fix automatically without MySQL root access"
        echo "Please run these commands manually as MySQL root:"
        echo ""
        echo "mysql -u root -p"
        echo "CREATE DATABASE IF NOT EXISTS \`$DB_NAME\`;"
        echo "CREATE USER IF NOT EXISTS '$DB_USER'@'$DB_HOST' IDENTIFIED BY '$DB_PASS';"
        echo "GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'$DB_HOST';"
        echo "FLUSH PRIVILEGES;"
        echo "EXIT;"
    fi
fi

# Update db_login.php file
echo ""
echo "🔧 Updating db_login.php file..."

cat > "$DB_LOGIN_FILE" << EOF
<?php
// Database configuration for MySQLi
\$db_host = "$DB_HOST";
\$db_username = "$DB_USER";
\$db_password = "$DB_PASS";
\$db_database = "$DB_NAME";

// Legacy variables for backward compatibility
\$db_login = \$db_username;
\$db_pwd = \$db_password;
\$db_name = \$db_database;
?>
EOF

print_status "success" "Updated db_login.php with new credentials"

# Test PHP connection
echo ""
echo "🧪 Testing PHP MySQLi connection..."

php << EOF
<?php
include('$DB_LOGIN_FILE');

try {
    \$connection = new mysqli(\$db_host, \$db_username, \$db_password, \$db_database);
    
    if (\$connection->connect_error) {
        echo "❌ Connection failed: " . \$connection->connect_error . "\n";
        exit(1);
    } else {
        echo "✅ PHP MySQLi connection successful!\n";
        echo "Connected to: " . \$connection->server_info . "\n";
        \$connection->close();
    }
} catch (Exception \$e) {
    echo "❌ PHP connection error: " . \$e->getMessage() . "\n";
    exit(1);
}
?>
EOF

# Final verification
echo ""
echo "🎉 Database Setup Complete!"
echo "=========================="
echo ""
echo "Configuration summary:"
echo "- Database Host: $DB_HOST"
echo "- Database User: $DB_USER"
echo "- Database Name: $DB_NAME"
echo "- Config File: $DB_LOGIN_FILE"
echo ""
echo "Next steps:"
echo "1. Test your PHP application"
echo "2. Check that all required database tables exist"
echo "3. Verify file permissions on db_login.php"
echo ""
echo "Useful commands:"
echo "  - Test connection: mysql -h $DB_HOST -u $DB_USER -p $DB_NAME"
echo "  - Check PHP connection: php -f test_connection.php"
echo "  - View MySQL users: mysql -u root -p -e \"SELECT User,Host FROM mysql.user;\""
echo ""

# Set proper file permissions
chmod 640 "$DB_LOGIN_FILE"
chown www-data:www-data "$DB_LOGIN_FILE" 2>/dev/null || true

print_status "success" "Set secure file permissions on db_login.php"
