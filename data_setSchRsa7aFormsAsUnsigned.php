  
<?php
    

    require_once("db_GetSetData.php");
     $conn = getCon();

 

    $Rsa7aFormSignatureStatus = $_POST['Rsa7aFormSignatureStatus'];
    $UserId = $_POST['UserId'];

    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);
 
    $ProviderSignatureTimeStamp = date("Y-m-d h:i:s");


    if ($Rsa7aFormSignatureStatus == '4') {

      $ParentApprovalSentEmailDate = date("Y-m-d h:i:s");

    } else {

      $ParentApprovalSentEmailDate = ("0000-00-00 00:00:00");

    }

    foreach ($Data as $Rsa7aFormId) {

        
  

         $query = "UPDATE SchRsa7aFormSignatures       
             SET StatusId =  '{$Rsa7aFormSignatureStatus}',
                 ParentApprovalSentEmailDate = '0000-00-00 00:00:00',
                 ProviderSignatureTimeStamp =  '{$ProviderSignatureTimeStamp}',
                 ParentSignatureName = '',
                 ParentSignatureTimeStamp = '0000-00-00 00:00:00',
                 ParentSignatureIPAddress = '',
                 UserId = '{$UserId}',
                 TransDate = NOW()
           WHERE  Id =     '{$Rsa7aFormId}'
             ";
   

        $ret =  setData ($conn, $query);        
  
  }   


    
  setDisConn($conn);
  //echo $ret;
  echo $query;
 
    


?>
 
