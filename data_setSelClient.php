<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$form_data = json_decode(file_get_contents('php://input'));

$result = $rcr_transaction->setSelClient(   $form_data->{'Id'},
											$form_data->{'SearchId'},
											$form_data->{'ClientStatusId'},	  
											$form_data->{'ClientName'},
											$form_data->{'StreetAddress1'},
											$form_data->{'StreetAddress2'},
											$form_data->{'City'},
											$form_data->{'State'},
											$form_data->{'ZipCode'},
											$form_data->{'OfficePhone'},
											$form_data->{'Fax'},
											$form_data->{'RecruitorId'},
											$form_data->{'CoordinatorId'},
											$form_data->{'UserId'}
											); 

$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
