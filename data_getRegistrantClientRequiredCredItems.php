<?php 

require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$RegistrantId = $_GET['RegistrantId'];
$ClientId = $_GET['ClientId'];
$ClientUnitId = $_GET['ClientUnitId'];
$ServiceTypeId = $_GET['ServiceTypeId'];

$result = $rcr_transaction->getRegistrantClientRequiredCredItems(	$RegistrantId,
																	$ClientId,
																	$ClientUnitId,
																	$ServiceTypeId
																);


$rcr_transaction->disconnectDB (); 

echo  "{ success: true,  data: ".json_encode($result)."}";


  
?>
