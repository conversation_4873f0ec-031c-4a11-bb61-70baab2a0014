<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$ClientId = $_GET['ClientId'];
	$ClientUnitId = $_GET['ClientUnitId'];
	$ServiceTypeId = $_GET['ServiceTypeId'];
	$SpecialtiesList = $_GET['SpecialtiesList'];


	$result = $rcr_transaction->getClientApprovalCandidatesSel(	$ClientId,
																$ClientUnitId,
																$ServiceTypeId,
																$SpecialtiesList
																	 );

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";  

?>
