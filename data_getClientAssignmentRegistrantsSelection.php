<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$ClientId = $_GET['ClientId'];
	$ClientUnitId = $_GET['ClientUnitId'];	
	$ServiceTypeId = $_GET['ServiceTypeId'];
	$StartDate = $_GET['StartDate'];
	$EndDate = $_GET['EndDate'];


	$result = $rcr_transaction->getClientAssignmentRegistrantsSelection(	$ClientId, 
																			$ClientUnitId,
																			$ServiceTypeId, 
																			$StartDate, 
																			$EndDate
																		);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
  

?>
