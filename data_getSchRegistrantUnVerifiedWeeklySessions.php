<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$PayrollWeek = $_GET['PayrollWeek'];
	$ServiceDate = $_GET['ServiceDate'];


	$query = "CALL  proc_getSchRegistrantUnVerifiedWeeklySessions ('{$RegistrantId}','{$PayrollWeek}','{$ServiceDate}')";      


    // $query = "	SELECT  a.Id as MandateId,
				// 			a.ServiceTypeId, 
				// 	   	c.ServiceTypeDesc,	
				//        	a.SessionLength,
				//        	a.SessionGrpSize,
				//        	a.SessionFrequency,
				//        	a.StudentId,
				//        	/*CONCAT(b.LastName, ', ', b.FirstName) as StudentName*/
				// 		CONCAT( trim( b.LastName) , ', ', trim(b.FirstName) ,' (', b.ExtId,')' ) as StudentName 


				// FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
				// WHERE a.RegistrantId = '{$RegistrantId}' 
				// AND a.StatusId = '1'
				// AND a.StudentId = b.Id
				// AND '{$ServiceDate}' BETWEEN a.StartDate AND a.EndDate
				// AND a.ServiceTypeId = c.Id
				// AND  (SELECT COUNT(*) FROM WeeklyServices d
				//        	  	WHERE d.MandateId = a.Id 
				//        	  	AND d.ScheduleStatusId > 5
				//        	  	AND d.PayrollWeek = '{$PayrollWeek}'    
				//        	) < a.SessionFrequency	
				// AND NOT EXISTS (SELECT 1 FROM WeeklyServices e
				// 			WHERE e.MandateId = a.Id 
				//        	  	AND e.ScheduleStatusId > 5
				//        	  	AND e.ServiceDate = '{$ServiceDate}' 
				//        	  	AND a.ServiceTypeId = e.ServiceTypeId 
				// ) 
				// ORDER BY  b.LastName, b.FirstName       	  	

				//       	  	";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

?>
