<?php
// MySQLi is built into PHP - no include needed

/*===========================================================
// Class - data_handler*/
class dataHandler {
		 

        var $connection;

	   		function  dataHandler (){
            
			 	include('db_login.php');
					
                
				
				  
				$this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
                if ($this->connection->connect_error){
                    $this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
                }
				
				if ($this->connection->connect_error){
                           die("Could not connect to the database: <br />".$this->connection->connect_error);
                } 
				  
				$this->connection->query('SET NAMES utf8');
  
            }  
 
			/* Close Open Connection
            //=======================*/			
			function disconnectDB () {
                          $this->connection->disconnect();
            }	


			// Get States  Listing
            //=======================			
			function getStates() {
			
                        $query = "SELECT State, StateName 
									FROM States ";
									 
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
					}

			
			/* Verify 
            //=======================*/					
	/*
			function verifyLogin($login, $password) {
                         
						$login = mysql_real_escape_string($login); 
						$password = mysql_real_escape_string($password); 

                        $query = "SELECT 	a.UserId, 
											a.FirstName,
											a.LastName,
											a.UserGroupId,
											a.ExtId,
											CompanyName,
											TimeCardTabFL,
											BilingTabFL,
											PayrollTabFL,
											AdminTabFL,
											a.ResetFL,
											a.Email 
									FROM Users a, Company b, UserGroups c
										WHERE Login = '{$login}'
										AND Password = '{$password}' 
										AND a.UserGroupId = c.Id ";
                        $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }

                        if ($result->numRows() == "1") {
                              
                            $row = $result->fetchRow(); 
                            $UserName =  $row[1]." ". $row[2]; 
                            $arr = Array(	"UserId" => $row[0], 
										 	"UserName" => $UserName, 
											"UserGroup" => $row[3],
											"ExtId" => $row[4],
											"CompanyName" => $row[5],
											"TimeCardTabFL" => $row[6],
											"BilingTabFL" => $row[7],
											"PayrollTabFL" => $row[8],
											"AdminTabFL" => $row[9],
											"ResetFL" => $row[10],
											"Email" => $row[11]	
										);
                            return $arr;
                             
						} else {
                              
                            $invalid = "invalid";
                            $err_arr = Array("UserId" => $invalid, "UserName" => $invalid, "Email" => $invalid);                    
                            return $err_arr;
                        }				 

                                 
            }  			
		*/
			// Get Dashboard Side Navigation Data
            //=======================			
			function getDashboardSideNavigation() {
			
                        $query = "SELECT *							
						FROM DashboardSideNavigation 
					    WHERE Status = 1
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			
			// Get Client Side Navigation Data
            //=======================			
			function getClientSideNavigation() {
			
                        $query = "SELECT *							
						FROM ClientSideNavigationNew 
					    WHERE Status = 1
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			// Get Clients Listing
            //=======================			
			function getClients() {
                        $query = "SELECT Id as id, 
							Id, 
							ClientName,
							SearchId
						FROM Clients 
						WHERE SchoolFL = '1'
	
					order by ClientName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get Selected Client's general Info
            //=======================			
			function getSelClient($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id, 
										SearchId,
										ClientStatusId,
										ClientName,
										StreetAddress1,
										StreetAddress2,
										City,
										State,
										ZipCode,
										OfficePhone,
										Fax,
										RecruitorId,  
										
										( select  CONCAT( trim( b.FirstName) , ' ', trim( b.LastName)) 
												from Users b
												Where RecruitorId  =  b.UserId ) as RecruitorName,  
										CoordinatorId,
										
										( select  CONCAT( trim( c.FirstName) , ' ', trim( c.LastName)) 
												from Users c
												Where CoordinatorId =  c.UserId ) as CoordinatorName,  
										a.UserId  
										FROM Clients a  
										where Id = '{$ClientId}'  "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}

			// Set Selected Client's general Info
            //=======================			
			function setSelClient(  $Id,
								    $SearchId,
									$ClientStatusId,	  
									$ClientName, 
									$StreetAddress1,  
									$StreetAddress2,  
									$City,  
									$State, 
									$ZipCode, 
									$OfficePhone,
									$Fax,
									$RecruitorId,
									$CoordinatorId,
									$UserId)
			
			{ 
				 
				if(is_numeric($Id))  { 	
			
 					    $query =" Update Clients
							set ClientStatusId =  '{$ClientStatusId}',  
								SearchId =  '{$SearchId}',
								ClientName =  '{$ClientName}',  
								StreetAddress1 =  '{$StreetAddress1}',  
								StreetAddress2 =  '{$StreetAddress2}',  
								City =  '{$City}',  
								State =  '{$State}',
								ZipCode =  '{$ZipCode}',
								OfficePhone =  '{$OfficePhone}',
								Fax=  '{$Fax}',
								RecruitorId=  '{$RecruitorId}',
								CoordinatorId=  '{$CoordinatorId}',
								UserId =  '{$UserId}',
								TransDate = Now()
						where Id = '{$Id}' ";  
               }    else { 
                       $query ="Insert into Clients ( 		SearchId,
															ClientStatusId,
															ClientName,
															StreetAddress1,  
															StreetAddress2,  
															City,  
															State,
															ZipCode,
															OfficePhone,
															Fax,
															RecruitorId,
															CoordinatorId,
															UserId,		
															TransDate )
							values ('{$SearchId}',
									'{$ClientStatusId}',							
									'{$ClientName}',  
									'{$StreetAddress1}',  
									'{$StreetAddress2}',  
									'{$City}',  
									'{$State}',
									'{$ZipCode}',
									'{$OfficePhone}', 
									'{$Fax}', 
									'{$RecruitorId}',
									'{$CoordinatorId}',
									'{$UserId}',
									 NOW() )  ";
								            

				} 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}	 

			// Get Client Messages
            //=======================			
		 	function getClientMessages($ClientId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								ClientId,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM ClientMessages a, Users b  
								WHERE ClientId = '{$ClientId}'
								AND a.UserId = b.UserId 
								Order By  a.TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			/* Set Client's Message */
            /*======================= */			
			function setClientMessages($ClientId,
										$HighPriority,
										$Msg,
										$UserId )  
			{
				
                       $query ="Insert into ClientMessages 
								(ClientId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$ClientId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	

			// Get Client's Units Listing
            //=======================			
			function getClientUnits($ClientId) {
                        $query = "SELECT Id as id, 
							Id as ClientUnitId, 
							UnitName AS sort,
							UnitName,
							SearchId
						FROM ClientUnits 
					WHERE ClientId = '{$ClientId}'
					ORDER BY (UnitName +0)  ASC, sort ASC";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get Selected Client's Unit Info
            //=======================			
			function getSelClientUnit($UnitId) {
			
                        $query ="SELECT Id as id, 
										Id, 
										ClientId, 
										ExtId, 
										SearchId, 
										StatusId, 
										UnitName, 
										StreetAddress1, 
										StreetAddress2, 
										City, 
										State, 
										ZipCode, 
										ContactFirstName, 
										ContactLastName, 
										OfficePhone, 
										Fax, 
										Email, 
										Comments, 
										UserId, 
										TransDate FROM ClientUnits 
										WHERE Id = '{$UnitId}'  "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			// Set Selected Client's Unit Info
            //=======================			
			function setSelClientUnit(  $Id,
								    $ClientId,
									$ExtId,
									$SearchId,
									$StatusId,	  
									$UnitName, 
									$StreetAddress1,  
									$StreetAddress2,  
									$City,  
									$State, 
									$ZipCode, 
									$OfficePhone,
									$Fax,
									$ContactFirstName,
									$ContactLastName,
									$Comments,
									$Email,
									$UserId)
			
			{ 
			
				$ExtId = $this->connection->escapeSimple ($ExtId); 




				if(is_numeric($Id))  { 	
			
 					    $query =" Update ClientUnits
							set ClientId =  '{$ClientId}',
								ExtId =  '{$ExtId}',
								StatusId =  '{$StatusId}',  
								SearchId =  '{$SearchId}',
								UnitName =  '{$UnitName}',  
								StreetAddress1 =  '{$StreetAddress1}',  
								StreetAddress2 =  '{$StreetAddress2}',  
								City =  '{$City}',  
								State =  '{$State}',
								ZipCode =  '{$ZipCode}',
								OfficePhone =  '{$OfficePhone}',
								Fax=  '{$Fax}',
								ContactFirstName =  '{$ContactFirstName}',
								ContactLastName=  '{$ContactLastName}',
								Comments =  '{$Comments}',
								Email =  '{$Email}',
								UserId =  '{$UserId}',
								TransDate = Now()
						where Id = '{$Id}' ";  
               }    else { 
                       $query ="Insert into ClientUnits ( 	ClientId,
															ExtId,
															SearchId,
															StatusId,
															UnitName,
															StreetAddress1,  
															StreetAddress2,  
															City,  
															State,
															ZipCode,
															OfficePhone,
															Fax,
															ContactFirstName,
															ContactLastName,
															Comments,
															Email,
															UserId,		
															TransDate )
							values ('{$ClientId}',
									'{$ExtId}',
									'{$SearchId}',
									'{$StatusId}',							
									'{$UnitName}',  
									'{$StreetAddress1}',  
									'{$StreetAddress2}',  
									'{$City}',  
									'{$State}',
									'{$ZipCode}',
									'{$OfficePhone}', 
									'{$Fax}', 
									'{$ContactFirstName}',
									'{$ContactLastName}',
									'{$Comments}',
									'{$Email}',
									'{$UserId}',
									 NOW() )  ";
								            

				} 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}	

			// Get Client Contacts Info
            //=======================			
			function getClientContacts($ClientId) {
			
                        $query ="SELECT Id as ContactId ,
								ClientId,
								Title,
								a.FirstName,
								a.LastName,
								OfficePhone,
								MobilePhone,
								Fax,
								a.Email,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM ClientContacts a, Users b  
								WHERE a.UserId = b.UserId  
								  and a.ClientId = '{$ClientId}'  "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			//  Update Client Contacts Info 
            //=======================			
			function setClientContacts(   
									$ContactId,
									$ClientId,
									$Title,
									$FirstName,
									$LastName,
									$OfficePhone,	
									$MobilePhone,
									$Fax,
									$Email,
									$UserId ) 
					{
					
					if(is_numeric($ContactId))  { 	

                         $query ="update ClientContacts 
						            set Title = '{$Title}', 
                                        FirstName = '{$FirstName}', 	
                                        LastName = '{$LastName}', 
                                        OfficePhone = '{$OfficePhone}', 
                                        MobilePhone = '{$MobilePhone}', 
										Fax = '{$Fax}',
										Email = '{$Email}',
                                        UserId = '{$UserId}',
                                        TransDate = now()										
								Where 	Id = '{$ContactId}' 	";
					} else {
							$query ="insert into ClientContacts
							                  (
												ClientId,
												Title,	 
												FirstName, 
												LastName, 		
												OfficePhone, 	
												MobilePhone , 
												Fax, 
												Email, 
												UserId,
												TransDate )	
												
						       values ( '{$ClientId}',
										'{$Title}',	 
										'{$FirstName}',	 
                                        '{$LastName}', 		
                                        '{$OfficePhone}', 
                                        '{$MobilePhone}', 
                                        '{$Fax}', 
										'{$Email}',
 										'{$UserId}',
                                         now()	)";
					
					}		

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}				
			// Get Idle Submissions in-need of Follow-up   
            //=======================			
			function getIdleSubmissions() {
			
                        $query ="SELECT ClientId,
								ClientName,  
								a.RegistrantId, 
								CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,  
								a.TransDate,
								abs(DATEDIFF( a.TransDate, curdate( ) )) as DaysIdle,
								a.Id as ClientSubmissionId,
								Recruitorid,
								CoordinatorId,
								(Select CONCAT( trim( d.FirstName ) , ' ', trim( d.LastName ) )    from Users d
									where RecruitorId = d.Userid ) as RecruitorName,
								(Select CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) from Users e
									where CoordinatorId = e.Userid ) as CoordinatorName,
								(Select CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) from Users e
									where CoordinatorId = e.Userid ) as CoordinatorNamePrt	
							FROM 	ClientConnectedRegistrants a,
									Clients b,
									Registrants c   
							WHERE 	a.StatusId = 1 
							        and a.NextApprStageId is NOT NULL 
									and abs(DATEDIFF( a.TransDate, curdate( ) )) > 4
									and a.ClientId = b.Id
									and a.RegistrantId = c.Id
									Order By CoordinatorName, ClientName"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			// Get Detached Registrants All   
            //=======================			
			function getDetachedRegistrantsAll() {
			
                        $query ="SELECT ClientId, 
								ClientName, 
								a.RegistrantId, 
								CONCAT( trim( c.LastName ) , ', ', trim( c.FirstName ) ) AS RegistrantName, 
								a.TransDate, 
								CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
								DetachReason, 
								Recruitorid, 
								CoordinatorId,
								
								(SELECT  CASE count(*) 
                                                    			WHEN 0 THEN ''
									ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
									END as SpecialtiesList
                                            			FROM RegistrantAttchedSpecialties f, Specialties b
                                            				where b.Id = f.SpecialtyId
                                                                        and  a.RegistrantId = f.RegistrantId) as SpecialtiesList
										 

							FROM ClientConnectedRegistrants a, Clients b, Registrants c, Users e
								WHERE a.StatusId =2
									AND a.UserId = e.UserId
									AND a.ClientId = b.Id
									AND a.RegistrantId = c.Id
									ORDER BY RegistrantName"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			// Get Active Submissions Chart Data
            //=======================			
			function getActiveSubmissionsChartData() {
			
  						$query = "Call  proc_CalcClientSubmisionsChart ()  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

            // Get Non-Compliant Registrants Listing ALL
            //==============================================			
			function getRegistrantsNonComplCredItemsList($CredItemId, $RegistrantTypeId, $ClientId) 
			{
             
            	$query = "Call  proc_getRegistrantsNonComplCredItemsList (	'{$CredItemId}',
            																'{$RegistrantTypeId}',
            																'{$ClientId}')  "; 
            						  
				$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                if (DB::isError($result)){
                    die("Could not query the database:<br />$query ".DB::errorMessage($result));
                }
	
				return $result;
					
			}				

           // Get Registrants with 30 days Expiring Credentilaing Items Listing ALL
            //=======================			
			function getRegistrant30DaysExpCredItemAll($ClientId) 
			{
			
 
							
                        $query = " SELECT a.Id as RegistrantCredItemTransId,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										CredItemDesc,
										a.CredItemId,
										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										CASE  ComplianceLevelId 
											WHEN '1' THEN 'Non-Compliant'
										ELSE 'Compliant'
										END AS ComplianceLevelDesc,
										a.CredItemId,
										a.CredItemStatus,
										CredItemStatusDesc,
										CredItemStatusColor,
										CredItemStatusBGColor,
										ComplianceLevelId,
										CredItemType,
														
										COALESCE(( SELECT Msg
										FROM RegistrantCredItemMessages b
										WHERE b.Id = ( SELECT max( c.Id )
											FROM RegistrantCredItemMessages c
											WHERE c.RegistrantCredItemTransId = a.Id )),'') as LastMessage,

										CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers,
										COALESCE(a.Results,'') as Results,	
										COALESCE(a.Comments,'') as Comments,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	RegistrantCredItems a,
									CredentialingItems b,
									Registrants c,
									RegistrantTypes f,
									CredentialingItemStatuses g,
									Users e 
						WHERE 	a.CredItemId = b.Id 
								AND a.RegistrantId = c.Id
								AND c.TypeId = f.Id
								AND c.StatusId = '1' 
								AND a.CredItemStatus = g.CredItemStatus
								AND a.UserId = e.UserId
								AND ComplianceLevelId !=1 
								AND DATEDIFF( ExpirationDate, CURDATE( )  ) between 0 and 30 
									AND EXISTS (SELECT 1 FROM ClientApprovedRegistrants d
							               WHERE a.RegistrantId = d.RegistrantId
							               AND  d.Status = '1'
							               AND  d.ClientId  =  '{$ClientId}'
									) 

							Order by RegistrantName, CredItemDesc "; 
 
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
						
			}	

			// Get Registrant Credentialing Items Email Info
            //=======================			
			function getEmailCredItems ($RegistrantId, $SenderName, $SenderEmail) {
						
						$query = "Call  proc_RegistrantCredEmail (	'{$RegistrantId}',
																	'{$SenderName}',
																	'{$SenderEmail}'
																  )  "; 
 						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);	
		
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query; 
						
			}	

			// Set Recipients (Registrants) for an Email Blast   
            //=======================			
			function getRegistrantsSelEmailBlast($SpecialtiesList) 	{ 
				 
                       $query ="SELECT 	CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,
										City , 
										State , 
										ZipCode , 
										Email, 
										(SELECT count(*) from RegistrantCredItems d where a.Id = d.RegistrantId AND ComplianceLevelId = 1 ) as CredItemsNonCompliant, 
										(SELECT count(*) from RegistrantCredItems d where a.Id = d.RegistrantId AND ComplianceLevelId != 1 ) as CredItemsCompliant,
										(SELECT count(*) from RegistrantCredItems d where a.Id = d.RegistrantId 
											AND ComplianceLevelId != 1 
											AND DATEDIFF( ExpirationDate, CURDATE( ) ) between 0 and 30) as CredItemsExp30Days, 
										(SELECT CASE count(*) WHEN 0 THEN '' ELSE group_concat( SpecialtyDesc SEPARATOR ', ' ) END as SpecialtiesList 
											FROM RegistrantAttchedSpecialties c, Specialties b 
											where b.Id = c.SpecialtyId 
											and   a.Id = c.RegistrantId) as SpecialtiesList, 
										(Select RegistrantTypeDesc from RegistrantTypes where Typeid = RegistrantTypes.Id) as RegistrantTypeDesc, 
										(Select RegistrantGroupId from RegistrantTypes where Typeid = RegistrantTypes.Id) as RegistrantGroupId 
								FROM Registrants a, RegistrantAttchedSpecialties b 
									where a.Id = b.RegistrantId
									AND Email != ''    
									AND SpecialtyId IN ({$SpecialtiesList})
									Order By UPPER(City), LastName, FirstName";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			 
	 		}	

			// Get Specialties for a Selected Registrant Listing   
            //=======================			
			function getSpecialties ($RegistrantId,  $RegistrantGroupId) {
			
                        $query ="SELECT distinct b.Id AS id, SpecialtyDesc
									FROM Specialties b
								WHERE NOT EXISTS (select 1 from RegistrantAttchedSpecialties c
														where b.Id = c.SpecialtyId 
														and  RegistrantId = '{$RegistrantId}'
														Order By SpecialtyDesc )";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			// Get Specialties for Email Pusposes Listing   
            //=======================			
			function getEmailSpecialties ($RegistrantTypeId) {
			
                        $query ="SELECT Specialties.Id AS id, SpecialtyDesc
									FROM Specialties, RegistrantTypeSpecialties a
								WHERE Specialties.Id = a.SpecialtyId and
					RegistrantTypeId  = '{$RegistrantTypeId}' 
						 Order By SpecialtyDesc ";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}	
			
			// Get Coordinators Listing   
            //=======================			
			function getCoordinators () {
			
                        $query ="SELECT UserId as CoordinatorId , CONCAT( trim( FirstName ) , ' ', trim( LastName ) ) AS UserName
									FROM Users
								WHERE UserStatusId =1
								AND UserGroupId = 3 
								ORDER BY LastName, FirstName";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}	

			// Get Recruitors Listing   
            //=======================			
			function getRecruitors () {
			
                        $query ="SELECT UserId as RecruitorId , CONCAT( trim( FirstName ) , ' ', trim( LastName ) ) AS UserName
									FROM Users
								WHERE UserStatusId =1
								AND UserGroupId = 2 
								ORDER BY LastName, FirstName";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}	
			// Get Client's Orientations History Info
            //============================================			
			function getClientOrientationsHistory($ClientId) {
			
                        $query ="SELECT a.Id AS id, 
						                a.Id as OrientationId,     
										a.ClientId,
										RequestedQty,
										(SELECT count(* ) FROM ClientApprovedRegistrants e
											WHERE a.Id = e.OrientationId) as MatchedQty,
										
										DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate, 
										(SELECT  CASE count(*) 
											WHEN 0 THEN ''
											ELSE group_concat( DATE_FORMAT( OrientAddtnlDate, '%m-%d-%Y' ) 	 SEPARATOR ', ' )
											END 
												FROM ClientOrientationAddtnlDates f
												WHERE a.Id = f.OrientationId) as OrientAddtnlDatesList,
							
										UnitName,
										a.ServiceTypeId,
										ServiceTypeDesc,
										
										COALESCE(a.Comments, '') as Comments, 
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	ClientOrientations a, 
											Users b, 
											ClientUnits e,
											ServiceTypes h	
									WHERE a.UserId = b.UserId
									AND a.ClientUnitId = e.Id
									AND a.ServiceTypeId = h.Id
									AND a.ClientId = '{$ClientId}' 
							Order By StartDate Desc   "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}
			
			// Get Client's Orientation Info
            //=======================			
			function getClientOrientations($ClientId) {
			
                        $query ="SELECT a.Id AS id, 
						                a.Id as OrientationId,     
										a.ClientId,
										a.ClientUnitId,
										UnitName,
										a.ServiceTypeId,
										ServiceTypeDesc,
										RequestedQty,
										(SELECT count(* ) FROM ClientApprovedRegistrants e
											WHERE a.Id = e.OrientationId) as MatchedQty,
										
										DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate, 
										(SELECT  CASE count(*) 
											WHEN 0 THEN ''
											ELSE group_concat( DATE_FORMAT( OrientAddtnlDate, '%m-%d-%Y' ) 	 SEPARATOR ', ' )
											END 
												FROM ClientOrientationAddtnlDates f
												WHERE a.Id = f.OrientationId) as OrientAddtnlDatesList,
							
										COALESCE(a.Comments, '') as Comments, 
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	ClientOrientations a, 
											Users b, 
											ClientUnits e,
											ServiceTypes h		
									WHERE a.UserId = b.UserId
									AND a.ClientUnitId = e.Id
									AND a.ServiceTypeId = h.Id
									AND StartDate >= CURDATE()
									AND a.ClientId = '{$ClientId}' 
							Order By StartDate  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		

			// Set Client's Orientation Info
           //=======================			
			function setClientOrientation(	$OrientationId,
											$ClientId,
											$ClientUnitId,
											$ServiceTypeId,
											$StartDate,
											$RequestedQty,
											$Comments,	
											$UserId) {
					
					if(is_numeric($OrientationId))  { 	

                         $query ="update ClientOrientations
						            set StartDate = '{$StartDate}', 		
                                        ClientUnitId = '{$ClientUnitId}',
										ServiceTypeId = '{$ServiceTypeId}',
										RequestedQty = '{$RequestedQty}', 	
                                        Comments = '{$Comments}', 		
                                        UserId = '{$UserId}',
                                        TransDate = now()										
								Where 	Id = '{$OrientationId}' 	";
					} else {
							$query ="insert into ClientOrientations
							             (	  ClientId,
											  ClientUnitId,
											  ServiceTypeId,
											  StartDate,
											  RequestedQty,
											  Comments,
											  UserId,
											  TransDate )
											  
						       values ( '{$ClientId}',
										'{$ClientUnitId}',
										'{$ServiceTypeId}',					
                                        '{$StartDate}', 		
                                        '{$RequestedQty}', 	
                                        '{$Comments}', 		
                                        '{$UserId}',
                                         now()	)";
					
					}		

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}	

			// Set Deete Client Orientation 
           //=======================			
			function setDeleteClientOrientation(	$OrientationId) {


					$query ="Delete from  ClientOrientations
								Where Id = '{$OrientationId}'";
					 

					$result = $this->connection->query($query);
                     if (DB::isError($result)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                    }				
				
					$query ="Delete from  ClientOrientationAddtnlDates
								Where OrientationId = '{$OrientationId}'";
					 

					$result = $this->connection->query($query);
                     if (DB::isError($result)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                    }				
				
				//return $result;
				return $query;			
			}				
			
			
			// Get Client Orientation's Additional Dates Info
            //===================================================			
			function getClientOrientationAddtnlDates($OrientationId) {
			
                        $query ="SELECT OrientationId,     
										DATE_FORMAT( OrientAddtnlDate, '%m-%d-%Y' ) as OrientAddtnlDate, 
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM ClientOrientationAddtnlDates a, Users b 
									WHERE a.UserId = b.UserId
									AND a.OrientationId = '{$OrientationId}' 
							Order By OrientAddtnlDate Desc  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}            
			

			/* Set Orientation Additional Dates Data
             =======================================	*/		
			function setClientOrientationAddtnlDates(	$OrientationId,
														$Data,
														$UserId) 
			{
					
							
                     // Delete All Existing Addtional Dates for a given Orientation
					//===============================================================					
					$query ="Delete from  ClientOrientationAddtnlDates
								Where OrientationId = '{$OrientationId}'";
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }

					// Insert New  Additional Dates for a given Orientation
					//====================================================================	

					
 
					foreach ($Data as $rec) {
								
							$OrientAddtnlDate = $rec['OrientAddtnlDate'];

							$query = "INSERT INTO ClientOrientationAddtnlDates
									  ( OrientationId,
										OrientAddtnlDate,
										UserId,
										TransDate
									  )
									VALUES (
										'{$OrientationId}',
										'{$OrientAddtnlDate}',
										'{$UserId}',
										NOW() ) ";
																		
							$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
							}
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}				
	

			//=======================			
			function getClientApprovalCandidates($ClientId) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										a.ClientUnitId,
										COALESCE((SELECT UnitName
											FROM ClientUnits d
											WHERE  a.ClientUnitId = d.Id),'') as UnitName, 
										
										a.ServiceTypeId,
										COALESCE((SELECT ServiceTypeDesc
											FROM ServiceTypes h
											WHERE  a.ServiceTypeId = h.Id),'') as ServiceTypeDesc, 
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END  
                                            FROM RegistrantAttchedSpecialties c, Specialties b
                                            where b.Id = c.SpecialtyId
                                              and  a.RegistrantId = c.RegistrantId) as SpecialtiesList,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrants a, 
							Users b,
							Registrants c,
							RegistrantTypes f
							
						WHERE a.UserId = b.UserId
						AND a.ClientId = '{$ClientId}' 
						AND a.RegistrantId = c.Id
						AND c.TypeId = f.Id
						AND a.Status in (0,2,3)
					Order By c.LastName, c.FirstName  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}					

			// Get Client's Approved Reggistrant Info
			//=======================			
			function getClientApprovedRegistrants($ClientId) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										a.Status,										
										a.RegistrantId,
										ClientName,
										UnitName,
										ServiceTypeDesc,
										
										COALESCE((Select group_concat( CredItemDesc
                                                 SEPARATOR ', ' ) 
                                        FROM    RegistrantCredItems k,  
                                                CredentialingItems g
                                            WHERE  c.Id  = k.RegistrantId 
                                            AND  k.CredItemId = g.Id
                                            AND ComplianceLevelId = 1
                                            AND ((g.CredItemCategory  !='2') || 
                                                ((g.CredItemCategory  = '2') && (k.StatusId != '0')))),'') as ExpiredCredItems,
										COALESCE((Select group_concat( CredItemDesc
			                                                 SEPARATOR ', ' ) 
			                                        FROM    RegistrantCredItems k,  
			                                                CredentialingItems g
			                                            WHERE  c.Id  = k.RegistrantId 
			                                            AND  k.CredItemId = g.Id
			                                            AND ComplianceLevelId = 2
			                                            AND ExpirationDate != '0000-00-00'
			                                            AND DATEDIFF( ExpirationDate, CURDATE( ) ) between 0 and 30
			                                            AND ((g.CredItemCategory  !='2') || 
			                                                ((g.CredItemCategory  = '2') && (k.StatusId != '0')))),'') as Expired30daysCredItems,


										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
										COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,

										
										(SELECT max(k.ServiceDate)  
										 FROM WeeklyServices k
										WHERE k.ScheduleStatusId > 6
										AND   k.RegistrantId = c.Id) as LastSchedDate ,

										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
						FROM 	ClientApprovedRegistrantsNonRest a, 
								Users b,
								Registrants c,
								RegistrantTypes f,
								Clients d,
								ClientUnits e,
								ServiceTypes h	
							
						WHERE a.UserId = b.UserId
						AND a.ClientId = '{$ClientId}' 
						AND a.ClientId = d.Id
						AND a.RegistrantId = c.Id
						AND a.ClientUnitId = e.Id
						AND a.ServiceTypeId = h.Id
						
						AND c.TypeId = f.Id
						AND a.Status = '1'
					Order By c.LastName, c.FirstName 	 "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			// Set Client's Approved Registrant Info
            //===========================================			
			function setClientApprovedRegistrants( 	$Id,
													$Status,
													$ClientId,
													$ClientUnitId,
													$ServiceTypeId,
													$RegistrantId,	
													$OrientationId,
													$Comments,
													$SearchId,		
													$UserId)
			{ 
				 
				if(is_numeric($Id))  { 	
			
 					    $query =" Update ClientApprovedRegistrants
							set Status =  '{$Status}',  
								OrientationId =  '{$OrientationId}',
								Comments =  '{$Comments}', 
  								UserId =  '{$UserId}',
								TransDate = Now()
						where Id = '{$Id}' ";  
               }    else { 
                       $query ="Insert into ClientApprovedRegistrants ( 
										SearchId,
										Status, 
										ClientId, 
										ClientUnitId,
										ServiceTypeId,
										RegistrantId, 
										UserId,		
										TransDate )
							values ('{$SearchId}',
									'0',
									'{$ClientId}',	
									'{$ClientUnitId}',	
									'{$ServiceTypeId}',	
									'{$RegistrantId}',  
									'{$UserId}',
									 NOW() )  ";
								            

				} 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}	 
			
           //=======================			
			function getClientApprovalCandidateComments($ApprovalId) {
			
				$query ="SELECT	a.Comments, 
					CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
					TransDate as TransDateSort,
					DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
						FROM 	ClientApprovedRegistrantComments a, 
							Users b
							
						WHERE a.UserId = b.UserId
						AND a.ApprovalId = '{$ApprovalId}' 
						
					Order By TransDateSort Desc  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		
			
            //=======================			
			function getClientApprovedRegistrantComments($ApprovalId) {
			
				$query ="SELECT	a.Comments, 
					CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
					TransDate as TransDateSort,
					DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
						FROM 	ClientApprovedRegistrantComments a, 
							Users b
							
						WHERE a.UserId = b.UserId
						AND a.ApprovalId = '{$ApprovalId}' 
						
					Order By TransDateSort Desc  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		

            //=======================			
			function setNewClientApprovedRegistrantMessage(	$SearchId, 
															$Msg,
															$UserId) 
			{
			
				$query ="INSERT INTO ClientApprovedRegistrantComments
						(
							ApprovalId, 
							Comments, 
							UserId, 
							TransDate) 
							VALUES 
							( 
								( SELECT Id FROM ClientApprovedRegistrants
								  WHERE SearchId = '{$SearchId}'),
								  '{$Msg}',
								  '{$UserId}',
								  NOW()
							  
							)  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		
			
            //=======================			
			function setClientApprovedRegistrantMessage(	$Id, 
															$Msg,
															$UserId) 
			{
			
				$query ="INSERT INTO ClientApprovedRegistrantComments
						(
							ApprovalId, 
							Comments, 
							UserId, 
							TransDate) 
							VALUES 
							( 
								  '{$Id}',
								  '{$Msg}',
								  '{$UserId}',
								  NOW()
							  
							)  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		


			/* Get Candidates Selection for Client Approval Info
            //================================================ */			
			function getClientApprovalCandidatesSelection(	$ClientId,
															$RegistrantTypeId,
															$SpecialtyId,
															$NameSeach
														  ) 
			{
                        								
						$query = "Call proc_ClientApprovalCandidatesSel ('{$ClientId}','{$RegistrantTypeId}','{$SpecialtyId}','{$NameSeach}' ) "; 
                        
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			/* Get Candidates Selection for Client Approval Info
            //================================================ */			
			function getClientApprovalCandidatesSel(	$ClientId,
														$ClientUnitId,
														$ServiceTypeId,
														$SpecialtiesList
													) 

			{			


					$query = "SELECT DISTINCT a.Id as RegistrantId,
					       CONCAT( trim( a.LastName) , ', ', trim( a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					       
					       COALESCE(DATE_FORMAT( (SELECT max(ServiceDate) from WeeklyServices g
					           where a.Id = g.RegistrantId
					           AND ScheduleStatusId in (7,8)), '%m-%d-%Y' ),'') as LastSchedDate,
				       		a.City,
					       CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone)) as PhoneNumbers, 

						 (SELECT  CASE count(*)
					               WHEN 0 THEN ''
					                   ELSE group_concat( CredItemDesc SEPARATOR ', ' )
					               END  
					           FROM RegistrantCredItems d, CredentialingItems b
					               WHERE a.Id = d.RegistrantId
					               AND b.Id = d.CredItemId
					               AND ComplianceLevelId =1 )     as NonComplList,
					      
					       SUBSTRING(a.ZipCode,1,5) as ZipCode					      
					   FROM    Registrants a,
					   		   RegistrantAttchedSpecialties b,	
					           RegistrantTypes f
					   		WHERE  a.TypeId = f.Id
					           AND a.StatusId = 1
					           AND a.Id = b.RegistrantId
     						   AND b.SpecialtyId in ($SpecialtiesList) 
	
							   AND NOT EXISTS (SELECT 1 from ClientApprovedRegistrants c
												WHERE a.Id = c.RegistrantId
												AND   c.ClientId = '{$ClientId}'      
					                            AND   c.ClientUnitId = '{$ClientUnitId}'
					                            AND   c.ServiceTypeId = '{$ServiceTypeId}' 
												)
								ORDER BY a.LastName, a.FirstName 
								";				

                       								
 						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		
			
			// Get Registrant Types Listing
            //=======================			
			function getRegistrantTypes() {
			
                        $query = "SELECT Id as id, 	
										 Id as RegistrantTypeId,
									     RegistrantGroupId ,
                                         RegistrantTypeDesc  										 
									FROM RegistrantTypes
									WHERE RegistrantStatusId = '1'
									order by RegistrantTypeDesc";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			
			// Get Service Types Listing
            //=======================			
			function getServiceTypes() {
			
                        $query = "SELECT Id as id, 	
										 Id as RegistrantTypeId,
                                         ServiceTypeDesc  										 
									FROM ServiceTypes
									ORDER by ServiceTypeDesc";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			


			// Get Specialties for selected Reguistrant Type  Listing   
            //===========================================================			
			function getRegistrantTypeSpecialties ($RegistrantTypeId) {
			
                        $query ="SELECT Specialties.Id AS id, SpecialtyDesc
									FROM Specialties, RegistrantTypeSpecialties a
								WHERE Specialties.Id = a.SpecialtyId and
					RegistrantTypeId  = '{$RegistrantTypeId}' 
						 Order By SpecialtyDesc ";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}	

			// Get Registrant Side Navigation Data
            //=======================			
			function getRegistrantSideNavigation() {
			
                        $query = "SELECT *							
						FROM RegistrantSideNavigationNew 
					    WHERE Status = 1
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


			// Get Registrant Web Navigation Data
            //=======================			
			function getRegistrantWebNavigation() {
			
                        $query = "SELECT *							
						FROM RegistrantWebNavigation 
					    WHERE Status = 1
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// get Selected Registrant's Info 
			//=======================================	
			function getSelRegistrant($RegistrantId) {
                       $query ="SELECT  Id as id, 
					                    Id,
										StatusId,
										SearchId,
										ExtId,
										TerminationType,
										TerminationReason,
										DATE_FORMAT( TerminationDate, '%m-%d-%Y' ) as TerminationDate,
										DATE_FORMAT( BirthDate, '%m-%d-%Y' ) as BirthDate,
										DATE_FORMAT( HireDate, '%m-%d-%Y' ) as HireDate,
										TypeId,
										TypeId as OrigTypeId,
										FirstName,
										LastName,
										CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,
										MiddleInitial,
										StreetAddress1,
										StreetAddress2,
										City ,
										State ,
										ZipCode ,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
										Fax,
										Email,
										CASE NextDayPay 
											WHEN '0' THEN 'No'
										ELSE 'Yes'
										END AS NextDayPayFlag,
										
										COALESCE(Gender, '') as Gender,
										COALESCE(Race, '') as Race,
										COALESCE(CheckType, '') as CheckType,
									 	COALESCE(W4Status, '') as W4Status,
										Exemptions, 
										

										Availability,
										HospitalExp,
										Shifts,
										DATE_FORMAT( LastPayDate, '%m-%d-%Y' ) as LastPayDate,
                                        CASE COALESCE(LastPayDate, 0)  
                                            WHEN '0' THEN ''
                                            ELSE DATE_FORMAT( LastPayDate, '%m-%d-%Y' )
                                        END as LastPayDate,
										PerDiem,
										ThrnWeekContract,
										NewGraduate,
										ForeignTrained,
										NextDayPay,
										UserId,
										TransDate,

												
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END as SpecialtiesList
                                            FROM RegistrantAttchedSpecialties a, Specialties b
                                            where b.Id = a.SpecialtyId
                                              and  a.RegistrantId = Registrants.Id) as SpecialtiesList,
										(Select RegistrantTypeDesc from RegistrantTypes
											where Typeid  = RegistrantTypes.Id) as  RegistrantTypeDesc,
										(Select  	RegistrantGroupId from RegistrantTypes
											where Typeid  = RegistrantTypes.Id) as  RegistrantGroupId,
										COALESCE((Select  	CONCAT(StoredName, '.pdf' ) from RegistrantDocuments a
											where a.RegistrantId = Registrants.Id
										    AND DocumentTypeId = '1'),'') as  RegistrantResumeName
												
									FROM Registrants 
								  where Id = '{$RegistrantId}' ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			

			}

			/* Set Selected Registrants's general Info
            //=======================	*/		
			function setSelRegistrant(  $Id,
										$SearchId,
										$StatusId,	
										$ExtId,
										$TerminationType,
										$TerminationReason,	
										$TerminationDate,
										$BirthDate,
										$HireDate,
										$Gender,
										$Race,
										$CheckType,
										$W4Status,
										$Exemptions,
										$TypeId,
										$FirstName,
										$LastName,
										$MiddleInitial,
										$StreetAddress1,
										$StreetAddress2,
										$City,
										$State,
										$ZipCode,
										$MobilePhone,
										$HomePhone,
										$Fax,
										$Email,
										$Availability,
										$HospitalExp,
										$Shifts,
										$NextDayPay,
										$PerDiem,
										$ThrnWeekContract,
										$NewGraduate,
										$ForeignTrained,
										$UserId  )
			 
			
			{

				
				$LastName = $this->connection->escapeSimple ($LastName); 


				if(is_numeric($Id) ) { 	
			
                        $query ="Update Registrants 
								set StatusId =  '{$StatusId}', 
								TerminationType =  '{$TerminationType}',
								TerminationReason =  '{$TerminationReason}',
								TerminationDate =  '{$TerminationDate}',
								BirthDate =  '{$BirthDate}',
								HireDate =  '{$HireDate}',
								Gender =  '{$Gender}',

								Race =  '{$Race}',
								CheckType =  '{$CheckType}',
								W4Status =  '{$W4Status}',
								Exemptions =  '{$Exemptions}',

								TypeId =  '{$TypeId}',
								ExtId =  '{$ExtId}',
								FirstName =  '{$FirstName}',
								LastName	 =  '{$LastName}',
								MiddleInitial	 =  '{$MiddleInitial}',
								StreetAddress1	 =  '{$StreetAddress1}',
								StreetAddress2	 =  '{$StreetAddress2}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								MobilePhone	 =  '{$MobilePhone}',
								HomePhone	 =  '{$HomePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								Availability = '{$Availability}',
								HospitalExp = '{$HospitalExp}',
								Shifts = '{$Shifts}',
								PerDiem = '{$PerDiem}',
								NextDayPay = '{$NextDayPay}',
								ThrnWeekContract = '{$ThrnWeekContract}',
								NewGraduate = '{$NewGraduate}',
								ForeignTrained = '{$ForeignTrained}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into Registrants  
								(StatusId, 
                                SearchId, 		
                                ExtId,								
								TerminationType,
								TerminationReason,
								TerminationDate,
								BirthDate,
								HireDate,
								Gender,

								Race,
								CheckType,
								W4Status,
								Exemptions,

								TypeId,
								FirstName,
								LastName,
								MiddleInitial,
								StreetAddress1,
								StreetAddress2,
								City,
								State,
								ZipCode,
								MobilePhone,
								HomePhone,
								Fax,
								Email,
								Availability,
								HospitalExp,
								Shifts,
								NextDayPay,
								PerDiem,
								ThrnWeekContract,
								NewGraduate,
								ForeignTrained,
								EntryDate,
								UserId,
								TransDate )
				values 	('{$StatusId}',  
						'{$SearchId}',	
						'{$ExtId}',
						'{$TerminationType}',
						'{$TerminationReason}',						
						'{$TerminationDate}',
						'{$BirthDate}',
						'{$HireDate}',
						'{$Gender}',
						
						'{$Race}',
						'{$CheckType}',
						'{$W4Status}',
						'{$Exemptions}',

						'{$TypeId}',  
						'{$FirstName}',  
						'{$LastName}',  
						'{$MiddleInitial}',  
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$MobilePhone}', 
						'{$HomePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$Availability}',
						'{$HospitalExp}',
						'{$Shifts}',
						'{$NextDayPay}',
						'{$PerDiem}',
						'{$ThrnWeekContract}',
						'{$NewGraduate}',
						'{$ForeignTrained}',
						NOW(),
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
						
			}			

			// get Selected Registrant's Additional Info 
			//=======================================	
			function getRegistrantAddtionalInfo($RegistrantId) {
                       $query ="SELECT 	
								COALESCE(MobilePhone,'') as MobilePhone,
								COALESCE(HomePhone,'') as HomePhone,
								CASE Email 
									WHEN '' THEN 'No'
								ELSE 'Yes'
								END AS HasEmail,
								(SELECT RegistrantTypeDesc FROM RegistrantTypes
											WHERE Typeid  = RegistrantTypes.Id) as  RegistrantTypeDesc,	

								CASE COALESCE(LastPayDate, 0)  
									WHEN '0' THEN ''
									ELSE DATE_FORMAT( LastPayDate, '%m-%d-%Y' )
								END as LastPayDate,
								(SELECT count(*) from RegistrantCredItems d, CredentialingItems b
										where Registrants.Id = d.RegistrantId
										and d.CredItemId = b.Id
										and (!(b.CredItemCategory = 2 and d.StatusId = '0'))
										/*AND  d.StatusId in (1,2)*/	
										AND ComplianceLevelId != 2 ) as CredItemsNonCompliant,    
										
								(SELECT count(*) from RegistrantCredItems d
										where Registrants.Id = d.RegistrantId
										/*AND d.StatusId in (1,2)*/	
										AND ComplianceLevelId = 2 ) as CredItemsCompliant,

								(SELECT count(*) from RegistrantCredItems d
										where Registrants.Id = d.RegistrantId
										/*AND d.StatusId in (1,2)*/	
										AND DATEDIFF( ExpirationDate, CURDATE( ) ) between 0 and 30) as CredItemsExp30Days
							FROM Registrants 
							WHERE Id = '{$RegistrantId}' ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			

			}
			
			// Get Registrants  Listing
            //=======================			
			function getRegistrants($Statuses, $SearchId) {
			
                        $query = "SELECT a.Id as id,  
											TypeId,
											ExtId,
											SearchId,			
											CONCAT( trim( LastName) , ', ', trim(FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
										FROM Registrants a, RegistrantTypes
										WHERE Typeid  = RegistrantTypes.Id 
										AND StatusID in {$Statuses}
										AND SearchId like '{$SearchId}'
										order by LastName, FirstName";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;

			}

			// Get Registrant Statuses Listing
            //=======================			
			function getRegistrantStatuses() {
			
                        $query = "SELECT Id as id,
										RegistrantStatusDesc  										 
									FROM RegistrantStatuses";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			
			
			// Get Registrant Document Types Listing
            //=======================			
			function getRegistrantDocumentTypes() {
			
                        $query = "SELECT Id as id,
										CredItemDesc as DocumentTypeDesc  										 
									FROM CredentialingItems
									Order By CredItemDesc";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			// Get Registrant Documents Listing
            //=======================================			
			function getRegistrantDocuments($RegistrantId) {
			
                        $query = "SELECT 	a.Id as id, 
											DocumentTypeId, 
											CredItemDesc as DocumentTypeDesc,
											DocumentName, 
											StoredName, 
											a.UserId, 
											CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
											DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										FROM RegistrantDocuments a, CredentialingItems b, Users c 
										WHERE a.DocumentTypeId = b.Id
										AND a.UserId = c.UserId
										AND RegistrantId = '{$RegistrantId}'";
									
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			// Get Specialties Listing for a given Registrant  
            //=======================			
			function getRegistrantSpecialties ($RegistrantId) {
			
                        $query ="Select SpecialtyId as id, SpecialtyDesc 
							from RegistrantAttchedSpecialties, Specialties 
						where SpecialtyId = Specialties.Id and
						  RegistrantId  = '{$RegistrantId}' 
						  Order By SpecialtyDesc";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


			//  Update Registrant's Specialties     
            //=======================			
			function setRegistrantSpecialties($RegistrantId,
										$Specialties,			
										$UserId) {
					
 
							
 
                    // Delete All Existing Specialties for an Registrant
					//===============================================					
					$query ="Delete from  RegistrantAttchedSpecialties
								Where RegistrantId = '{$RegistrantId}'";
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }

					// Insert newaly Selected Specialties for an Registrant
					//===============================================					

					foreach ($Specialties as $SpecialtyId) {
								
							$Sel_Speciality = $SpecialtyId['id'];
							
												
						$query ="Insert into RegistrantAttchedSpecialties
						   (RegistrantId, SpecialtyId, UserId, TransDate)   
						values ( '{$RegistrantId}',  '{$Sel_Speciality}', '{$UserId}',  now()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/* Get Registrant Credentialing Items  
            //=============================	*/		
			function getRegistrantCredItems($RegistrantId) {
			
                        $query ="SELECT a.Id as RegistrantCredItemTransId,
										RegistrantId,
										a.CredItemId,
										a.StatusId,
										b.CredItemCategory,
										CredItemType,
										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										CredItemDesc,
										a.CredItemStatus,
										CredItemStatusColor,
										CredItemStatusBGColor,
										CredItemStatusDesc,
										ComplianceLevelId,
										'' as CondItemSelId,
										''as CredItemCondGrpId,
										
										CASE  ComplianceLevelId 
											WHEN '1' THEN 'Non-Compliant'
										ELSE 'Compliant'
										END AS ComplianceLevelDesc,
										COALESCE((SELECT StoredName FROM RegistrantDocuments h
											WHERE a.RegistrantId = h.RegistrantId
											AND   a.CredItemId = h.DocumentTypeId),'') as StoredDocName,	
										
										
										COALESCE(b.Comments,'') as Comments,
										COALESCE(a.Results,'') as Results,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										COALESCE(DATE_FORMAT( a.OrigDocumentDate, '%m-%d-%Y' ),'')  as OrigDocumentDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	RegistrantCredItems a,
									CredentialingItems b,
									CredentialingItemStatuses g,
									Users e 
						WHERE 	a.CredItemId = b.Id 
								AND a.UserId = e.UserId
								AND a.CredItemStatus = g.CredItemStatus
								AND RegistrantId = '{$RegistrantId}' 
							ORDER BY CredItemDesc"; 
								 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

          /* Set Registrant Credentialing Items  
          //=======================	*/		
			function setRegistrantCredItems($RegistrantId,
											$CredItemId, 
											$CredItemStatus,
											$ComplianceLevelId,
											$OrigDocumentDate,
											$ExpirationDate,
											$Results,
											$UserId) {
			
                        $query ="Update RegistrantCredItems 
								Set CredItemStatus = '{$CredItemStatus}',
								    ComplianceLevelId = '{$ComplianceLevelId}', 
									PrevExpirationDate = ExpirationDate,
									OrigDocumentDate = '{$OrigDocumentDate}',
									ExpirationDate = '{$ExpirationDate}',
									Results = '{$Results}',
								    UserId = '{$UserId}',
									TransDate = now()
							WHERE 	RegistrantId = '{$RegistrantId}' 
									AND CredItemId = '{$CredItemId}' ";     
                                   	 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			/* Get Registrant Cred Item Messages
            //=======================	*/		
			function getRegistrantCredItemMsgs($RegistrantCredItemTransId) {
			
                        $query ="SELECT a.Id as id ,
						        a.Id as Id ,
								Msg,
								HighPriority,
								CommunicationModeId,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDateSort,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM RegistrantCredItemMessages a, Users b, CommunicationModes c  
								WHERE RegistrantCredItemTransId =  '{$RegistrantCredItemTransId}'	
								AND a.UserId = b.UserId 
								AND CommunicationModeId = c.Id
								Order By  TransDateSort Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			/* Set  Registrant Cred Item Messages
            //=======================	*/		
			function setRegistrantCredItemMsgs(	$RegistrantCredItemTransId, 
												$Msg,
												$HighPriority,	  
												$UserId)
			
			{ 
				 
                       $query ="Insert into RegistrantCredItemMessages ( RegistrantCredItemTransId,
															Msg,
															HighPriority,
															UserId,		
															TransDate )
							values  ('{$RegistrantCredItemTransId}',
									'{$Msg}',  
									'{$HighPriority}',  
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}				
			
			/* Set Switch Registrant Conditional Crededntialing Item
            //======================= */ 			
			function setSwitchRegistrantCondItem(	$RegistrantCredItemTransId, 
													$CredItemCondGrpId,
													$CondItemSelId,
													$UserId) {
			
 
							
                        $query = "Call proc_SwitchSchoolRegistrantCredItem ('{$RegistrantCredItemTransId}', '{$CredItemCondGrpId}', '{$CondItemSelId}', '{$UserId}' )  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			//=======================================			
			/* Get Registrant's Client Approvals    */
           //========================================			
			function getRegistrantClientsApprovals($RegistrantId) {
			
                        $query ="SELECT a.ClientId AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										c.ClientName,
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
						FROM 	ClientApprovedRegistrantsNonRest a, 
							Users b,
							Clients c
							
						WHERE a.UserId = b.UserId
						AND a.RegistrantId = '{$RegistrantId}' 
						AND a.ClientId = c.Id
					 
						
					Order By c.ClientName "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		

          // Get Client Weekly Schedules
            //=======================			
			function getClientWklySchedules($ClientId, 
											$PayrollWeek, 
											$ServiceDate,			
											$InclClientUnitId,
											$ServiceTypeId	
											) 
			{
			
 
							
                        $query = "Call proc_ClientWklySchedules ('	{$ClientId}', '
                        											{$PayrollWeek}', 
                        											'{$ServiceDate}',
                        											'{$InclClientUnitId}',
                        											'{$ServiceTypeId}'	
                        											)  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}				

			// Get Schedule Messages
            //=======================			
		 	function getClientWklySchedulesMsgs($ScheduleId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDate1,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM WeeklyServicesMessages a, Users b  
								WHERE ScheduleId = '{$ScheduleId}' 
								AND a.UserId = b.UserId 
								Order By  TransDate1 Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			// Get Client Weekly Schedule "Summary" Info
            //=======================			
			function getClientWklyScheduleSummary ($ClientId,	  
											$PayrollWeek) {
						
						$query = "Call  proc_ClientWklyScheduleSummary ('{$ClientId}', '{$PayrollWeek}')  "; 
 						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);	
		
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query; 
						
			}	
			//=======================
			// Get Service Time
            //=======================			
			function getServiceTime() {
													 
                       $query ="SELECT 	ServTime as id,
										ServTime,
                                        ServTimeDesc  					   
							FROM DailyServiceTimes
						Order by TimeSorter  ";
							
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;	
			}				
		 	
			//=======================
			// Get Schedule Statuses Listing
            //=======================			 
			function getScheduleStatuses() {
			
                        $query = "SELECT Id as id,
										ScheduleStatusDesc  										 
									FROM ScheduleStatuses
									WHERE OpenStatusFlag = 0";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			// Get Schedule Shifts Listing
            //=======================			
			function getScheduleShifts() {
			
                        $query = "SELECT Id as id, 	
										 Id as ScheduleShiftId,
									     ScheduleShiftDesc ,
                                         StartTime,
										 EndTime			
									FROM ScheduleShifts
									order by Id";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			// Get Default Shifts Listing
            //=======================			
			function getDefaultShifts() {
			
                        $query = "SELECT Id as id, 	
										 Id as ShiftId,
									     ShiftNameDefault as ShiftName,
                                         StartTime,
										 EndTime,
										 TotalHours		
									FROM DefaultShifts
									order by Id";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			
			
			// Check for Registrant's Duplicate Schedules
            //=======================			
			function getRegistrantDupSchedules($ServiceDate,
											 $StartTime,
											 $EndTime,
										     $RegistrantId) {

						$query ="SELECT count(*) as dup_shift
									FROM  WeeklyServices 
							WHERE  ServiceDate  = '{$ServiceDate}'
							AND  StartTime  >= '{$StartTime}'
							AND  EndTime  <= '{$EndTime}'
							AND  RegistrantId  = '{$RegistrantId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

           // Set Client Weekly Schedule
            //=======================			
			function setClientWklySchedules($PayrollWeek,
									$ClientId,
									$ClientUnitId,
									$ScheduleId,
									$ScheduleStatusId,
									$ServiceDate,
									$StartTime,
									$EndTime,
									$TotalHours,
									$WeekDay, 
									$RegistrantId,
									$RegistrantTypeId,
									$ScheduleOrigBy,
									$UserId ) 
					{
				
							$query ="insert into WeeklyServices
							                  (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												RegistrantId, 
												RegistrantTypeId,
												ScheduleOrigBy,
												UserId,
												TransDate )	
												
						       values (
										'{$PayrollWeek}',
										'{$ClientId}',
										'{$ClientUnitId}',
										'{$ScheduleStatusId}',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
                                        '{$RegistrantId}', 
										'{$RegistrantTypeId}',
										'{$ScheduleOrigBy}',
 										'{$UserId}',
                                         now()	)";
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;  
				return $query;			
			}				

			/* Get Client Weekly Services Messages
            //=======================	*/		
		 	function getClientWklyServicesMsgs($ServiceId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
								
								WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDate1,
								a.TransDate as TransDate
							FROM WeeklyServicesMessages a, Users b  
								WHERE ServiceId = '{$ServiceId}' 
								AND a.UserId = b.UserId 
								Order By  TransDate1 Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}				
			
			/* Set Client Weekly Schedule Message */
            /*======================= */			
			function setClientWklyServicesMsg(	$ServiceId,
													$HighPriority,
													$Msg,
													$UserId )  
			{
				
                       $query ="Insert into WeeklyServicesMessages 
								(ServiceId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$ServiceId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						/*return $result;*/ 
						return $query;
						
			}	

           //=======================			
			function setClientWklyScheduleMsg(	$ScheduleId, 
												$Msg,
												$HighPriority,	  
												$UserId) 	{ 
				 
                       $query ="Insert into WeeklyServicesMessages (	ScheduleId,
																		Msg,
																		HighPriority,
																		UserId,		
																		TransDate )
							values  ('{$ScheduleId}',
									'{$Msg}',  
									'{$HighPriority}',  
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}	

			// Get Client Approved Registrants by Proximity Data
            //=======================			
			function getClientApprRegistrantsByProximity (	$ClientId, 
															$RegistrantTypeId,
															$ServiceDate,
															$StartTime,
															$EndTime ) {
			
  						$query = "Call proc_RegistrantProximityToClient ('{$ClientId}', '{$RegistrantTypeId}' , '{$ServiceDate}' , '{$StartTime}' , '{$EndTime}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
											 

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Set Schedule Shifts Status
            //=======================			
			function setClientWklyScheduleStatus(
								$ScheduleId,  
								$ScheduleStatusId, 
								$RegistrantConfFL, 
								$ClientConfFL, 
								$RegistrantId, 
								$UserId	) {
			
                        $query = "update WeeklyServices
									set ScheduleStatusId = '{$ScheduleStatusId}',
										RegistrantConfFL = '{$RegistrantConfFL}',
										ClientConfFL = '{$ClientConfFL}',
										RegistrantId = '{$RegistrantId}',
										UserId = '{$UserId}',
										TransDate = now()
									Where 	Id = '{$ScheduleId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Set Cancelled By Registrant Info
            //==================================================			
			function setCancelledByRegistrant(	$RegistrantId,
												$ScheduleId, 
												$ServiceCancellationReasonId,
												$CancelReason,
												$UserId) 	{ 
				 
                       $query ="Insert into RegistrantServiceCancellations (   	RegistrantId,
																				ScheduleId,
																				ServiceCancellationReasonId,
																				CancelReason,
																				Userid,
																				TransDate)
							values  ('{$RegistrantId}',
									'{$ScheduleId}',  
									'{$ServiceCancellationReasonId}',
									'{$CancelReason}',
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}			

			// Set Cancelled By Client Info
            //=========================================			
			function setCancelledByClient(	$ClientId,
											$ScheduleId, 
											$ServiceCancellationReasonId,
											$CancelReason,
											$UserId) 	{ 
				 
                       $query ="Insert into ClientServiceCancellations (  ClientId,
																		ScheduleId,
																		ServiceCancellationReasonId,
																		CancelReason,
																		Userid,
																		TransDate)													 
							values  ('{$ClientId}',
									'{$ScheduleId}',  
									'{$ServiceCancellationReasonId}',
									'{$CancelReason}',
									'{$UserId}',
									 NOW() )  ";
				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}
			 
			// Set Cancelled By Coordinator Info
            //============================================			
			function setCancelledByCoordinator(	$CoordinatorId,
												$ScheduleId, 
												$ServiceCancellationReasonId,
												$CancelReason,
												$UserId) 	{ 
				 
                       $query ="Insert into CoordinatorServiceCancellations ( 	CoordinatorId,
																				ScheduleId,
																				ServiceCancellationReasonId,
																				CancelReason,
																				Userid,
																				TransDate)							 
							values  ('{$CoordinatorId}',
									'{$ScheduleId}',  
									'{$ServiceCancellationReasonId}',
									'{$CancelReason}',
									'{$UserId}',
									 NOW() )  ";
				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}
			// Set Confirmed Schedules By Client Info
            //=======================			
			function getConfirmedSchedulesByClient(	$ClientId,
													$StartDate, 
													$EndDate) 	{
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										ScheduleStatusId, 
										ScheduleStatusDesc,
										TextColor,
										BackgroundColor,
										a.ClientId, 
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										PayrollWeek,  
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											ScheduleStatuses g								
										Where 	a.ClientId= '{$ClientId}' 
												AND a.ScheduleStatusId > 6 
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND  ServiceDate between '{$StartDate}' and '{$EndDate}' 
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
											ORDER BY ServiceDate, StartTime ";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}

			// Set Registrant Schedules By Client Info
            //=======================			
			function getRegistrantWklyAvailability(	$RegistrantId,
													$PayrollWeek) 	{
				 
                       $query ="SELECT 	ServiceDate, 
										DAYOFWEEK(ServiceDate) as DayId, 
										WeekDay, 
										ShiftId,
										CASE 
											WHEN (ShiftId ='1' ) THEN CONCAT(WeekDay, 'DayShift')  
												ELSE '0' 
										END as DayShift,
										CASE  
											WHEN (ShiftId ='2' ) THEN CONCAT(WeekDay, 'EveningShift')  
												ELSE '0' 
										END as EveningShift,
										CASE  
											WHEN (ShiftId ='3' ) THEN CONCAT(WeekDay, 'NightShift')  
												ELSE '0' 
										END as NightShift,
										CASE  
											WHEN (ShiftId ='4' ) THEN CONCAT(WeekDay, '12HrsDayShift')  
												ELSE '0' 
										END as 's12HrsDayShift',
										CASE  
											WHEN (ShiftId ='5' ) THEN CONCAT(WeekDay, '12HrsNightShift')  
												ELSE '0' 
										END as 's12HrsNightShift'
										
								FROM RegistrantVerifiedAvailability
								WHERE PayrollWeek = '{$PayrollWeek}' 
								AND RegistrantId = '{$RegistrantId}'";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}
			// Set Registrant Weekly Schedule
            //=======================			
			function setRegistrantWklySchedules($PayrollWeek,
									$ClientId,
									$ClientUnitId,
									$ScheduleId,
									$ScheduleStatusId,
									$ServiceDate,
									$WeekDay, 
									$RegistrantId,
									$RegistrantTypeId,
									$ShiftId,
									$UserId ) 
					{
				
							$query ="insert into WeeklyServices
							                  (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												RegistrantId, 
												RegistrantTypeId,
												ShiftId,
												RegistrantConfFL,
												UserId,
												TransDate )	
												
						       SELECT
										'{$PayrollWeek}',
										'{$ClientId}',
										'{$ClientUnitId}',
										'{$ScheduleStatusId}',
										'{$ServiceDate}',	 
										StartTime,	 
                                        EndTime, 		
                                        TotalHours, 
										'{$WeekDay}',
                                        '{$RegistrantId}', 
										'{$RegistrantTypeId}',
										'{$ShiftId}',
										'1',
 										'{$UserId}',
                                         NOW()	 
								FROM ClientShifts
									WHERE ClientId = '{$ClientId}'
									AND ShiftId = '{$ShiftId}' ";
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				return $result;  
				//return $query;			
			}				

			// Set Registrant Weekly Availability
            //=======================			
			function setRegistrantWklyAvailability(	$PayrollWeek,
													$ServiceDate,
													$WeekDay, 
													$RegistrantId,
													$ShiftId,
													$UserId ) 
					{
				
                   // Delete Reggistrant's Existing "Non-Matched" Availability Data 
					//========================== =====================					
					
				/*		
					$query ="DELETE FROM  RegistrantVerifiedAvailability
								WHERE RegistrantId = '{$RegistrantId}'
								AND ServiceDate = '{$ServiceDate}'
								AND ShiftMatchedFL != 1 ";
								 
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
							
				*/			
							
					$query ="Insert into RegistrantVerifiedAvailability
						  (	RegistrantId,
						    ServiceDate, 
							PayrollWeek,
							WeekDay,
							ShiftId,
							UserId, 
							TransDate)   
						values ( '{$RegistrantId}',  
								 '{$ServiceDate}', 
								 '{$PayrollWeek}',
								 '{$WeekDay}',
								 '{$ShiftId}',
								 '{$UserId}',  
								  NOW()  ) ";
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				return $result;  
				//return $query;			
			}				
			
			// Get Client's Restricted Registrants Info
            //=======================			
			function getClientRestRegistrants($ClientId) {
			
                        $query ="SELECT a.RegistrantId as id,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RestrictType,
										CASE RestrictType
											WHEN '1' THEN 'Clinical'
											WHEN '0' THEN 'Non-Clinical'
										END AS RestrictTypeDesc,
										RestrictReason,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										
							FROM 	ClientRestrictedRegistrants a,
									Registrants b, 
									Users c,
									RegistrantTypes f  
					where 	a.RegistrantId = b.Id 
						AND b.TypeId = f.Id
						AND a.UserId = c.UserId
						AND RestrictStatus = 1
						AND a.ClientId = '{$ClientId}' 
						 Order By b.LastName, b.FirstName  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			// Get Client's nonRestricted Registrants Info
            //=======================			
			function getClientNonRestRegistrants($ClientId) {
			
                        $query ="SELECT a.RegistrantId as id,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
							FROM ClientApprovedRegistrants a,
								Registrants b, RegistrantTypes f  
					where 	a.RegistrantId = b.Id 
						and b.TypeId = f.Id
						and a.Status = 1
						and a.ClientId = '{$ClientId}' 
						and not exists (Select 1 from ClientRestrictedRegistrants d
                                            Where a.RegistrantId = d.RegistrantId
                                       		AND RestrictStatus = 1	)   
						 Order By b.LastName, b.FirstName   "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			//  Remove  Client's Restriction     
            //=============================================			
			function setRemoveClientRestRegistrant(	$RegistrantId,
													$ClientId,	  
													$UserId) {
 												
						$query ="Update ClientRestrictedRegistrants
						   Set RestrictStatus = 2,
							   UserId = '{$UserId}',
							   TransDate = now()	
						Where 	RegistrantId = '{$RegistrantId}'
						AND		ClientId = 	'{$ClientId}'
						AND		RestrictStatus = 1  ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
						}	
  						
				return $result;
			}	

			//  Update  Client's Restriction Reason    
            //=============================================			
			function setClientRestRegistrantReason(	$RegistrantId,
													$ClientId,	  
													$RestrictType,
													$RestrictReason,			
													$UserId) {
 												
						$query ="Insert into ClientRestrictedRegistrants
						   (RegistrantId,  	ClientId,  RestrictType,  RestrictReason, UserId, TransDate)   
						values ( '{$RegistrantId}',  '{$ClientId}',  '{$RestrictType}', '{$RestrictReason}',   '{$UserId}',  now()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
						}	
  						
				return $result;
			}				
			
          //===============================================
			// Set Adjust Client Schedule 
            //===============================================			
			function setAdjustClientSchedule(
								$ScheduleId,
								$StartTime,
								$EndTime,
								$LunchHour,
								$TotalHours,
								$ClientUnitId,
								$ServiceTypeId,
								$UserId) 
			{ 
                        $query = "update WeeklyServices 
						            set StartTime = '{$StartTime}', 
                                        EndTime = '{$EndTime}', 
                                        LunchHour = '{$LunchHour}',
										TotalHours = '{$TotalHours}', 
										ClientUnitId = '{$ClientUnitId}',
										ServiceTypeId = '{$ServiceTypeId}',
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									Where 	Id = '{$ScheduleId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

			/* Get TiemCard Site Navigation Data
            //=======================	*/		
			function getTimeCardSideNavigation() {
			
                        $query = "SELECT *							
						FROM TimeCardNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			// Get Registrant Tiem Card Verificaiton Data
            //=======================			
			function getRegistrantTimeCardVerification($RegistrantId, $PayrollWeek) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id AS ScheduleId,
										ScheduleStatusId, 
										ScheduleStatusDesc,
										a.ServiceTypeId,
										ServiceTypeDesc,
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                                        SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,  
										SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
										PayrollWeek,   
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										a.ClientId,
										ClientName,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											Clients d,
											ClientUnits c,
											ServiceTypes h,
											ScheduleStatuses g								
										Where 	a.RegistrantId= '{$RegistrantId}' 
												AND PayrollWeek = '{$PayrollWeek}'
												AND ScheduleStatusId = 7
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.UserId = e.UserId
												AND a.ClientId = d.Id
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
												AND a.ServiceTypeId = h.Id
											ORDER BY ServiceDateSort "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			// Get Time Card Transactions (by Client) Data
            //=======================			
			function getClientTimeCardTransactions($ClientId, $PayrollWeek) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id AS ScheduleId,
										ScheduleStatusId, 
										ScheduleStatusDesc,
										a.ServiceTypeId,
										ServiceTypeDesc,
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                                        SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,  
										SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
										PayrollWeek,   
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										a.ClientId,
										ClientName,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											Clients d,
											ClientUnits c,
											ServiceTypes h,
											ScheduleStatuses g								
										Where 	a.ClientId= '{$ClientId}' 
												AND PayrollWeek = '{$PayrollWeek}'
												AND ScheduleStatusId = 7
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.UserId = e.UserId
												AND a.ClientId = d.Id
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
												AND a.ServiceTypeId = h.Id
											ORDER BY ServiceDateSort "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}				

			// Set Verify Time Card 
            //=======================			
			function setRegistrantTimeCardVerification(
								$InclSchedules,
								$ScheduleStatusId,
								$UserId) 
			{ 
                        $query = "UPDATE WeeklyServices 
						            SET ScheduleStatusId = '{$ScheduleStatusId}', 
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									WHERE 	FIND_IN_SET(Id, '$InclSchedules')  ";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

            //=====================================		
			// Get TimeCards Upload Data Options
            //=====================================		
			function getRegistrantTimeCardsUploadDataOptions() {
			
                        $query = "SELECT Id as id,
										 UploadDesc,
										 UploadLink			
						FROM RegistrantsTimeCardsDataUpload 
					order by Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get Client's Shifts Info
            //=======================			
			function getClientShifts($ClientId) {
			
                        $query ="SELECT a.ShiftId AS id, 
										a.ShiftId,     
										a.ClientId,
										ShiftName, 
										a.Status,
										CASE Status 
											WHEN '1' THEN 'Active'
											ELSE 'Inactive'
										END AS StatusDesc,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime, 
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										LunchHour,
										TotalHours,
										DefaultFL,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM ClientShifts a, Users b 
										WHERE a.UserId = b.UserId
										AND a.ClientId = '{$ClientId}' 
									Order By ShiftId "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			//============================================	
			// Set Client Shift Info
            //============================================		
			function setClientShifts ( $form_data )
		
			{

				$ClientId = $form_data->{'ClientId'};
				$ShiftId = $form_data->{'ShiftId'};
				$UserId = $form_data->{'UserId'};
				$Status = $form_data->{'Status'};
				$ShiftName = $form_data->{'ShiftName'};
				$StartTime = $form_data->{'StartTime'};
				$EndTime = $form_data->{'EndTime'};
				$LunchHour = $form_data->{'LunchHour'};
				$TotalHours = $form_data->{'TotalHours'};
				$DefaultFL = $form_data->{'DefaultFL'};

				if ($DefaultFL == '1') {

						$query1 ="UPDATE ClientShifts 
							SET DefaultFL = '0'
							WHERE ClientId = '{$ClientId}'
							AND DefaultFL = '1'	 "; 
										 
                                                      
                        $result1 = $this->connection->getAll($query1, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }					
				
		               $query ="UPDATE ClientShifts 
									SET Status= '{$Status}',
										ShiftName= '{$ShiftName}',
										StartTime= '{$StartTime}',
										EndTime= '{$EndTime}',
										LunchHour= '{$LunchHour}',
										TotalHours= '{$TotalHours}',
										DefaultFL= '1',
										UserId= '{$UserId}',
										TransDate= NOW()
									WHERE ClientId = '{$ClientId}'
									AND ShiftId = '{$ShiftId}'	 "; 
												 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				} else {

	               $query ="UPDATE ClientShifts 
								SET Status= '{$Status}',
									ShiftName= '{$ShiftName}',
									StartTime= '{$StartTime}',
									EndTime= '{$EndTime}',
									LunchHour= '{$LunchHour}',
									TotalHours= '{$TotalHours}',
									UserId= '{$UserId}',
									TransDate= NOW()
								WHERE ClientId = '{$ClientId}'
								AND ShiftId = '{$ShiftId}'	 "; 
											 
	                                                      
	                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

	                        if (DB::isError($result)){
	                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
	                        }

				}


 			
						return $result;
			}				

           //==============================================		
			// Get Conditional Credentialing Items Header  
            //=============================================		
			function getRegistrantCredItemsConditionalHeader($RegistrantId) {
			
                        $query = "SELECT a.Id as id,
										 a.Id as ConditionalItemId, 	
										 a.ConditionalItemDesc,
										 a.TriggerValueDesc,
										 b.ConditionalSwitch				
						FROM CredentialingItemsConditionalHeader a,
						     RegistrantCredentialingItemsConditional b
						WHERE RegistrantId = '{$RegistrantId}'
						AND   a.Id = b.ConditionalItemId		
							ORDER BY ConditionalItemDesc ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

           //==============================================		
			// Get Conditional Credentialing Items Details  
            //=============================================		
			function getRegistrantCredItemsConditionalDetails($ConditionalItemId, $ConditionalSwitch) {
			
                        $query = "SELECT a.CredItemId , CredItemDesc
									FROM CredentialingItemsConditionalDetails a, CredentialingItems b
								  WHERE ConditionalItemId = '{$ConditionalItemId}'
									AND ConditionalSwitch = '{$ConditionalSwitch}'	
									AND a.CredItemId = b.Id ";
																					  
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
	
			//==============================================		
			// Set Registrant's Credential Item Status (On/Off) for Optional items ONLY  
            //=============================================		
			function setRegistrantCredItemsStatus($CredItemTransId, $StatusId, $UserId) {
			
                        $query = "UPDATE RegistrantCredItems
									SET StatusId =  '{$StatusId}',
									    UserId =  '{$UserId}',
										TransDate = NOW()
							WHERE Id = 	'{$CredItemTransId}'	";
																					  
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}			


			//=====================================================		
			// Set Registrant's New Conditional Credential Items   
            //====================================================		
			function setRegistrantNewConditionalCredItems(	$RegistrantId, 
															$ConditionalItemId, 
															$ConditionalSwitch, 
															$UserId) {
						$query = "Call  proc_setRegistrantNewConditionalCredItems ('{$RegistrantId}', '{$ConditionalItemId}', '{$ConditionalSwitch}', '{$UserId}')  "; 
 						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);	


                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
						
						//return $result;
						return $query;
			}			

			//===============================================
			// Set Adjust Client Schedule 
            //===============================================			
			function setAdjustTimeCard(
								$ScheduleId,
								$StartTime,
								$EndTime,
								$LunchHour,
								$TotalHours,
								$ClientUnitId,
								$ServiceTypeId,
								$UserId) 
			{ 
                        $query = "update WeeklyServices 
						            set StartTime = '{$StartTime}', 
                                        EndTime = '{$EndTime}', 
										LunchHour = '{$LunchHour}',
                                        TotalHours = '{$TotalHours}', 
										ClientUnitId = '{$ClientUnitId}',
										ServiceTypeId = '{$ServiceTypeId}',
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									Where 	Id = '{$ScheduleId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}			

			//==============================			
			// Get Clients Daily Schedules
			//=============================			
			function getClientsDailySchedules( $ServiceDate) {
			
 
							
                        $query = "Call proc_ClientsDailySchedules ('{$ServiceDate}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			//==============================			
			// Get Clients Date Range Schedules
			//=============================			
			function getClientsDateRangeSchedules($FromDate, $ToDate) {
			
 
							
                        $query = "Call proc_ClientsDateRangeSchedules ('{$FromDate}', '{$ToDate}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			//============================================
			// Get Confirmed Schedules By Registrant Info
            //============================================			
			function getConfirmedSchedulesByRegistrant(	$RegistrantId,
														$StartDate, 
														$EndDate) 	{
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										ScheduleStatusId, 
										ScheduleStatusDesc,
										TextColor,
										BackgroundColor,
										a.ClientId, 
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										RegistrantTypeDesc, 
										RegistrantTypeId,
										ClientName,
										ClientUnitId,
										UnitName,
									

										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											Clients d,
											ScheduleStatuses g								
										Where 	a.RegistrantId= '{$RegistrantId}' 
												AND ScheduleStatusId > 6
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.ClientId = d.Id
												AND  ServiceDate between '{$StartDate}' and '{$EndDate}' 
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
											ORDER BY ServiceDate, StartTime 
											";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}			

			// Set Registrant Cred Item Email Messages
            //=======================			
			function setEmailCredItemsMsg($RegistrantId, 
												$UserId) 	{ 
				 
                       $query ="Insert into RegistrantCredItemMessages ( RegistrantCredItemTransId,
																		Msg,
																		HighPriority,
																		UserId,		
																		TransDate )
		
													Select  Id,
															'Cred. Item related Email was sent',
															'0', 
															'{$UserId}',
															NOW() 
													From RegistrantCredItems
														Where RegistrantId = '{$RegistrantId}' 
														AND ComplianceLevelId = 1  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}		

			//=====================================================
			// Get Registrant's Service Cancellations History Info
            //=====================================================			
			function getRegistrantServiceCancellations($RegistrantId) {
			
                        $query ="SELECT CONCAT( trim( d.LastName ) , ', ', trim( d.FirstName ) , ' (', RegistrantTypeDesc, ')' ) AS RegistrantName,
                                        ClientName,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										CancelReason,
										COALESCE((SELECT  ServiceCancellationReasonDesc
												FROM ServiceCancellationReasons e
												WHERE  e.id = ServiceCancellationReasonId 
											),'Undefined') as ServiceCancellationReasonDesc,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	RegistrantServiceCancellations a,
                                            Registrants d, 
											RegistrantTypes c,
                                            WeeklyServices f,  
                                            Clients g, 
                                            
											Users b			
									WHERE 	a.RegistrantId = d.Id
											AND Typeid = c.Id
											AND a.UserId = b.UserId
                                            AND a.ScheduleId = f.Id
                                            AND f.ClientId = g.Id  
                                             
                                            AND a.RegistrantId = '{$RegistrantId}' 
                                        Order By a.TransDate Desc 	"; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}						

			//==============================			
			// Get Registrant's Monthly Calendar
			//=============================			
			function getRegistrantMonthlyCalendar($RegistrantId, $StartDate) {
			
 
							
                        $query = "Call proc_RegistrantMontlyCalendar ('{$RegistrantId}', '{$StartDate}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Billing Side Navigation Data
            //=======================	*/		
			function getBillingSideNavigation() {
			
                        $query = "SELECT *							
							FROM BillingSideNavigation 
						ORDER BY Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get Service Contracts Data
             ============================	*/		
			function getServiceContracts() {
			
                        $query ="SELECT Id as id,
                                        Id as ContractId,
										StatusId,
										ContractName,
										COALESCE((Select group_concat( ClientName  
                                         SEPARATOR ', ' ) 
											FROM Clients c
											WHERE a.Id = c.ContractId ),'') as  ParticipClients,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContracts a,
									Users b
							WHERE a.UserId = b.UserId		
							ORDER BY Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Service Contracts (Active) Data
             ============================	*/		
			function getServiceContractsActive() {
			
                        $query ="SELECT Id as id,
                                        Id as ContractId,
										ContractName
							FROM 	ServiceContracts
							WHERE StatusId = '1'	
							ORDER BY Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}				
			
			/* Get Admin Side Navigation Data
            //=======================	*/		
			function getAdminSideNavigation() {
			
                        $query = "SELECT *							
							FROM AdminSideNavigation 
						ORDER BY Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			//  Update Service Contract Info 
            //=======================			
			function setServiceContract(   
										$ContractId,
										$StatusId,
										$ContractName,
										$UserId ) 
			{
					
					if(is_numeric($ContractId))  { 	

                         $query ="update ServiceContracts 
						            set StatusId = '{$StatusId}', 
                                        ContractName = '{$ContractName}', 	
                                        UserId = '{$UserId}',
                                        TransDate = NOW()										
								Where 	Id = '{$ContractId}' 	";
					} else {
							$query ="insert into ServiceContracts
							                  (
												StatusId,
												ContractName,	 
												UserId,
												TransDate )	
												
						       values ( '{$StatusId}',
										'{$ContractName}',	 
 										'{$UserId}',
                                         NOW()	)";
			
					}		

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}					

			/* Get Service Contract Category Data
             ===============================	*/		
			function getServiceContractCategories($ContractId) {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as ContractCategoryId,
										ContractId,
										a.StatusId,
										ContractName,
										ContractCategoryName,
										COALESCE((Select group_concat( UnitName  
                                         SEPARATOR ', ' ) 
											FROM ClientUnits c
											WHERE a.Id = c.ContractCategoryId ),'') as  ParticipUnits,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractCategories a,
									ServiceContracts c,
									Users b
							WHERE a.UserId = b.UserId	
							AND a.ContractId = c.Id
							AND a.ContractId =  '{$ContractId}'	
							ORDER BY Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			//  Update Service Contract Info 
            //=======================			
			function setServiceContractCategory(   
										$ContractCategoryId,
										$ContractId,
										$StatusId,
										$ContractCategoryName,
										$UserId ) 
			{
					
					if(is_numeric($ContractCategoryId))  { 	

                         $query ="update ServiceContractCategories 
						            set StatusId = '{$StatusId}', 
                                        ContractCategoryName = '{$ContractCategoryName}', 	
                                        UserId = '{$UserId}',
                                        TransDate = NOW()										
								Where 	Id = '{$ContractCategoryId}' 	";
					} else {
							$query ="insert into ServiceContractCategories
							                  (
												ContractId,
												StatusId,
												ContractCategoryName,	 
												UserId,
												TransDate )	
												
						       values ( '{$ContractId}',
										'{$StatusId}',
										'{$ContractCategoryName}',	 
 										'{$UserId}',
                                         NOW()	)";
			
					}		

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}	

			//==================================			
			// Get Service Contract Category Rates
			//==================================			
			function getServiceContractCategoryRates($ContractCategoryId,  $ServiceTypeId, $RateDateRangeId) {
			
 
							
                        $query = "Call proc_getServiceContractCategoryRates ( '{$ContractCategoryId}', '{$ServiceTypeId}', '{$RateDateRangeId}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			
			/*  Update Service Contract Category Rates 
            ========================================*/			
			function setServiceContractCategoryRates(   $ContractCategoryId,
													$ServiceTypeId,
													$RateDateRangeId,
													$ShiftId,
													$RateTypeId,
													$BillRate,
													$PayRate,
													$UserId ) 
			{
					

							$query ="INSERT INTO ServiceContractCategoryRates
							                  (
												ContractCategoryId,
												ServiceTypeId,
												RateDateRangeId,
												ShiftId,	
												RateTypeId,
												BillRate,
												PayRate,
												UserId,
												TransDate )	
												
						       VALUES ( '{$ContractCategoryId}',
										'{$ServiceTypeId}',
										'{$RateDateRangeId}',
										'{$ShiftId}',	 
										'{$RateTypeId}',	 
										'{$BillRate}',	 
										'{$PayRate}',	 
										'{$UserId}',
                                         NOW()	)
								ON DUPLICATE KEY UPDATE PayRate = VALUES(PayRate), BillRate= VALUES(BillRate) ";		 
										  
			

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}	
			
			/* Get Service Contract Category Override Rates
             ===========================================	*/		
			function getServiceContractCategoryOvrRates($ContractCategoryId, $ServiceTypeId, $RateDateRangeId) {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as ContractCategoryRatesOvrId,
										ContractCategoryName,
										ContractCategoryId,
										ServiceTypeDesc,
										ServiceTypeId,
										RateDateRangeId,
										WeekDayId,
										CASE WeekDayId 
											WHEN '1' THEN 'Sun'
											WHEN '2' THEN 'Mon'
											WHEN '3' THEN 'Tue'
											WHEN '4' THEN 'Wed'
											WHEN '5' THEN 'Thu'
											WHEN '6' THEN 'Fri'
											WHEN '7' THEN 'Sat'
										END AS WeekDayName,
										ShiftId,
										CASE ShiftId 
											WHEN '1' THEN 'Day'
											WHEN '2' THEN 'Evening'
											WHEN '3' THEN 'Night'
											WHEN '4' THEN '12 Hrs Day'
											WHEN '5' THEN '12 Hrs Night'
										END AS ShiftName,
										BillPayFL,
										CASE BillPayFL 
											WHEN '1' THEN 'Pay'
											WHEN '2' THEN 'Bill'
										END AS BillPayFLName,
									
										OverrideRate,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractCategoryRatesOvr a,
									Users b,
									ServiceContractCategories c,
									ServiceTypes f
							WHERE a.UserId = b.UserId	
							AND a.ContractCategoryId =  '{$ContractCategoryId}'	
							AND a.ServiceTypeId =  '{$ServiceTypeId}'	
							AND a.RateDateRangeId =  '{$RateDateRangeId}'
							AND a.ServiceTypeId = f.Id
							AND a.ContractCategoryId = c.Id
							ORDER BY WeekDayId  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/*  Update Service Contract Category Rates Override
            ================================================= */			
			function setServiceContractCategoryOvrRates(  	$ContractCategoryRatesOvrId,
														$ContractCategoryId,
														$ServiceTypeId,
														$RateDateRangeId,
														$WeekDayId,
														$ShiftId,
														$BillPayFL,
														$OverrideRate,
														$UserId 
													) 
			{
					

					if(is_numeric($ContractCategoryRatesOvrId))  { 	

                         $query ="update ServiceContractCategoryRatesOvr 
						            set WeekDayId = '{$WeekDayId}', 
                                        ShiftId = '{$ShiftId}',
										BillPayFL = '{$BillPayFL}',
										OverrideRate = '{$OverrideRate}',
                                        UserId = '{$UserId}',
                                        TransDate = NOW()										
								Where 	Id = '{$ContractCategoryRatesOvrId}' 	";
					} else {
							$query ="INSERT INTO ServiceContractCategoryRatesOvr
							                  (
												ContractCategoryId,
												ServiceTypeId,
												RateDateRangeId,
												WeekDayId,	
												ShiftId,
												BillPayFL,
												OverrideRate,
												UserId,
												TransDate )	
												
						       VALUES ( '{$ContractCategoryId}',
										'{$ServiceTypeId}',
										'{$RateDateRangeId}',
										'{$WeekDayId}',	 
										'{$ShiftId}',	 
										'{$BillPayFL}',	 
										'{$OverrideRate}',	 
										'{$UserId}',
                                         NOW()	)";
			
					}											  
			

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}			
			
			/*  Update Service Contract Category Rates Override
            ================================================= */			
			function setDeleteServiceContractCategoryOvrRate($ContractCategoryRatesOvrId)			
                        
			{			$query = "DELETE FROM ServiceContractCategoryRatesOvr			
							Where 	Id = '{$ContractCategoryRatesOvrId}' 	";
                                                      
                        $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Clients Un-Assigned to Contracts Data
            //=======================	*/		
			function getServiceContractUnassignedClients() {
			
                        $query = "	SELECT	Id as id,
											ClientName
									FROM Clients
									WHERE ContractId = 0
										ORDER BY ClientName ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Clients Assigned to Contracts Data
            //========================================	*/		
			function getServiceContractAssignedClients($ContractId) {
			
                        $query = "	SELECT	Id as id,
											ClientName
									FROM Clients
									WHERE ContractId = '{$ContractId}'
										ORDER BY ClientName ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/*  Update Service Contract Un-Assigned Clients     
             ===================================================*/			
			function setServiceContractUnassignedClients(	$ContractId,	
															$ClientsArray,	
															$UserId) {
					
 
	
					foreach ($ClientsArray as $ClientData) {
								
							$ClientId = $ClientData['id'];
							
												
						$query ="UPDATE Clients
									SET ContractId = 0,
									    UserId = '{$UserId}',
										TransDate = NOW()
									
								WHERE Id =  '{$ClientId}'		
								AND ContractId != 0";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
						
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/*  Update Service Contract Assigned Clients     
             ===================================================*/			
			function setServiceContractAssignedClients(	$ContractId,	
														$ClientsArray,	
														$UserId) {
					
 
	
					foreach ($ClientsArray as $ClientData) {
								
							$ClientId = $ClientData['id'];
							
							
												
						$query ="UPDATE Clients
									SET ContractId = '{$ContractId}',
									    UserId = '{$UserId}',
										TransDate = NOW()
								WHERE Id =  '{$ClientId}' 
								AND ContractId != '{$ContractId}' ";		
		
						$result = $this->connection->query($query);
                        
						if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
						
					} // end of 'foreach' loop
						
				return $result;
				//return $query;			
			}	

			/* Get Client Units Un-Assigned to Contract Categories Data
            //========================================================	*/		
			function getServiceContractCategoryUnassignedClientUnits($ClientId) {
			
                        $query = "	SELECT	Id as id,
											UnitName
										FROM ClientUnits 
									WHERE 	ClientId = '{$ClientId}' 
										AND	ContractCategoryId = 0
									ORDER BY UnitName ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Client Units Assigned to Contract Categories Data
           //========================================================	*/		
			function getServiceContractCategoryAssignedClientUnits($ClientId, $ContractCategoryId) {
			
                        $query = "	SELECT	Id as id,
											UnitName
									FROM ClientUnits
									WHERE ClientId = '{$ClientId}' 
									AND ContractCategoryId = '{$ContractCategoryId}'
										ORDER BY UnitName ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}				

			/* Get Service Contract Holidays Data
             ===============================	*/		
			function getServiceContractHolidays($ContractId, $CalendayYear) {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as HolidayId,
										ContractId,
										ContractName,
										HolidayDate as HolidayDateSort,
										DATE_FORMAT(HolidayDate, '%m-%d-%Y' ) as HolidayDate,
										HolidayDesc,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractHolidays a, Users b, ServiceContracts c
							WHERE ContractId =  '{$ContractId}'
							AND CalendarYear = '{$CalendayYear}'	
							AND a.UserId = b.UserId		
							AND a.ContractId = c.Id		
							ORDER BY HolidayDateSort  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			
			// Get Clients With Contract Listing
            //=======================			
			function getClientsWithContract() {
                        
						$query = "SELECT 	a.Id as id, 
											a.Id as ClientId, 
											ClientName,
											b.id as ContractId,
											ContractName
						FROM Clients a, ServiceContracts b
						WHERE a.ContractId = b.Id
							ORDER BY ClientName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/*  Update Service Contract Un-Assigned Units     
             ===================================================*/			
			function setServiceContractUnassignedUnits(	$ContractCategoryId,	
														$UnitsArray,	
														$UserId) {
					
 
	
					foreach ($UnitsArray as $UnitData) {
								
							$ClientUnitId = $UnitData['id'];
							
												
						$query ="UPDATE ClientUnits
									SET  	ContractCategoryId = 0,
									    UserId = '{$UserId}',
										TransDate = NOW()
									
								WHERE Id =  '{$ClientUnitId}'		
								AND ContractCategoryId != 0";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
						
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/*  Update Service Contract Assigned Units     
             ===================================================*/			
			function setServiceContractAssignedUnits(	$ContractCategoryId,	
														$UnitsArray,	
														$UserId) {
					
 
	
					foreach ($UnitsArray as $UnitData) {
								
							$ClientUnitId = $UnitData['id'];
							
							
												
						$query ="UPDATE ClientUnits
									SET ContractCategoryId = '{$ContractCategoryId}',
									    UserId = '{$UserId}',
										TransDate = NOW()
								WHERE Id =  '{$ClientUnitId}' ";
									
		
						$result = $this->connection->query($query);
                        
						if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
						
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/* Get All Service Contracts with Categories Data
             ===============================	*/		
			function getServiceContractsWithCategories() {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as ContractCategoryId,
										ContractId,
										a.StatusId,
										ContractName,
										ContractCategoryName,
										COALESCE((Select group_concat( UnitName  
                                         SEPARATOR ', ' ) 
											FROM ClientUnits c
											WHERE a.Id = c.ContractCategoryId ),'') as  ParticipUnits,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractCategories a,
									ServiceContracts c,
									Users b
							WHERE c.StatusId = '1' 
							AND a.UserId = b.UserId	
							AND a.ContractId = c.Id
							ORDER BY ContractName,  ContractCategoryName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Rate Date Ranges Data
             ===============================	*/		
			function getRatesDateRanges() {
			
                        $query ="SELECT a.Id AS id, 
										a.Id AS RateDateRangeId, 
										DATE_FORMAT( StartDate, '%m-%d-%Y') AS StartDateDesc,
										StartDate,
										EndDate,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										
							FROM	ServiceContractCategoryRateDateRanges a, Users b  
							WHERE a.UserId = b.UserId	
							ORDER BY StartDate ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			/* Get Client Invoices Header Data
             ===================================	*/		
			function getClientInvoicesHeader($ClientId) {
			
                        $query ="SELECT Id as id,
										Id as InvoiceNumber,
										ClientId, 
										SearchId, 
										StatusId, 
										InvoiceDate, 
										DATE_FORMAT(InvoiceDate, '%m-%d-%Y' ) as InvoiceDate,
										PayrollWeek, 
										TotalUnits, 
										TotalAmount, 
										PaidAmount,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										FROM InvoiceHeader a,Users b  
									WHERE ClientId = '{$ClientId}'
									AND a.UserId = b.UserId";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			/* Get Client Invoice Details Data
             ===================================	*/		
			function getClientInvoiceDetails($InvoiceNumber) {
			
                        $query ="SELECT a.InvoiceNumber,
										LineNumber,
										SequenceNumber,
										CurrentLineFL,
										a.ScheduleId,
										DATE_FORMAT( c.ServiceDate, '%m/%d/%Y' ) as ServiceDateString,
										CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as RegistrantName,
										c.ClientUnitId,
										UnitName,
										c.ServiceTypeId,
										ServiceTypeDesc,
										ShiftName,
										ShiftType,
										a.Units,
										a.BillRate,
										a.PayRate,
										ROUND((a.BillRate * a.Units),2)  as BillAmount,
										ROUND((a.PayRate * a.Units),2)  as PayAmount,
										CONCAT( trim( g.FirstName ) , ' ', trim( g.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	InvoiceDetails a, 
										Registrants b, 
										WeeklyServices c, 
										ClientUnits d, 
										ServiceTypes f,
										Shifts e,
										Users g
								WHERE a.InvoiceNumber = '{$InvoiceNumber}'
								AND a.ScheduleId = c.Id	
								AND c.RegistrantId = b.Id
								AND c.ClientUnitId = d.Id
								AND c.ServiceTypeId = f.Id
								AND c.ShiftId = e.ShiftId
								AND a.UserId = g.UserId
								ORDER BY LineNumber, SequenceNumber DESC, c.ShiftId";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						

			}			

			/* Set Client Invoice Detail Adjusted Data
             =========================================	*/		
			function setAdjustClientInvoiceLineItem(	$InvoiceNumber,
														$LineNumber,
														$Units,
														$UnitsOrig,
														$BillRate,
														$BillAmount,
														$BillAmountOrig,
														$ScheduleId,
														$ClientUnitId,
														$ServiceTypeId,
														$UserId 
													) 
			{
			
                        $query ="   UPDATE InvoiceDetails a, WeeklyServices b, InvoiceHeader c
										SET a.Units = '{$Units}',
											a.BillRate = '{$BillRate}',
											a.Amount = '{$BillAmount}',
											a.UserId = '{$UserId}',
											a.TransDate = now(),
											b.ClientUnitId = '{$ClientUnitId}',
											b.ServiceTypeId = '{$ServiceTypeId}',
											b.UserId = '{$UserId}',
											b.TransDate = now(),
											c.TotalUnits =  c.TotalUnits - '{$UnitsOrig}' + '{$Units}',
											c.TotalAmount =  ROUND((c.TotalAmount - '{$BillAmountOrig}' + '{$BillAmount}'),2),
											c.UserId = '{$UserId}',
											c.TransDate = now()

											
								WHERE 	a.InvoiceNumber = '{$InvoiceNumber}'
								AND     a.LineNumber = '{$LineNumber}' 
								AND     b.Id = '{$ScheduleId}' 
								AND     c.Id = '{$InvoiceNumber}' "; 

								
								
								 
                                                      
                       $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $query;
						
			}			

			/* Get Client Invoice Payments Data
             ===================================	*/		
			function getClientInvoicePayments($InvoiceNumber) {
			
                        $query ="SELECT Id as PaymentId,
										PaymentDate,
										DATE_FORMAT(PaymentDate, '%m-%d-%Y' ) as PaymentDateString,
										ReferenceNumber,
										PaymentAmount,
										PaymentAmount as PaymentAmountOrig,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	InvoicePayments a, 
										Users b
								WHERE InvoiceNumber = '{$InvoiceNumber}'
								AND a.UserId = b.UserId
								ORDER BY Id DESC";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			/* Set Client Invoice Payments Data
             ===================================	*/		
			function setClientInvoicePayment(	$PaymentId,
												$InvoiceNumber,
												$PaymentAmount,
												$ReferenceNumber,
												$UserId
												) 
			{
			
					if(is_numeric($PaymentId))  { 	

                         $query ="UPDATE InvoicePayments  
						            set PaymentAmount = '{$PaymentAmount}', 
                                        ReferenceNumber = '{$ReferenceNumber}',
                                        UserId = '{$UserId}',
                                        TransDate = NOW()										
								Where 	Id = '{$PaymentId}' 	";
					} else {
							$query ="INSERT INTO InvoicePayments
							                  (
												InvoiceNumber,
												PaymentDate,
												PaymentAmount,
												ReferenceNumber,
												UserId,
												TransDate )	
												
						       VALUES ( '{$InvoiceNumber}',
										  CURDATE(),
										'{$PaymentAmount}',
										'{$ReferenceNumber}',	 
										'{$UserId}',
                                         NOW()	)";
			
					}	                                                      
                       $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		
			 
			// Set Client Invoice Header Payments 
            //===================================			
			function setClientInvoicePaidAmount( 	$InvoiceNumber,
													$PaymentAmount,
													$PaymentAmountOrig,
													$UserId) 
			{ 
                        $query = "UPDATE InvoiceHeader 
						            SET PaidAmount 	 =  PaidAmount - '{$PaymentAmountOrig}' + '{$PaymentAmount}', 
                                        UserId = '{$UserId}',
                                        TransDate = NOW()										
									Where 	Id = '{$InvoiceNumber}' ";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			 
			/* Get Client Invoice Adjustments Data
             ===================================	*/		
			function getClientInvoiceAdjustments($InvoiceNumber) {
			
                        $query ="SELECT a.Id as id, 
										a.id as AdjustmentId,
										a.InvoiceNumber,
										AdjAmount,
										AdjReason,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	InvoiceAdjustments a, 
										Users b
								WHERE a.InvoiceNumber = '{$InvoiceNumber}'
								AND a.UserId = b.UserId
								ORDER BY a.Id";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			/* Set Client Invoice Adjustment Data
             ===================================	*/		
			function setClientInvoiceAdjustment(	$AdjustmentId,
													$InvoiceNumber,
													$AdjAmount,
													$AdjReason,
													$UserId
												) 
			{
			
					if(is_numeric($AdjustmentId))  { 	

                         $query ="UPDATE InvoiceAdjustments  
						            set AdjAmount = '{$AdjAmount}', 
                                        AdjReason = '{$AdjReason}',
                                        UserId = '{$UserId}',
                                        TransDate = NOW()										
								Where 	Id = '{$AdjustmentId}' 	";
					} else {
							$query ="INSERT INTO InvoiceAdjustments
							                  (
												InvoiceNumber,
												AdjAmount,
												AdjReason,
												UserId,
												TransDate )	
												
						       VALUES ( '{$InvoiceNumber}',
										'{$AdjAmount}',
										'{$AdjReason}',	 
										'{$UserId}',
                                         NOW()	)";
			
					}	                                                      
                       $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			// Set Client Invoice Header Adjusted Total Amount 
            //====================================================			
			function deleteClientInvoiceAdjustment( $AdjustmentId )
			{ 
                        $query = "DELETE FROM  InvoiceAdjustments 
									Where 	Id = '{$AdjustmentId}' ";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

			// Set Client Invoice Header Adjusted Total Amount 
            //====================================================			
			function setClientInvoiceTotalAmount( 	$InvoiceNumber,
													$AdjAmount,
													$AdjAmountOrig,
													$UserId) 
			{ 
                        $query = "UPDATE InvoiceHeader 
                                   SET TotalAmount  =  TotalAmount - '{$AdjAmountOrig}' + '{$AdjAmount}',    
										UserId = '{$UserId}',
                                        TransDate = NOW()										
									Where 	Id = '{$InvoiceNumber}' ";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			// Set Generate New Client's Invoices
            //==========================================			
			function setGenerateNewInvoicesData(	$ClientId, 
													$PerStartDateMon,		
													$PerStartDateTue,
													$PerEndDateSun,
													$UserId)
	{
			
  	$query = "Call  proc_GenerateClientNewInvoicesData ('{$ClientId}' , '{$PerStartDateMon}' , '{$PerStartDateTue}' ,'{$PerEndDateSun}' , '{$UserId}' )  ";                            
																			 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $query;
			}					

			/* Check for Duplicate Holiday Date
            //=====================================*/					
			function getDuplicateHolidays($ContractId, $CalendayYear, $HolidayDate) {
                         

                        $query = "SELECT *
									FROM ServiceContractHolidays
										WHERE ContractId = '{$ContractId}'
										AND CalendarYear = '{$CalendayYear}'
										AND HolidayDate = '{$HolidayDate}' ";										
                        
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }

                        
						return $result;
						//return $query;
	                                 
            }  	
			// Set Service Contract Holiday Data  
            //====================================================			
			function data_setServiceContractHoliday($ContractId,
													$CalendarYear,
													$HolidayDate,
													$HolidayDesc,
													$UserId) 
			{ 
                        $query = "INSERT INTO ServiceContractHolidays
							                  (
												ContractId,
												CalendarYear,
												HolidayDate,
												HolidayDesc,
												UserId,
												TransDate )	
												
						       VALUES ( '{$ContractId}',
										'{$CalendarYear}',
										'{$HolidayDate}',
										'{$HolidayDesc}',	 
										'{$UserId}',
                                         NOW() )";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

			/* Get Payroll Side Navigation Data
            //=======================	*/		
			function getPayrollSideNavigation() {
			
                        $query = "SELECT *							
							FROM PayrollSideNavigation 
						ORDER BY Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			// Get Client Services History Charts Data 
            //=============================================			
		 	function getClientServHistChartData($ClientId) {
			
                        $query ="SELECT distinct PayrollWeek as PayrollWeekSort,
                                                 DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) as PayrollWeek, 

						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId = 0) as 'Pending Shifts',           

						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId in (5, 6)) as 'Un-Confirmed Shifts',           

						( Select count(*) from WeeklyServices c 
							where a.ClientId = c.ClientId   
							AND a.PayrollWeek = c.PayrollWeek
							AND c.ScheduleStatusId in (2,3,4)) as 'Cancelled Shifts',           

						( Select count(*) from WeeklyServices d 
							where a.ClientId = d.ClientId   
							AND a.PayrollWeek = d.PayrollWeek
							AND d.ScheduleStatusId > 6) as 'Confirmed Shifts',           

						( Select count(*) from WeeklyServices e 
							where a.ClientId = e.ClientId   
							AND a.PayrollWeek = e.PayrollWeek
							AND e.ScheduleStatusId = 1) as 'Un-Used Availability'           
							FROM WeeklyServices a
							WHERE ClientId = '{$ClientId}'
							AND PayrollWeek > (ADDDATE( CURDATE( ) , -120 ))
							ORDER BY PayrollWeekSort"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			// Get Client Services History Charts Data 
            //=============================================			
		 	function getAllServHistChartData() {
			
                        $query ="SELECT distinct PayrollWeek as PayrollWeekSort,
                                                 DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) as PayrollWeek, 
						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId = 0) as 'Pending Shifts',           
												 
						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId in (5, 6)) as 'Un-Confirmed Shifts',           

						( Select count(*) from WeeklyServices c 
							where a.ClientId = c.ClientId   
							AND a.PayrollWeek = c.PayrollWeek
							AND c.ScheduleStatusId in (2,3,4)) as 'Cancelled Shifts',           

						( Select count(*) from WeeklyServices d 
							where a.ClientId = d.ClientId   
							AND a.PayrollWeek = d.PayrollWeek
							AND d.ScheduleStatusId > 6) as 'Confirmed Shifts',           

						( Select count(*) from WeeklyServices e 
							where a.ClientId = e.ClientId   
							AND a.PayrollWeek = e.PayrollWeek
							AND e.ScheduleStatusId = 1) as 'Un-Used Availability'           
							FROM WeeklyServices a
							WHERE PayrollWeek > (ADDDATE( CURDATE( ) , -30 ))
							ORDER BY PayrollWeekSort "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			/* Get Client Work Orders Data
             ===================================	*/		
			function getClientWorkOrders($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.id as WorkOrderId,
										a.ClientId,
										a.StatusId,
										a.OrderTypeId,
										OrderTypeDesc,
										OrderDate as OrderDateSort,
										DATE_FORMAT(OrderDate, '%m-%d-%Y' ) as OrderDate,
										DATE_FORMAT(StartDate, '%m-%d-%Y' ) as StartDate,
										DATE_FORMAT(EndDate, '%m-%d-%Y' ) as EndDate,
										a.Comments,
										(SELECT COUNT(*) FROM WeeklyServices d
											WHERE d.WorkOrderId = a.Id
											AND ScheduleStatusId > 6) as ShiftsCount,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	ClientWorkOrders a, 
										ClientOrderTypes b,
										Users c
								WHERE a.ClientId = '{$ClientId}'
								AND a.UserId = c.UserId
								AND a.OrderTypeId = b.Id
								ORDER BY OrderDateSort Desc";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			//======================================================
			function setWorkOrder(	$WorkOrderId,
									$ClientId,
									$OrderTypeId,
									$StartDate,
									$EndDate,
									$Comments,	
									$UserId) {
					
					if(is_numeric($WorkOrderId))  { 	

                         $query ="update ClientWorkOrders
						            set OrderTypeId = '{$OrderTypeId}', 		
                                        StartDate = '{$StartDate}', 
										EndDate = '{$EndDate}',
                                        Comments = '{$Comments}', 		
                                        UserId = '{$UserId}',
                                        TransDate = now()										
								Where 	Id = '{$WorkOrderId}' 	";
					} else {
							$query ="insert into ClientWorkOrders
							                  (ClientId,
											  OrderDate,
											  OrderTypeId,
											  StartDate,
											  EndDate,
											  Comments,
											  UserId,
											  TransDate )
											  
						       values ( '{$ClientId}',
										CURDATE(),  	
                                        '{$OrderTypeId}', 		
                                        '{$StartDate}', 
										'{$EndDate}',
                                        '{$Comments}', 		
                                        '{$UserId}',
                                         now()	)";
					
					}		

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}				

			/* Get Client Active Work Orders Data
             ===================================	*/		
			function getClientActiveWorkOrders($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.id as WorkOrderId,
										a.OrderTypeId,
										OrderTypeDesc,
										OrderDate as OrderDateSort,
										DATE_FORMAT(OrderDate, '%m-%d-%Y' ) as OrderDate,
										DATE_FORMAT(StartDate, '%m-%d-%Y' ) as StartDate,
										DATE_FORMAT(EndDate, '%m-%d-%Y' ) as EndDate,
										a.Comments,
										(SELECT COUNT(*) FROM WeeklyServices d
											WHERE d.WorkOrderId = a.Id
											AND ScheduleStatusId > 6) as ShiftsCount,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	ClientWorkOrders a, 
										ClientOrderTypes b,
										Users c
								WHERE a.ClientId = '{$ClientId}'
								AND a.StatusID = '1'
								AND a.UserId = c.UserId
								AND a.OrderTypeId = b.Id
								ORDER BY OrderDateSort Desc";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			/* Get Client Pending Daily Shifts
             ===================================	*/		
			function getClientDailyPendingShifts ($ClientId, $ServiceDate, $ShiftId) {
			
                        $query ="SELECT a.Id AS ScheduleId, 
										ScheduleStatusId,
										ServiceTypeId,
										ServiceTypeDesc, 
										ClientUnitId, 
										COALESCE((SELECT UnitName FROM ClientUnits c
											WHERE a.ClientUnitId = c.Id), '') as UnitName,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										LunchHour,
										TotalHours,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
								FROM 	WeeklyServices a, 
										Users e,
										ServiceTypes b								
									Where 	a.ClientId= '{$ClientId}' 
									AND  ServiceDate =  '{$ServiceDate}'
									AND a.ShiftId =  '{$ShiftId}'
									AND ScheduleStatusId =  '0'
									AND a.UserId = e.UserId
									AND a.ServiceTypeId = b.Id";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Registrants for Pending Shifts
             ===================================	*/		
			function getRegistrantsForPendingShifts (	$ClientId, 
														$ClientUnitId,
														$ServiceTypeId,
														$ServiceDate, 
														$PayrollWeek, 
														$ShiftId) 
			{
				
				if ($ServiceTypeId == '1' ) {

                    				$query ="SELECT
									
									COALESCE((SELECT 'Yes' FROM RegistrantVerifiedAvailability d
										WHERE a.RegistrantId = d.RegistrantId 
										AND d.ServiceDate = '{$ServiceDate}'
										AND d.ShiftId = '{$ShiftId}'),'') as AvailVerfFL, 
									COALESCE((Select group_concat( CredItemDesc
										SEPARATOR ', ' ) 
										FROM RegistrantCredItems k,  CredentialingItems h
										WHERE a.RegistrantId = k.RegistrantId
										  /*AND  k.StatusId in (1,2)*/ 	
                                          AND  k.CredItemId = h.Id ),'')  as CredItems,	
									COALESCE((SELECT  CASE count(*) 
										WHEN 0 THEN ''
											ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
										END  
									FROM RegistrantAttchedSpecialties k, Specialties h
										where 	h.Id = k.SpecialtyId
												and  a.RegistrantId = k.RegistrantId),'') as SpecialitiesList,  										  
									COALESCE((SELECT i.Comments FROM RegistrantAvailabilityComments i
										WHERE a.RegistrantId = i.RegistrantId 
										AND i.ServiceDate = '{$ServiceDate}'
										AND i.ShiftId = '{$ShiftId}'),'') as Comments, 
									COALESCE((SELECT sum(TotalHours) 
										 from WeeklyServices h
										Where a.RegistrantId = h.RegistrantId	
										AND h.PayrollWeek = '{$PayrollWeek}' 
										AND h.ScheduleStatusId > 4),'') as HoursScheduled,										
									a.RegistrantId,
									f.RegistrantGroupId,
									COALESCE(Availability, '') as Availability,
									COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,
									CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
									DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrantsNonRest a, 
							Users b,
							Registrants c,
							RegistrantTypes f
						
					WHERE a.UserId = b.UserId
					AND a.ClientId = '{$ClientId}' 
					AND a.ClientUnitId = '{$ClientUnitId}'
					AND a.ServiceTypeId = '{$ServiceTypeId}'
					AND a.RegistrantId = c.Id
					AND c.TypeId = f.Id
					AND c.StatusId = '1'
					AND a.Status = '1' 
					AND NOT EXISTS (SELECT 1 FROM WeeklyServices g
									  WHERE a.RegistrantId = g.RegistrantId
									  AND g.ServiceDate = '{$ServiceDate}'
									  AND g.ShiftId = '{$ShiftId}')			
				Order By c.LastName, c.FirstName ";

				} else {

                    				$query ="SELECT
									
									COALESCE((SELECT 'Yes' FROM RegistrantVerifiedAvailability d
										WHERE a.RegistrantId = d.RegistrantId 
										AND d.ServiceDate = '{$ServiceDate}'
										AND d.ShiftId = '{$ShiftId}'),'') as AvailVerfFL, 
									COALESCE((Select group_concat( CredItemDesc
										SEPARATOR ', ' ) 
										FROM RegistrantCredItems k,  CredentialingItems h
										WHERE a.RegistrantId = k.RegistrantId
										  /*AND  k.StatusId in (1,2)*/ 	
                                          AND  k.CredItemId = h.Id ),'')  as CredItems,	
									COALESCE((SELECT  CASE count(*) 
										WHEN 0 THEN ''
											ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
										END  
									FROM RegistrantAttchedSpecialties k, Specialties h
										where 	h.Id = k.SpecialtyId
												and  a.RegistrantId = k.RegistrantId),'') as SpecialitiesList,  										  
									COALESCE((SELECT i.Comments FROM RegistrantAvailabilityComments i
										WHERE a.RegistrantId = i.RegistrantId 
										AND i.ServiceDate = '{$ServiceDate}'
										AND i.ShiftId = '{$ShiftId}'),'') as Comments, 
									COALESCE((SELECT sum(TotalHours) 
										 from WeeklyServices h
										Where a.RegistrantId = h.RegistrantId	
										AND h.PayrollWeek = '{$PayrollWeek}' 
										AND h.ScheduleStatusId > 4),'') as HoursScheduled,										
									a.RegistrantId,
									f.RegistrantGroupId,
									COALESCE(Availability, '') as Availability,
									COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,
									CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
									DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrantsNonRest a, 
							Users b,
							Registrants c,
							RegistrantTypes f
						
					WHERE a.UserId = b.UserId
					AND a.ClientId = '{$ClientId}' 
					AND a.ServiceTypeId = '{$ServiceTypeId}'
					AND a.RegistrantId = c.Id
					AND c.TypeId = f.Id
					AND a.Status = '1' 
					AND c.StatusId = '1'
					AND NOT EXISTS (SELECT 1 FROM WeeklyServices g
									  WHERE a.RegistrantId = g.RegistrantId
									  AND g.ServiceDate = '{$ServiceDate}'
									  AND g.ShiftId = '{$ShiftId}')			
				Order By c.LastName, c.FirstName ";

				}


                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Set Add Client Pending Shifts Data
             ===================================	*/		
			function setClientAddPendingShifts(	$ClientId, 
												$ClientUnitId, 
												$ScheduleStatusId,
												$ServiceDate,
												$WeekDay, 
												$PayrollWeek,
												$ShiftId,
												$ServiceTypeId,
												$UserId
											) 
			{
			
					 
					 $query ="INSERT INTO WeeklyServices
							                  (
												ClientId, 
												ClientUnitId, 
												ScheduleStatusId,
												ServiceDate,
												StartTime,
												EndTime,
												LunchHour,
												TotalHours,
												WeekDay, 
												PayrollWeek,
												ShiftId,
												ServiceTypeId,
												UserId,
												TransDate )	
 
								SELECT	'{$ClientId}',
										'{$ClientUnitId}',
										'{$ScheduleStatusId}',
										'{$ServiceDate}',
										StartTime,	 
                                        EndTime, 		
                                        LunchHour,
										TotalHours, 
										'{$WeekDay}',
										'{$PayrollWeek}',	 
                                        '{$ShiftId}', 
										'{$ServiceTypeId}',
 										'{$UserId}',
                                         NOW()	 	
								FROM ClientShifts
								WHERE ClientId = '{$ClientId}' 
								 AND  ShiftId = '{$ShiftId}' ";
								 
								
                       $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			/* Set Add Client Pending Shifts Data
             ===================================	*/		
			function setRegistrantAvailabilityComments(	$RegistrantId, 
														$ServiceDate,
														$ShiftId,
														$Comments,
														$UserId
													) 
			{
			
					 

                         $query ="REPLACE INTO RegistrantAvailabilityComments
									(RegistrantId,
									 ServiceDate,
									 ShiftId,
									 Comments,
									 UserId,
									 TransDate 
									) 

									VALUES
									(
									 '{$RegistrantId}',
									 '{$ServiceDate}',
									 '{$ShiftId}',
									 '{$Comments}',
									 '{$UserId}',
									  NOW()
									) ";
                                                     
                       $result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Set Add Client Pending Shifts Data
             ===================================	*/		
			function setMatchPendShiftsRegistrants(	$Data,
													$UserId) {
					
							
 
					// Match Pedning Shifts with Registrants   
					//===============================================					

					foreach ($Data as $rec) {
								
							$ScheduleId = $rec['ScheduleId'];
							$RegistrantId = $rec['RegistrantId'];

							$query = "UPDATE WeeklyServices
									set ScheduleStatusId = '6',
										RegistrantConfFL = '1',
										RegistrantId = '{$RegistrantId}',
										UserId = '{$UserId}',
										TransDate = now()
									Where 	Id = '{$ScheduleId}' "; 
																		
							$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
							}
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}	

			/* Get Registrant View "Attention" Credentialing Items 
             ===================================	*/		
			function getRegistrantViewAttnCredItems ($RegistrantId) {
			
                        $query ="Select RegistrantId,
										a.CredItemId,
										a.StatusId,
										b.CredItemCategory,
										CredItemType,
										CredItemDesc,
										a.CredItemStatus,
										ComplianceLevelId,
 										CASE (SELECT 1 FROM RegistrantDocuments  h
											WHERE a.RegistrantId = h.RegistrantId
											AND   a.CredItemId = h.DocumentTypeId
											AND   AwatingVerification = '1') 
											WHEN '1' THEN 'Document Uploaded. Awating Verifications'
											ELSE 'Click to Upload New Document'
										END AS DocumentVerified,

										COALESCE((SELECT 'uploaded' FROM RegistrantDocuments  h
											WHERE a.RegistrantId = h.RegistrantId
											AND   a.CredItemId = h.DocumentTypeId
											AND AwatingVerification = '1'),'expired') as status

									FROM 	RegistrantCredItems a,
											CredentialingItems b
									WHERE a.CredItemId = b.Id 	
									AND RegistrantId = '{$RegistrantId}'
									AND ((ComplianceLevelId = '1') || (DATEDIFF( ExpirationDate, CURDATE( ) ) between 0 and 30))";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Newly Uploaded Credentialing Item Document Awaiting Verification   
             ======================================================================	*/		
			function getRegistrantsCredItemDocsNewlyUploaded () {
			
                        $query ="SELECT a.Id as RegistrantCredItemTransId,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										c.SearchId,
										CredItemDesc,
										a.CredItemId,
										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										a.CredItemId,
										a.CredItemStatus,
										CredItemStatusDesc,
										CredItemStatusColor,
										CredItemStatusBGColor,
										ComplianceLevelId,
										CredItemType,
										StoredName as StoredDocName,
										COALESCE(a.Comments,'') as Comments,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	RegistrantCredItems a,
									CredentialingItems b,
									Registrants c,
									RegistrantDocuments d,
									RegistrantTypes f,
									CredentialingItemStatuses g,
									Users e 
							WHERE 	a.CredItemId = b.Id 
								AND a.RegistrantId = c.Id
								AND c.TypeId = f.Id
								AND a.CredItemStatus = g.CredItemStatus
								AND a.UserId = e.UserId
								AND a.RegistrantId = d.RegistrantId
								AND a.CredItemId = d.DocumentTypeId
								AND AwatingVerification = '1'
								AND a.CredItemStatus in (1,3)
							Order by RegistrantName, CredItemDesc";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			// Set Registrant Credentialing Item Docuemnt Verification Status 
            //====================================================			
			function setRegistrantDocumentVerificationStatus( 	$RegistrantId,
																$CredItemId,
																$AwatingVerification,
																$UserId) 
			{ 
                        $query = "UPDATE RegistrantDocuments 
                                   SET 	AwatingVerification  =  '{$AwatingVerification}',    
										UserId = '{$UserId}',
                                        TransDate = NOW()										
									WHERE 	RegistrantId = '{$RegistrantId}' 
									AND DocumentTypeId = '{$CredItemId}'";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

			/* Get Registrant (Blank) Forms   
             ======================================================================	*/		
			function getRegistrantForms () {
			
                        $query ="SELECT  FormDesc, StoredDocName FROM RegistrantForms;";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			// Get Cred. Items Not Selected for User Group Listing   
            //=======================			
			function getCredItemsNotSelectedRegGroup ($RegistrantGroupId) {
			
                        $query ="SELECT distinct 	a.Id AS id, 
													CredItemDesc
									FROM CredentialingItems a
								WHERE ConitionalItemFL = '0' 
								AND NOT EXISTS (SELECT 1 FROM RegistrantGroupCredItems c
									WHERE a.Id = c.CredItemId 
									AND   RegistrantGroupId = '{$RegistrantGroupId}')
									Order By CredItemDesc";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get Cred. Items Selected for User Group Listing   
            //=======================			
			function getCredItemsSelectedRegGroup ($RegistrantGroupId) {
			
                        $query ="SELECT distinct 	a.Id AS id, 
													CredItemDesc
									FROM CredentialingItems a
								WHERE ConitionalItemFL = '0' 
								AND  EXISTS (SELECT 1 FROM RegistrantGroupCredItems c
									WHERE a.Id = c.CredItemId 
									AND   RegistrantGroupId = '{$RegistrantGroupId}')
									Order By CredItemDesc";
						
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
						
						return $result;
			}

			// Get Registrant Groups Listing
            //=======================			
			function getRegistrantGroups() {
			
                        $query = "SELECT Id as id, 	
										 Id as RegistrantGroupId,
									     RegistrantGroupDesc 										 
									FROM RegistrantGroups
									order by Id";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			
			
			//  Update Registrant Group's Credentialing Items     
            //=======================			
			function setRegistrantGroupCredItems($RegistrantGroupId,
												$CredItems,			
												$UserId) 
				{
					
 
							
 
                    // Delete All Existing Cred. Items for selected RegistrantGroupId
					//===============================================					
					$query ="Delete from  RegistrantGroupCredItems
								Where RegistrantGroupId = '{$RegistrantGroupId}'";
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }

					// Insert newaly Selected Cred. items for selected RegistrantGroupId
					//========================================================================					

					foreach ($CredItems as $CredItem) {
							
							$CredItemId = $CredItem['id'];			
												
						$query ="Insert into RegistrantGroupCredItems
						   (RegistrantGroupId, CredItemId, UserId, TransDate)   
						values ( '{$RegistrantGroupId}',  '{$CredItemId}', '{$UserId}',  now()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			// Cleitn Orientation Potential Candidates List
			//================================================			
			function getClientOrientationPotentialCandidates($ClientId, $ClientUnitId, $ServiceTypeId) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										a.ClientUnitId,
										a.ServiceTypeId,
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END  
                                            FROM RegistrantAttchedSpecialties c, Specialties b
                                            where b.Id = c.SpecialtyId
                                              and  a.RegistrantId = c.RegistrantId) as SpecialtiesList,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrants a, 
							Users b,
							Registrants c,
							RegistrantTypes f
							
						WHERE a.UserId = b.UserId
						AND a.ClientId = '{$ClientId} '
						AND a.ClientUnitId = '{$ClientUnitId}' 
						AND a.ServiceTypeId = '{$ServiceTypeId}'
						
						AND a.RegistrantId = c.Id
						AND c.TypeId = f.Id
						AND a.Status = 0
						AND a.OrientationId = 0 
					Order By c.LastName, c.FirstName  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			// Cleitn Orientation Matched Candidates List
			//==============================================================			
			function getClientOrientationMatchedCandidates($OrientationId) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END  
                                            FROM RegistrantAttchedSpecialties c, Specialties b
                                            where b.Id = c.SpecialtyId
                                              and  a.RegistrantId = c.RegistrantId) as SpecialtiesList,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrants a, 
							Users b,
							Registrants c,
							RegistrantAttchedSpecialties d,
							RegistrantTypes f
							
						WHERE a.UserId = b.UserId
						AND a.OrientationId = '{$OrientationId}' 
						AND a.RegistrantId = d.RegistrantId
						AND a.RegistrantId = c.Id
						AND c.TypeId = f.Id
						AND a.Status = 3
					Order By c.LastName, c.FirstName  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}		

			/* Set Client Orienttaion Matched Candidates Data
             ===================================	*/		
			function setClientOrientMatchedCandidates(	$OrientationId,
														$Data,
														$UserId) {

					foreach ($Data as $rec) {
								
							$ApprovalId = $rec['ApprovalId'];
							$RegistrantId = $rec['RegistrantId'];

							$query = "UPDATE ClientApprovedRegistrants
									set Status = '3',
										OrientationId = '{$OrientationId}',
										UserId = '{$UserId}',
										TransDate = now()
									Where 	Id = '{$ApprovalId}' "; 
																		
							$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
							}
							
							$query1 = "Call  proc_setClientOrientationCandidateSchedule ('{$OrientationId}', '{$RegistrantId}', '{$UserId}')  "; 
							$result1 = $this->connection->getAll($query1, DB_FETCHMODE_ASSOC);

							if (DB::isError($result1)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
							}
							
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}	

			// Delete Registrant Credentialing Item Docuemnt Verification Status 
            //====================================================			
			function setDeleteCandidateOrientSchedules( $RegistrantId,
														$OrientationId) 
			{ 
                        $query = "DELETE FROM WeeklyServices 
									WHERE 	RegistrantId = '{$RegistrantId}' 
									AND OrientationId = '{$OrientationId}'";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			// Get Upcoming Orientations All Info
            //=====================================			
			function getUpcomingOrientationsAll() {
			
                        $query ="SELECT a.Id AS id, 
						                a.Id as OrientationId,     
										a.ClientId,
										ClientName,
										f.SearchId,
										a.ClientUnitId,
										UnitName,
										a.ServiceTypeId,
										ServiceTypeDesc,
										RequestedQty,
										(SELECT count(* ) FROM ClientApprovedRegistrants e
											WHERE a.Id = e.OrientationId) as MatchedQty,
										
										DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate, 
										(SELECT  CASE count(*) 
											WHEN 0 THEN ''
											ELSE group_concat( DATE_FORMAT( OrientAddtnlDate, '%m-%d-%Y' ) 	 SEPARATOR ', ' )
											END 
												FROM ClientOrientationAddtnlDates f
												WHERE a.Id = f.OrientationId) as OrientAddtnlDatesList,
							
										COALESCE(a.Comments, '') as Comments, 
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	ClientOrientations a, 
											Users b, 
											ClientUnits e,
											ServiceTypes h,
											Clients f		
									WHERE a.UserId = b.UserId
									AND a.ClientUnitId = e.Id
									AND a.ServiceTypeId = h.Id
									AND a.ClientId = f.Id
									AND StartDate >= CURDATE()
							Order By ClientName, StartDate "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			//==============================
			// Get Client's Assigment Info
            //==============================			
			function getClientAssigments($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id as Id,
										a.StatusId,
										AssignmentStatusDesc,
										c.BackgroundColor,
										c.TextColor,
										a.ClientId, 
										a.ClientUnitId, 
										b.UnitName,
										a.Comments,
										a.RegistrantTypeId, 
										a.RegistrantId, 
										COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) 
											FROM Registrants c
											WHERE a.RegistrantId = c.Id),'To Be Assigned...') as RegistrantName, 
										a.ServiceTypeId,
										ServiceTypeDesc,
										DATE_FORMAT( a.StartDate, '%m-%d-%Y' ) as StartDate,
										DATE_FORMAT( a.EndDate, '%m-%d-%Y' ) as EndDate,
										(Select group_concat( DATE_FORMAT(d.ServiceDate, '%m/%d/%Y' )
                                         SEPARATOR ',' ) 
											FROM WeeklyServices d
											WHERE d.AssignmentId = a.Id
											AND   d.ScheduleStatusId > 5
											AND   DATEDIFF(CURDATE(),d.ServiceDate) < 60 
											GROUP BY AssignmentId) as ExcludedDates,
										
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) as UserName, 
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate	
									FROM 	ClientAssignments a, 
											ClientUnits b, 
											ClientAssignmentStatuses c,
											ServiceTypes h,
											Users e  
									WHERE a.ClientId = '{$ClientId}'	
									AND a.ClientUnitId = b.Id
									AND a.StatusId = c.StatusId
									AND a.StatusId != '3'
									AND a.ServiceTypeId = h.Id									
									AND a.UserId = e.UserId 
									ORDER BY a.StartDate DESC"; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	
	
			// Get Shifts Combinations Listing
            //=======================			
			function getShiftsCombinations() {
			
                        $query = "SELECT ShiftsCombinationValues, 
										 ShiftsCombinationDesc 
									FROM ShiftsCombinations ";
									 
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
					}
			//==============================
			// Set Client's Assigment Info
            //==============================			
			function setClientAssigment(	$Id,
											$StatusId,
											$ClientId,
											$ClientUnitId,
											$ServiceTypeId,
											$StartDate,
											$EndDate,
											$ConfirmationNumber,	
											$UserId) {
					
					if(is_numeric($Id))  { 	

                         $query ="update ClientAssignments
						            set StatusId = '{$StatusId}', 		
                                        ClientId = '{$ClientId}',
										ClientUnitId = '{$ClientUnitId}', 
										ServiceTypeId = '{$ServiceTypeId}', 
										StartDate = '{$StartDate}', 
										EndDate = '{$EndDate}',
                                        ConfirmationNumber = '{$ConfirmationNumber}', 		
                                        UserId = '{$UserId}',
                                        TransDate = now()	
										WHERE ID = '{$Id}' 	";
					} else {
							$query ="insert into ClientAssignments
							                  (
											   StatusId,
											   ClientId,
											   ClientUnitId,
											   ServiceTypeId,
											   StartDate,
											   EndDate,
											   ConfirmationNumber,
 											   UserId,
											   TransDate )
											  
						       values ( '{$StatusId}',
										'{$ClientId}',  	
                                        '{$ClientUnitId}', 
										'{$ServiceTypeId}',
                                        '{$StartDate}', 
										'{$EndDate}',
                                        '{$ConfirmationNumber}', 		
                                        '{$UserId}',
                                         NOW()	)";
					
					}		

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}				
					
			/* Get Client Assignment Registrants Selection Info
            //================================================ */			
			function getClientAssignmentRegistrantsSelection(	$ClientId,
																$ClientUnitId,
																$ServiceTypeId,
																$StartDate,
																$EndDate
														  ) 
			{
                        								
						$query = "Call proc_getClientAssignmentRegistrantsSelection (	'{$ClientId}',
																						'{$ClientUnitId}',
																						'{$ServiceTypeId}',
																						'{$StartDate}',
																						'{$EndDate}' ) "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			//==============================
			// Set Client's Assigment Registrant Info
            //==============================			
			function setClientAssigmentRegistrant(	$Id,
													$RegistrantId,
													$UserId) {
					
	
                         $query ="UPDATE ClientAssignments
						            SET 	RegistrantId = '{$RegistrantId}', 		
                                         	StatusId = '2',
                                         	UserId = '{$UserId}',
                                        	TransDate = now()	
										WHERE ID = '{$Id}' 	";
	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;
				return $query;			
			}				

			/* Set Add Client Assignment ShiftsData
             ===================================	*/		
			function setClientAssignmentShifts(	$AssignId,
												$Data,
												$UserId) {
					
							
 
					// Match Pedning Shifts with Registrants   
					//===============================================					

					foreach ($Data as $rec) {
								
							$ServiceDate = $rec['ServiceDate'];
							$ShiftId = $rec['ShiftId'];
							//============	
							$dw = date( "w", strtotime( $ServiceDate));
							if ($dw == 6) {
								$we_date = strtotime('Saturday', strtotime($ServiceDate));

							}   else { 
								
								$we_date = strtotime('next Saturday', strtotime($ServiceDate));

							}
	

							$PayrollWeek =  date('Y-m-d', $we_date);	

							//============
							$query = "Call proc_setClientAssignmentShifts ( '{$AssignId}',
																			'{$ServiceDate}',
																			'{$PayrollWeek}',
																			'{$ShiftId}',
																			'{$UserId}' ) "; 
							
							$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
							}
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}	

			// Set Client Assignment Schedules Info
            //=======================			
			function getClientAssigmentSchedules($AssignmentId) {
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										ScheduleStatusId, 
										ScheduleStatusDesc,
										TextColor,
										BackgroundColor,
										a.ClientId, 
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										PayrollWeek,  
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										ServiceTypeId,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											ScheduleStatuses g								
										Where 	a.AssignmentId= '{$AssignmentId}' 
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
											ORDER BY ServiceDate, StartTime ";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}	


			/* Get Cncel Client Assignment Info
            //================================================ */			
			function setCancelClientAssignment(	$AssignmentId,
												$Msg,
												$UserId
														  ) 
			{
                        								
						$query = "Call proc_setCancelClientAssignment ('{$AssignmentId}','{$Msg}','{$UserId}' ) "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get the List of Mandatory Credendtiling - Selected (by Registrant Type)
            //============================================================== */			
			function getCredItemsMandatorySelected(	$RegistrantTypeId )
														   
			{
                        								
						$query = "Call proc_getCredItemsMandatorySelected ('{$RegistrantTypeId}' ) "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get the List of Mandatory Credendtiling - Un-Selected (by Registrant Type)
            //============================================================== */			
			function getCredItemsMandatoryUnselected(	$RegistrantTypeId )
														  
			{ 
                        								
						$query = "Call proc_getCredItemsMandatoryUnselected ('{$RegistrantTypeId}' ) "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			//  Update Mandatory Credentialing Items for selected Registrant Type    
            //==========================================================			
			function setCredItemsMandatory(	$RegistrantTypeId,
											$CredItems,			
											$UserId) 
				{
					
 
                    // Delete All Existing Cred. Items for selected RegistrantGroupId
					//===============================================					
					$query ="DELETE FROM  CredentialingItemsMandatory
								WHERE RegistrantTypeId = '{$RegistrantTypeId}'";
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }

					// Insert newaly Selected Mandatory Cred. Items for selected Registrant Type					//========================================================================					

					foreach ($CredItems as $CredItem) {
							
							$CredItemId = $CredItem['id'];		
							$CondFL = $CredItem['CondFL'];	
										
												
						$query ="INSERT INTO CredentialingItemsMandatory
						   (RegistrantTypeId, MandatoryCredItemId, CondFL, UserId, TransDate)   
						VALUES ( '{$RegistrantTypeId}',  '{$CredItemId}', '{$CondFL}' , '{$UserId}',  NOW()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}				

			/* Get the List of Client's Mandatory Credendtiling Items- Selected (by Client, Client Unit)
            //========================================================================================= */			
			function getCredItemsClientMandatorySelected ($ClientId,  $ServiceTypeId)
														   
			{
                        								
					$query = "Call proc_getCredItemsClientMandatorySelected1 ('{$ClientId}',  '{$ServiceTypeId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	

			/* Get the List of Mandatory Credendtiling Items - Un-Selected (by Client, Client Unit)
            //============================================================== */			
			function getCredItemsClientMandatoryUnselected ($ClientId,  $ServiceTypeId)
														  
			{ 
                        								
					$query = "Call proc_getCredItemsClientMandatoryUnselected1 ('{$ClientId}',  '{$ServiceTypeId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	

			/* Get List of Mandatory Specilaties - Selected (by Client, Client Unit)
            //========================================================================================= */			
			function getSpecialtiesClientMandatorySelected ($ClientId, $ServiceTypeId)
														   
			{
                        								
					$query = "Call proc_getSpecialtiesClientMandatorySelected1 ('{$ClientId}',  '{$ServiceTypeId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	

			/* Get List of Mandatory Specilaties - Un-Selected (by Client, Client Unit)
            //============================================================== */			
			function getSpecialtiesClientMandatoryUnselected (	$ClientId, 
																$ServiceTypeId)
														  
			{ 
                        								
				  $query ="SELECT 	Id,
									Id as id,
									SpecialtyDesc FROM Specialties a
									WHERE  NOT EXISTS (SELECT 1 FROM SpecialtiesClientMandatory b
														WHERE b.ClientId = '{$ClientId}'
														AND b.ServiceTypeId = '{$ServiceTypeId}'
														AND a.Id = b.SpecialtyId)  						
									ORDER BY a.SpecialtyDesc "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}				
			// Get Credenting Items Client's Units Listing 
            //=======================			
			function getCredItemsClientUnits($ClientId) {
                        $query = "SELECT ClientId, Id as id, UnitName 
									FROM ClientUnits  
									WHERE ClientId = '{$ClientId}'
									AND StatusId = '1'
									ORDER BY Id ";
													  
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			//  Update Mandatory Credentialing Items for selected Client/Unit/Service Type   
            //=================================================================================			
			function setCredItemsClientMandatory(	$ClientId,
													$ServiceTypeId,
													$CredItems,	
													$CredItemsRem,
													$UserId) 
				{
					
 
                    // Delete "Removed" Cred. Items
					//===============================================					
					
					foreach ($CredItemsRem as $CredItemRem) {
							
						$CredItemRemId = $CredItemRem['id'];		

					
						$query ="DELETE FROM  CredentialingItemsClientMandatory
									WHERE  ClientId = '{$ClientId}'
									AND ServiceTypeId = '{$ServiceTypeId}'
									AND MandatoryCredItemId = '{$CredItemRemId}'";
					 

						$result = $this->connection->query($query);
							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
								
							}
							
					}		

					foreach ($CredItems as $CredItem) { // start of 'foreach' loop
							
							$CredItemId = $CredItem['id'];		
							$CondFL = $CredItem['CondFL'];	
							$UnitIds = $CredItem['UnitIds'];
							
						// Delete Cred. Items
						//============================================	
						$query ="DELETE FROM  CredentialingItemsClientMandatory
									WHERE  ClientId = '{$ClientId}'
									AND ServiceTypeId = '{$ServiceTypeId}'
									AND MandatoryCredItemId = '{$CredItemId}'";
					 

						$result = $this->connection->query($query);
							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
								
							}

						//============================================

						$units_arr = explode(",", $UnitIds);
	
						for($i=0;$i<count($units_arr);$i++) { //array itr. - start 
							
							$unit_id =  $units_arr[$i];
						
							// Insert Cred. Items							
							//============================================						
							$query ="INSERT INTO CredentialingItemsClientMandatory
							   (ClientId,
							    ClientUnitId,
								ServiceTypeId,
								MandatoryCredItemId, 
								CondFL, 
								UserId, 
								TransDate)   
							SELECT   	'{$ClientId}',
										'{$unit_id}',
										'{$ServiceTypeId}',
										'{$CredItemId}', 
										'{$CondFL}' , 
										'{$UserId}',  
										NOW()  "; 
							 
							
							$result = $this->connection->query($query);
	                        if (DB::isError($result)){
	                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
								
	                        }
						

						} //array itr. - end

					
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			//   Update Mandatory Specialties for selected Client/Unit/Service Type     
            //====================================================================			
			function setSpecialtiesClientMandatory(	$ClientId,
													$ServiceTypeId,
													$Specialties,	
													$SpecialtiesRem,
													$UserId) 
				{
					
                    // Delete "Removed" Specialties
					//===============================================					
					
					foreach ($SpecialtiesRem as $SpecialtieRem) {
							
						$SpecialtieRemId = $SpecialtieRem['id'];		

					
						$query ="DELETE FROM  SpecialtiesClientMandatory
									WHERE  ClientId = '{$ClientId}'
									AND ServiceTypeId = '{$ServiceTypeId}'
									AND SpecialtyId = '{$SpecialtieRemId}'";
					 

						$result = $this->connection->query($query);
							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
								
							}
							
					}		

					foreach ($Specialties as $Specialty) { // start of 'foreach' loop
							
							$SpecialtyId = $Specialty['id'];		
							$UnitIds = $Specialty['UnitIds'];
							
						// Delete Specialties
						//============================================	
						$query ="DELETE FROM  SpecialtiesClientMandatory
									WHERE  ClientId = '{$ClientId}'
									AND ServiceTypeId = '{$ServiceTypeId}'
									AND SpecialtyId = '{$SpecialtyId}'";
					 

						$result = $this->connection->query($query);
							if (DB::isError($result)){
								die("Could not query the database:<br />$query ".DB::errorMessage($result));
								
							}

						//============================================

						$units_arr = explode(",", $UnitIds);
	
						for($i=0;$i<count($units_arr);$i++) { //array itr. - start 
							
							$unit_id =  $units_arr[$i];
						
							// Insert Specialties							
							//============================================						
							$query ="INSERT INTO SpecialtiesClientMandatory
							   (ClientId,
							    ClientUnitId,
								ServiceTypeId,
								SpecialtyId, 
								UserId, 
								TransDate)   
							SELECT   	'{$ClientId}',
										'{$unit_id}',
										'{$ServiceTypeId}',
										'{$SpecialtyId}', 
										'{$UserId}',  
										NOW()  "; 
							 
							
							$result = $this->connection->query($query);
	                        if (DB::isError($result)){
	                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
								
	                        }
						

						} //array itr. - end

					
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;	
			}		
			
			/*Set Update Registrant's Client Required Credendtiling Items  
            //============================================================== */			
			function setUpdateRegistrantsClientRequiredCredItems ($UserId)
														  
			{ 
                        								
					$query = "Call proc_UpdateRegistrantsClientRequiredCredItems ('{$UserId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	
			

			/*Set Update Fegistrant's Mandatory Credendtiling Items  
            //============================================================== */			
			function setUpdateRegistrantsMandatoryCredItems ($RegistrantTypeId, $UserId)
														  
			{ 
                        								
					$query = "Call proc_UpdateRegistrantsMandatoryCredItems ('{$RegistrantTypeId}','{$UserId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	


			/* Get the List of Specialties Selected (by Registrant Type)
            //============================================================== */			
			function getSpecialtiesByRegistrantTypeSelected(	$RegistrantTypeId )
														  
			{ 
                        								
                       $query = "	SELECT 	a.Id as id, 
											a.SpecialtyDesc
									FROM  Specialties a
									WHERE EXISTS (SELECT 1 FROM RegistrantTypeSpecialties b
													WHERE b.RegistrantTypeId = '{$RegistrantTypeId}'
													AND a.id = b.SpecialtyId )
									ORDER BY a.SpecialtyDesc  ";
													  
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			

			/* Get the List of Specialties Un-Selected (by Registrant Type)
            //============================================================== */			
			function getSpecialtiesByRegistrantTypeUnselected(	$RegistrantTypeId )
														  
			{ 
                        								
                       $query = "	SELECT 	a.Id as id, 
											a.SpecialtyDesc
									FROM  Specialties a
									WHERE NOT EXISTS (SELECT 1 FROM RegistrantTypeSpecialties b
													WHERE b.RegistrantTypeId = '{$RegistrantTypeId}'
													AND a.id = b.SpecialtyId )
									ORDER BY a.SpecialtyDesc ";
													  
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get List of Mandatory Specilaties - Selected (by Client, Client Unit)
            //========================================================================================= */			
			function getRegistrantClientRequiredCredItems (	$RegistrantId,
															$ClientId,
															$ClientUnitId,
															$ServiceTypeId)
														   
			{
                        								
					$query = "Call proc_getRegistrantClientRequiredCredItems ('{$RegistrantId}',
																			  '{$ClientId}',
																			  '{$ClientUnitId}',	
																			  '{$ServiceTypeId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	

			/* Set List of Mandatory Specilaties - Selected (by Client, Client Unit)
            //========================================================================================= */			
			function setRegistrantRequiredCredItems ($RegistrantId, $UserId)
														   
			{
                        								
					$query = "Call proc_setRegistrantRequiredCredItems ('{$RegistrantId}',  '{$UserId}' ) "; 
					$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}	

			
			// Get Mesage Types  Listing
            //=====================================			
			function getMessageTypes($MessageGroupId) {
			
                        $query = "SELECT Id as id, 	
										 Id as MessageTypeId,
                                         MessageTypeDesc  										 
									FROM MessageTypes
									WHERE MessageGroupId = '{$MessageGroupId}'
									ORDER by MessageTypeDesc";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
						 
 						return $result;
						 

			}

			// Get Registrant Messages
            //=======================			
		 	function getRegistrantMessages($RegistrantId) {
			
                        $query ="SELECT a.Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								RegistrantId,
								MessageTypeId,
								MessageTypeDesc,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM RegistrantMessages a, Users b, MessageTypes c  
								WHERE RegistrantId = '{$RegistrantId}'
								AND   a.MessageTypeId = c.Id 
								AND a.UserId = b.UserId 
								Order By  a.TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			/* Set Registrant's Message */
            /*======================= */			
			function setRegistrantMessages( $RegistrantId,
											$HighPriority,
											$MessageTypeId,
											$Msg,
											$UserId )  
			{
				
						$Msg = $this->connection->escapeSimple ($Msg); 
                       	$query ="Insert into RegistrantMessages 
								(RegistrantId, 
								HighPriority,
								MessageTypeId,
								Msg,
								UserId,
								TransDate )
				values 	('{$RegistrantId}',  
						'{$HighPriority}',
						'{$MessageTypeId}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}

			// Get Dasboard Registrants Inquiry  Messages
            //================================================			
		 	function RegistrantsMessagesByType(	$MessageTypeId,
												$FromMessageDate,
												$ToMessageDate) {
			
                        $query ="SELECT 	c.Id as RegistrantId,
											CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')', ' (', c.Id,')') as RegistrantName,
											Msg,
											CONCAT( trim( a.FirstName ) , ' ', trim( a.LastName ) ) AS UserName,
											DATE_FORMAT( b.TransDate, '%m-%d-%Y %r' ) as TransDate
										
									FROM 	Registrants c,
											RegistrantTypes f,
											RegistrantMessages b,
											Users a		
									WHERE 	c.TypeId = f.Id
									AND 	c.StatusId = '1'
									AND     c.Id = b.RegistrantId
									AND     MessageTypeId = '{$MessageTypeId}'
									AND     b.TransDate BETWEEN '{$FromMessageDate}' and '{$ToMessageDate}'
									AND     b.UserId = a.UserId 
									ORDER BY RegistrantName"; 
								                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			// Get Service Cancellation Reasons Listing
            //=====================================			
			function ServiceCancellationReasons($ServiceCancellationReasonTypeId) {
			
                        $query = "SELECT Id as id, 	
										 Id as ServiceCancellationReasonId,
                                         ServiceCancellationReasonDesc  										 
									FROM ServiceCancellationReasons
									WHERE ServiceCancellationReasonTypeId = '{$ServiceCancellationReasonTypeId}'
									ORDER by Id";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
						 
 						return $result;
						 

			}

			//=====================================================
			// Get Client's Service Cancellations History Info
            //=====================================================			
			function getClientServiceCancellations($ClientId) {
			
                        	$query =  "SELECT 
                            			COALESCE((  SELECT CONCAT( trim( d.LastName ) , ', ', trim( d.FirstName ) , ' (', RegistrantTypeDesc, ')' ) 
                         					FROM  Registrants d, RegistrantTypes c
											WHERE  f.RegistrantId = d.Id
                            				AND Typeid = c.Id
                         				),'')	AS RegistrantName,
 										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										CancelReason,
										COALESCE((SELECT  ServiceCancellationReasonDesc
												FROM ServiceCancellationReasons e
												WHERE  e.id = ServiceCancellationReasonId 
											),'Undefined') as ServiceCancellationReasonDesc,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	ClientServiceCancellations a,
                                            WeeklyServices f,  
                                           
 											Users b			
									WHERE 	 a.UserId = b.UserId
                                            AND a.ScheduleId = f.Id
                                            AND a.ClientId = '{$ClientId}' 
                                        Order By a.TransDate Desc 	"; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get List of Mandatory Specilaties for give Client/Service Type/Client Unit
            //============================================================== */			
			function getClientServiceTypeReqSpecialties (	$ClientId, 
															$ServiceTypeId,
															$ClientUnitId)
														  
			{ 
                        								
				  $query ="SELECT b.Id as id, SpecialtyDesc 
							FROM SpecialtiesClientMandatory a, Specialties b
							 WHERE ClientId = '{$ClientId}' 

							 AND ServiceTypeId = '{$ServiceTypeId}'
							 AND ClientUnitId in (0,{$ClientUnitId})
							 AND b.id = a.SpecialtyId 
							 Order By SpecialtyDesc"; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

					if (DB::isError($result)){
						die("Could not query the database:<br />$query ".DB::errorMessage($result));
					}
		
					return $result;
						
			}

          /* Set User Password  
          //=======================	*/		
			function setUserPassword(	$CurrPassword,
										$NewPassword, 
										$UserId) {
			
                        $query ="Update Users 
								Set Password = '{$NewPassword}',
								    NextResetDate = DATE_ADD(CURDATE(), INTERVAL 90 DAY), 
									ResetFL = 0
							WHERE 	UserId = '{$UserId}' 
									AND Password = '{$CurrPassword}' ";     
                                   	 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Daily Messages
            ======================= */			
		 	function getDailyMessages($MessageDate) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM DailyMessages a, Users b  
								WHERE MessageDate = '{$MessageDate}'
								AND a.UserId = b.UserId 
								Order By  a.TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	
			
			/* Set New Daily Message  
            ======================= */			
			function setDailyMessage   ($MessageDate,
										$HighPriority,
										$Msg,
										$UserId )  
			{
			
						$Msg = $this->connection->escapeSimple ($Msg); 	
                       	$query ="INSERT INTO DailyMessages 
								(MessageDate, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$MessageDate}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	

		    //====================================			
			// Get Un-Apporoved Registrants	Info
			//===================================
			
			function getUnApprovedRegistrants(	$FromDate, 
												$ToDate)

			{
						$query ="SELECT	c.Id,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc,
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate,

										CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										(Select group_concat( CredItemDesc
                                        		 SEPARATOR ', ' ) 
					                    FROM RegistrantCredItems k,  CredentialingItems h
					                        WHERE  c.Id  = k.RegistrantId 
			                                AND  k.CredItemId = h.Id
			                                AND CredItemStatus in (1,3)) as ExpiredCredItems,
										(SELECT  CASE count(*) 
			                                                    			WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
												END as SpecialtiesList
                                            			FROM RegistrantAttchedSpecialties f, Specialties g
                                            				where g.Id = f.SpecialtyId
                                                            and  c.Id = f.RegistrantId) as SpecialtiesList,
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate
								FROM 	
										Registrants c,
										RegistrantTypes f,
										Users b
													
										WHERE 	c.TypeId = f.Id
										AND     c.UserId = b.UserId
										AND     c.StatusId = '1' 	 
												AND NOT EXISTS (SELECT 1 FROM ClientApprovedRegistrants d
								                               WHERE c.Id = d.RegistrantId
								                              
														) 
										AND c.EntryDate between '{$FromDate}' and '{$ToDate}'				 
										Order by c.EntryDate, RegistrantName";
                                                       
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			

			}			

			/* Get Alert Messages
            ======================= */			
		 	function getAlertMessages() {
			
                        $query ="SELECT Id as AlertId ,
								Msg,
								StatusId,
								0 as  Seq,
								'Orig' as AlertType,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b  
								WHERE a.UserId = b.UserId 
								
						UNION								
								SELECT c.AlertId ,
								c.Msg,
								a.StatusId,
								c.Id as Seq,
								'Resp' as AlertType,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( c.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b, AlertMessageResponses c  
								WHERE a.Id = c.AlertId   
								AND c.UserId = b.UserId 

								Order By    StatusId, SortTransDate Desc,  AlertId Desc, Seq "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			/* Set Alert Message Responce  
            ============================================== */			
			function setAlertMessageResponse   ($AlertId,
												$Msg,
												$SchoolFL,
												$UserId )  
			{
			
						$Msg = $this->connection->escapeSimple ($Msg); 	
                       	$query ="INSERT INTO AlertMessageResponses 
								(AlertId, 
								Msg,
								SchoolFL,
								UserId,
								TransDate )
				values 	('{$AlertId}',  
						'{$Msg}',
						'{$SchoolFL}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	

			/* Set Close Alert Message  
            ============================================== */			
			function setCloseAlertMessage (	$AlertId,
											$UserId )  
			{
			
                       	$query = "UPDATE AlertMessages
                       				SET StatusId = '1'
                       			  WHERE Id = '{$AlertId}'	
                       	 ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	

			/* Set New Alert Message  
            ============================================== */			
			function setNewAlertMessage (	$Msg,
											$SchoolFL,
											$UserId )  
			{
			
					$Msg = $this->connection->escapeSimple ($Msg); 

                   	$query = "INSERT INTO AlertMessages
                   				(
                   					StatusId,
                   					Msg,
                   					SchoolFL,
                   					UserId,
                   					TransDate	
                   				)
							  VALUES (
							  		'0',
							  		'{$Msg}',
							  		'{$SchoolFL}',
							  		'{$UserId}',
							  		NOW()
							  	)";


					
                    $result = $this->connection->query($query);
                    if (DB::isError($result)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result));
                    }
		
					return $result; 
					//return $query;
						
			}	

		    //================================================			
			// Get Apporoved Registrants by Entry Date	Info
			//================================================
			
			function getApprovedRegistrantsByEntryDates($FromDate, $ToDate) {
						$query ="SELECT	c.Id,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc,
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate,

										CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										(Select group_concat( CredItemDesc
                                        		 SEPARATOR ', ' ) 
					                    FROM RegistrantCredItems k,  CredentialingItems h
					                        WHERE  c.Id  = k.RegistrantId 
			                                AND  k.CredItemId = h.Id
			                                AND CredItemStatus in (1,3)) as ExpiredCredItems,
										(SELECT  CASE count(*) 
			                                                    			WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
												END as SpecialtiesList
                                            			FROM RegistrantAttchedSpecialties f, Specialties g
                                            				where g.Id = f.SpecialtyId
                                                            and  c.Id = f.RegistrantId) as SpecialtiesList,
										(Select group_concat( ClientName
                                        		 SEPARATOR ', ' ) 
					                    FROM ClientApprovedRegistrants k,  Clients h
					                        WHERE  c.Id  = k.RegistrantId 
			                                AND  k.ClientId = h.Id ) as ApprovedByClients,
			                                
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate
								FROM 	
										Registrants c,
										RegistrantTypes f,
										Users b
													
										WHERE 	c.TypeId = f.Id
										AND     c.UserId = b.UserId
										AND     c.StatusId = '1' 	 
										AND EXISTS (SELECT 1 FROM ClientApprovedRegistrants d
								                               WHERE c.Id = d.RegistrantId
								                              
														)
										AND c.EntryDate between '{$FromDate}' and '{$ToDate}'				 
										Order by c.EntryDate, RegistrantName";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;

			}			


			/* Set Change Registrant Type Credentialing Items
            //======================= */ 			
			function setRegistrantTypeCredItemsChange (	$RegistrantId, 
														$SourceTypeId,
														$DestTypeId,
														$UserId) {
			
 
							
                        $query = "Call proc_setRegistrantTypeCredItemsChange ('{$RegistrantId}',
                        													  '{$SourceTypeId}',
                        													  '{$DestTypeId}',
                        													  '{$UserId}' )  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	


			/* Get Registrants Daily Availability
            ========================================== */			
		 	function getRegistrantsDailyAvailibility($ClientId, $ServiceDate) {
			
                        $query ="SELECT 	CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
											UnitName,
											ServiceTypeDesc,
											ShiftName,
											COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,
											
											(Select group_concat( CredItemDesc
									                 SEPARATOR ', ' ) 
									        FROM    RegistrantCredItems k,  
									                CredentialingItems g	
									            WHERE  c.Id  = k.RegistrantId 
									            AND  k.CredItemId = g.Id
									            AND ComplianceLevelId = 1
									            AND ((g.CredItemCategory  !='2') || 
									                ((g.CredItemCategory  = '2') && (k.StatusId != '0')))) as ExpiredCredItems
 


									FROM  RegistrantVerifiedAvailability a,
									      ClientApprovedRegistrantsNonRest b,
									      Registrants c,
									      RegistrantTypes f,
									      ClientUnits e,
										  ServiceTypes h,
										  Shifts d	

									WHERE b.ClientId = '{$ClientId}'
									AND   a.RegistrantId = b.RegistrantId
									AND   a.RegistrantId = c.Id
									AND   c.TypeId = f.Id
									AND   a.ServiceDate = '{$ServiceDate}'
									AND   b.ClientUnitId = e.Id
									AND   b.ServiceTypeId = h.Id
									AND   a.ShiftId = d.ShiftId
									AND   a.ShiftMatchedFL = '0' 
									AND NOT EXISTS (SELECT 1 FROM WeeklyServices i
										              WHERE a.RegistrantId = i.RegistrantId
										              AND   i.ServiceDate = '{$ServiceDate}'
										              AND   a.ShiftId = i.ShiftId )

									

									ORDER BY a.ShiftId, c.LastName, c.FirstName"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
                        //return $query;	
			}	



			// Get School Districts Side Navigation Data
            //==============================================			
			function getSchDistrictSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchDistrictSideNavigation 
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


			
			/* Get Districts Listing
            //======================= */			
			function getSchDistricts() {
                        $query = "SELECT Id as id, 
										Id, 
										DistrictName,
										SearchId,
										ExtId as BillingClientCode
							FROM SchDistricts 
					order by DistrictSort  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}



			/* Get Selected (SCH) District's General Info  
            /*=============================================== */			
			
			function getSelSchDistrict($DistrictId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id, 
										ExtId,
										SearchId,
										StatusId,
										DistrictName,
										BoroughId,
										StreetAddress1,
										StreetAddress2,
										City,
										State,
										ZipCode,
										LiaisonFirstName,
										LiaisonLastName,
										OfficePhone,
										MobilePhone,
										Fax,
										Email,
										Comments,
										PerDiemBillingCode,
										LongTermBillingCode,
										UserId,
										TransDate
									FROM SchDistricts a  
									WHERE Id = '{$DistrictId}'  "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			


			/* Get Boroughs (Sch) Listing
            //=======================	*/		
			function getSchBoroughs($State) {
			
                        $query = "SELECT 	Id as id, 
											Id as BoroughId, 	
											BoroughName		
								FROM  SchBoroughs 
								Where State = '{$State}'
							Order by BoroughName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}



			/* Set (SCH) Selected District's General Info */
            /*========================================= */			
			function setSelSchDistrict( $Id,
										$ExtId,
										$StatusId,
										$SearchId,
										$DistrictStatusId,
										$DistrictName,
										$BoroughId,
										$StreetAddress1,
										$City,
										$State,
										$ZipCode,
										$LiaisonFirstName,
										$LiaisonLastName,
										$OfficePhone,
										$MobilePhone,
										$Fax,
										$Email,
										$Comments,
										$PerDiemBillingCode,
										$LongTermBillingCode,
										$UserId )  
			{
	
				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchDistricts
								set ExtId =  '{$ExtId}',
								StatusId =  '{$StatusId}',
								SearchId =  '{$SearchId}', 
								DistrictName =  '{$DistrictName}',
								BoroughId =  '{$BoroughId}',
								StreetAddress1	 =  '{$StreetAddress1}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								LiaisonFirstName =  '{$LiaisonFirstName}',
								LiaisonLastName	 =  '{$LiaisonLastName}',
								OfficePhone	 =  '{$OfficePhone}',
								MobilePhone	 =  '{$MobilePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								Comments =  '{$Comments}',
								PerDiemBillingCode	 =  '{$PerDiemBillingCode}',
								LongTermBillingCode	 =  '{$LongTermBillingCode}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchDistricts 
								(ExtId, 
								StatusId,
								SearchId, 
								DistrictName,
								BoroughId,
								StreetAddress1,
								City,
								State,
								ZipCode,
								LiaisonFirstName,
								LiaisonLastName,
								OfficePhone,
								MobilePhone,
								Fax,
								Email,
								Comments,
								PerDiemBillingCode,
								LongTermBillingCode,
								UserId,
								TransDate )
				values 	('{$ExtId}',  
						'{$StatusId}',
						'{$SearchId}',
						'{$DistrictName}',
						'{$BoroughId}',  
						'{$StreetAddress1}',  
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$LiaisonFirstName}',
						'{$LiaisonLastName}',
						'{$OfficePhone}', 
						'{$MobilePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$Comments}',
						'{$PerDiemBillingCode}',
						'{$LongTermBillingCode}',
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query; 
						
			}	


			// Get (SCH) District Messages
            //=========================================			
		 	function getSchDistrictMessages($DistrictId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM SchDistrictMessages a, Users b  
								WHERE DistrictId = '{$DistrictId}'
								AND a.UserId = b.UserId 
								Order By  a.TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			/* Set (SCH) District Message */
            /*======================= */			
			function setSchDistrictMessages($DistrictId,
											$HighPriority,
											$Msg,
											$UserId )  
			{
				
                       $query ="Insert into SchDistrictMessages 
								(DistrictId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$DistrictId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	


			/* Get (SCH) District's Services Details Info */
            /*================================================= */			
			function getSchDistrictServiceDetails ($DistrictId) {
			
                        $query ="SELECT a.Id,
										b.Id as ServiceTypeId, 
										b.ServiceTypeDesc,   
										
										CASE b.ServiceCategoryId
								        	WHEN '0' THEN 'School'  
								        	ELSE 'Student'
								        END AS 'ServiceCategoryDesc', 
								        
								        CASE b.RegistrantTypeId
								        	WHEN '12' THEN 'RN'
								        	WHEN '23' THEN 'Para'
								        	WHEN '7'  THEN 'PT'
								        	WHEN '17'  THEN 'OT'
								        	WHEN '16'  THEN 'ST'

										END AS 'RegistrantTypeDesc',
										COALESCE(PrimaryVendorFL,'0') as PrimaryVendorFL,
										COALESCE(PayRate, '0.00') as PayRate,
										COALESCE(BillRate, '0.00') as BillRate,
										COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName )),'') as UserName,
										COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ),'') as TransDate 

									FROM SchDistrictServiceDetails a  
								        
										RIGHT JOIN SchServiceTypes b
											ON a.ServiceTypeId = b.Id
											and a.DistrictId = '{$DistrictId}' 
										LEFT JOIN Users c 
											ON a.Userid = c.Userid	
									ORDER BY ServiceCategoryId, RegistrantTypeId		
											";  	
								  
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Set District's Services Details Info */
            /*======================= */			
			function setSchDistrictServiceDetails(	$DistrictId,
													$ServiceId,
													$ServiceTypeId,
													$PrimaryVendorFL,
													$PayRate,
													$BillRate,
													$UserId ) 
			{
			
                        
				if(is_numeric($ServiceId))  { 	

                        $query =" UPDATE SchDistrictServiceDetails
									SET PrimaryVendorFL =  '{$PrimaryVendorFL}', 
										PayRate =  '{$PayRate}',
										BillRate =  '{$BillRate}',
										UserId =  '{$UserId}',
										TransDate = NOW()
									WHERE Id = '{$ServiceId}'";  	
  				} else {

  						$query =" INSERT INTO SchDistrictServiceDetails
										(
										DistrictId,
										ServiceTypeId,
										PrimaryVendorFL,
										PayRate,
										BillRate,
										UserId,
										TransDate )
										VALUES
										( 
										 '{$DistrictId}',
										 '{$ServiceTypeId}',
										 '{$PrimaryVendorFL}',
										 '{$PayRate}',
										 '{$BillRate}',
										 '{$UserId}',
										 NOW()
										 )

  								";	

  				}
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	


			//============================================
			// Get Payroll Open Transactions Info
            //============================================			
			function getPayrollOpenTransactions() 	{
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,
										SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
										g.ExtId as OverrideBillingCode,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
										DATE_FORMAT( ServiceDate, '%m%d%Y' ) as ServiceDateUpload,
										DATE_FORMAT( StartTime, '%h%i%p' ) as StartTimeUpload, 
										DATE_FORMAT( EndTime, '%h%i%p' ) as EndTimeUpload,
										
										ABS(TotalHours) as TotalHours,
										WeekDay, 
										RegistrantId, 
										b.ExtId as EmplId,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName ,  
										b.LastName,
										b.FirstName,
										
										CASE NextDayPay 
											WHEN '1' THEN 'Next Day Pay'
											ELSE ''
										END AS NextDayPayFL, 
										RegistrantTypeDesc, 
										ClientName,
										ClientUnitId,
										UnitName,
										ServiceTypeDesc,

										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											Clients d,
											ServiceTypes g 							
										  	
											WHERE	ScheduleStatusId = 8
												AND a.PayrollBatchNumber = ''
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.ClientId = d.Id
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND a.ServiceTypeId = g.Id
												AND c.ExtId != ''
										ORDER BY b.LastName, b.FirstName, ServiceDate, StartTime 	
											";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}			

			//============================================
			// Get Payroll Upload Batch Headers Info
            //============================================			
			function getPayrollUploadBatchHeaders() 	{
				 
                       $query ="SELECT  a.Id AS PayrollBatchNumber, 
										DATE_FORMAT( BatchDate, '%m-%d-%Y' ) AS BatchDate, 
										BatchCount,
									    CONCAT( trim( b.FirstName) , ' ', trim( b.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	PayrollBatchHeader a, 
											Users b
									WHERE   a.UserId = b.UserId		
									AND 	a.Id != '0'
									AND EXISTS (SELECT  1 FROM WeeklyServices b 
									                WHERE a.Id = b.PayrollBatchNumber
									            )
									ORDER BY PayrollBatchNumber DESC  
									";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}			


			//============================================
			// Get Payroll Upload Batch Details Info
            //============================================			
			function getPayrollUploadBatchDetails($PayrollBatchNumber) 	{
				 
                   $query ="SELECT  a.Id AS ScheduleId, 
									DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
									StartTime as StartTimeNum,
									EndTime as EndTimeNum,
									DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
									DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
									DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
									TotalHours, 
									WeekDay, 
									RegistrantId, 
									CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName,  
									RegistrantTypeDesc, 
									ClientName,
									ClientUnitId,
									UnitName,
									ServiceTypeDesc,
									CASE NextDayPay 
											WHEN '1' THEN 'Next Day Pay'
											ELSE ''
										END AS NextDayPayFL, 
									CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
									DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											Clients d,
											ServiceTypes g 							
										  	
											WHERE	PayrollBatchNumber = '{$PayrollBatchNumber}'
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.ClientId = d.Id
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND a.ServiceTypeId = g.Id
										ORDER BY b.LastName, b.FirstName, ServiceDate, StartTime  
											";
				 	 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}			

			// Get School (SCH) Side Navigation Data
            //==============================================			
			function getSchSchoolSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchSchoolSideNavigation 
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get (SCH) Schools Listing
            //======================= */			
			function getSchSchools ($DistrictId) {


				if(!is_numeric($DistrictId)) {

                        $query = "SELECT 	a.Id as id, 
											a.Id, 
											CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId
											 
								FROM        SchSchools a,
											SchDistricts b
								WHERE       a.DistrictId = b.Id	
							    
							    ORDER BY    TRIM(SchoolName)  ";

				} else {

                        $query = "SELECT 	a.Id as id, 
											a.Id, 
											CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId 
											 
								FROM        SchSchools a,
											SchDistricts b
								WHERE       b.Id = '{$DistrictId}'
								AND         a.DistrictId = b.Id	
							    
							    ORDER BY    TRIM(SchoolName)  ";

				}

                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}

			/* Get Selected School's General Info */
            /*======================= */			
			function getSelSchSchool($SchoolId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id, 
										a.ExtId,
										a.SearchId,
										a.StatusId,
										SchoolName,
										CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
										SchoolTypeId,
										SchoolTypeDesc,
										DistrictId,
										a.StreetAddress1,
										a.StreetAddress2,
										a.City,
										a.State,
										a.ZipCode,
										a.LiaisonFirstName,
										a.LiaisonLastName,
										a.OfficePhone,
										a.MobilePhone,
										a.Fax,
										a.Email,
										a.Comments,
										a.UserId,
										a.TransDate

									FROM 	SchSchools a, 
											SchSchoolTypes b,
											SchDistricts c
										where a.Id = '{$SchoolId}' 
										AND a.SchoolTypeId = b.Id
										AND a.DistrictId = c.Id "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Set Selected School's General Info */
            /*======================= */			
			function setSelSchSchool(  	$Id,
										$ExtId,
										$SearchId,
										$DistrictId,
										$StatusId,
										$SchoolName,
										$SchoolTypeId,
										$StreetAddress1,
										$City,
										$State,
										$ZipCode,
										$LiaisonFirstName,
										$LiaisonLastName,
										$OfficePhone,
										$MobilePhone,
										$Fax,
										$Email,
										$Comments,
										$UserId )  
			{
	

				$SchoolName = $this->connection->escapeSimple ($SchoolName);	
				$StreetAddress1 = $this->connection->escapeSimple ($StreetAddress1);	
				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchSchools
								set ExtId =  '{$ExtId}', 
								DistrictId =  '{$DistrictId}',
								StatusId =  '{$StatusId}',
								SchoolName =  '{$SchoolName}',
								SchoolTypeId =  '{$SchoolTypeId}',
								StreetAddress1	 =  '{$StreetAddress1}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								LiaisonFirstName =  '{$LiaisonFirstName}',
								LiaisonLastName	 =  '{$LiaisonLastName}',
								OfficePhone	 =  '{$OfficePhone}',
								MobilePhone	 =  '{$MobilePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								Email	 =  '{$Email}',
								Comments =  '{$Comments}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchSchools 
								(ExtId, 
								SearchId,
								DistrictId,
								StatusId,
								SchoolName,
								SchoolTypeId,
								StreetAddress1,
								City,
								State,
								ZipCode,
								LiaisonFirstName,
								LiaisonLastName,
								OfficePhone,
								MobilePhone,
								Fax,
								Email,
								Comments,
								UserId,
								TransDate )
				values 	('{$ExtId}',  
						'{$SearchId}',
						'{$DistrictId}',
						'{$StatusId}',
						'{$SchoolName}',
						'{$SchoolTypeId}',  
						'{$StreetAddress1}',  
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$LiaisonFirstName}',
						'{$LiaisonLastName}',
						'{$OfficePhone}', 
						'{$MobilePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$Comments}',
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result; 
						return $query; 
						
			}				

			/* Get (SCH )School Types Listing
            //=======================	*/		
			function getSchSchoolTypes() {
			
                        $query = "SELECT Id as id, 
										Id as SchoolTypeId,
										SchoolTypeDesc 
										FROM SchSchoolTypes 
								Order by Id   ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	


			// Get (SCH) School Messages
            //=========================================			
		 	function getSchSchoolMessages($SchoolId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM SchSchoolMessages a, Users b  
								WHERE SchoolId = '{$SchoolId}'
								AND a.UserId = b.UserId 
								Order By  a.TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			/* Set (SCH) School Message */
            /*======================= */			
			function setSchSchoolMessages  ($SchoolId,
											$HighPriority,
											$Msg,
											$UserId )  
			{
				
                       $query ="Insert into SchSchoolMessages 
								(SchoolId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$SchoolId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	

			// Get (SCH) School Assignment Headers
            //=========================================			
		 	function getSchSchoolAssignmentHeaders($SchoolId) {
			
                        $query ="SELECT a.Id as Id ,
								a.Id,
								a.SchoolId,
								a.StatusId,
								ServiceTypeId as OrigServiceTypeId,
								ServiceTypeId,
								ServiceTypeDesc,
								RegistrantTypeId,
                                RegistrantTypeDesc,
								AssignmentTypeId,
								CASE AssignmentTypeId 
									WHEN '1' THEN 'Long Term'
										ELSE 'Per Diem'
								END AS AssignmentTypeDesc,
								ConfirmationNumber,
								DATE_FORMAT( StartDate, '%m-%d-%Y' ) AS StartDate, 
								DATE_FORMAT( EndDate, '%m-%d-%Y' ) AS EndDate, 
								/*==*/

								(SELECT  CASE count(*) 
								WHEN 0 THEN ''
								ELSE group_concat( WeekDayDesc SEPARATOR ',' )
								END  
								FROM SchSchoolAssignmentSelDays f
								where a.Id = f.AssignmentId) as SelectedDaysDesc,

								(SELECT  CASE count(*) 
								WHEN 0 THEN ''
								ELSE group_concat( WeekDayId SEPARATOR ',' )
								END  
								FROM SchSchoolAssignmentSelDays f
								where a.Id = f.AssignmentId) as SelectedDaysId,

								/*==*/

								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	SchSchoolAssignmentHeader a, 
									Users b, 
									SchServiceTypes d,
									RegistrantTypes f  
								WHERE SchoolId = '{$SchoolId}'
								AND a.UserId = b.UserId
								AND d.ServiceCategoryId = 0
								AND a.ServiceTypeId = d.Id
								AND d.RegistrantTypeId  = f.Id 
								ORDER BY  a.TransDate Desc ";
 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			// Get School (SCH) Registrant Types Listing
            //================================			
			function getSchSchoolRegistrantTypes() {
			
                        $query = "SELECT Id as id, 	
										 Id as RegistrantTypeId,
									     RegistrantGroupId ,
                                         RegistrantTypeDesc  										 
									FROM RegistrantTypes
									WHERE RegistrantGroupId = '7'
									order by RegistrantTypeDesc";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			// Get School (SCH) Service Types Listing
            //================================			
			function getSchSchoolServiceTypes($ServiceCategoryId) {
			
                        $query = "SELECT 	a.Id as id,
									        a.Id as ServiceTypeId,
									        a.RegistrantTypeId,
									       CONCAT(ServiceTypeDesc, ' (', RegistrantTypeDesc, ') ') as ServiceTypeDesc
									FROM SchServiceTypes a, RegistrantTypes b
									WHERE ServiceCategoryId = {$ServiceCategoryId} 
									AND   b.Id  = a.RegistrantTypeId
									ORDER BY a.RegistrantTypeId ";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			
 

 			/* Set (SCH) School Assignment Header */
            /*====================================== */			
			function setSchSchoolAssignmentHeader(  $SchoolId,
													$StatusId,
													$AssignmentId,
													$AssignmentTypeId,
													$ConfirmationNumber,
													$StartDate,
													$EndDate,
													$ServiceTypeId,
													$UserId )  
			{
	
				
	            	$query = "Call  proc_setSchSchoolAssignmentHeader (	'{$SchoolId}',
    																	'{$StatusId}',
    																	'{$AssignmentId}',
    																	'{$AssignmentTypeId}',
    																	'{$ConfirmationNumber}',
    																	'{$StartDate}',
    																	'{$EndDate}',
    																	'{$ServiceTypeId}',
    																	'{$UserId}')  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result; 
						return $query; 

						
			}				

			// Get (SCH) School Assignment Details 
            //=========================================			
		 	

		 	function getSchSchoolAssignmentDetails($AssignmentId) {
			
                        $query ="SELECT a.Id,
                        				a.Id AS id,
                        				b.WeekDayId,
                        				b.WeekDay,
                        				CASE   
											WHEN a.StartTime THEN a.StartTime
											ELSE '07:00:00'
										END AS StartTime,

                        				CASE  
											WHEN a.EndTime THEN a.EndTime 
											ELSE '15:00:00'
										END AS EndTime,


                        				CASE   
											WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )  
										END AS StartTimeFrm,
										
                        				CASE   
											WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
										END AS EndTimeFrm,



                        				CASE   
											WHEN a.TotalHours THEN a.TotalHours 
											ELSE '8.00'
										END AS TotalHours,
										a.RegistrantId,
										COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' )
															FROM 	Registrants c, RegistrantTypes f
													WHERE a.RegistrantId = c.Id
													AND   c.TypeId = f.Id ),'Not Selected') as RegistrantName,
								COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ),'') AS UserName,
								COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                        FROM  SchSchoolAssignmentDetails a

						RIGHT JOIN DaysOfWeek b
						ON a.WeekDayId = b.WeekDayId
						AND a.AssignmentId = '{$AssignmentId}'


						LEFT JOIN Users c 
						ON a.Userid = c.Userid	

					     ";

						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;

				}		
		
			// Get (SCH) Schools Approved Registrants
            //=========================================			
		 	function getSchSchoolApprovedRegistrants($ServiceTypeId) {
			
                        $query ="SELECT a.RegistrantId as id,
                        				a.RegistrantId,
							       		CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName  
							 
							 FROM 	ClientApprovedRegistrantsNonRest a,
							        Registrants c,
							        RegistrantTypes f,
							        Clients b,
							        SchServiceTypes d
							WHERE a.ClientId = b.Id  
							AND   a.RegistrantId = c.Id 
							AND   b.SchoolFL = '1'
							AND   c.TypeId = f.Id 
							AND   c.TypeId = d.RegistrantTypeId 
							AND   d.Id = '{$ServiceTypeId}' 
							ORDER BY c.LastName, c.FirstName "; 
							
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				


			/* Set School (SCH) Assignment Detail  */
            /*========================================== */			
			function setSchSchoolAssignmentDetail(  $AssignmentId,
													$AssignmentDetailId,	
													$WeekDayId,
													$StartTime,
													$EndTime,
													$TotalHours,
													$RegistrantId,
													$UserId )  
			{
	
				
				if(is_numeric($AssignmentDetailId) ) { 	
			
                        $query ="UPDATE SchSchoolAssignmentDetails
								SET AssignmentId =  '{$AssignmentId}', 
								StartTime =  '{$StartTime}',
								EndTime =  '{$EndTime}',
								TotalHours =  '{$TotalHours}',
								RegistrantId =  '{$RegistrantId}',

								UserId =  '{$UserId}',
								TransDate = NOW()
							WHERE Id = '{$AssignmentDetailId}' ";
                } else {
                       $query ="INSERT into SchSchoolAssignmentDetails 
								(AssignmentId, 
								WeekDayId,
								StartTime,
								EndTime,
								TotalHours,
								RegistrantId,
								UserId,
								TransDate )
				VALUES 	('{$AssignmentId}',  
						'{$WeekDayId}',
						'{$StartTime}',
						'{$EndTime}',
						'{$TotalHours}',
						'{$RegistrantId}',  
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						
			}				


			/* Set School (SCH) Assignment Detail - ALL */
            /*========================================== */			
			function setSchSchoolAssignmentDetailAll(  $AssignmentId,
														$StartTime,
														$EndTime,
														$TotalHours,
														$RegistrantId,
														$UserId )  
			{




				$query ="DELETE FROM  SchSchoolAssignmentDetails
							Where AssignmentId = '{$AssignmentId}'";
				 

				$result = $this->connection->query($query);
                 if (DB::isError($result)){
                    die("Could not query the database:<br />$query ".DB::errorMessage($result));
						
                }				

               
                sleep(1);


				$query ="INSERT into SchSchoolAssignmentDetails 
												(AssignmentId, 
												WeekDayId,
												StartTime,
												EndTime,
												TotalHours,
												RegistrantId,
												UserId,
												TransDate )
					SELECT  '{$AssignmentId}',
						    WeekDayId,
							'{$StartTime}',
							'{$EndTime}',
							'{$TotalHours}',
							'{$RegistrantId}',  
							'{$UserId}',
							NOW()  						       	
					FROM     DaysOfWeek	";			

                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
				 


			}

 			/* Set (SCH) School Weekly Scheudle */
            /*====================================== */			
			function getSchSchoolWklySchedules( $SchoolId,
												$PayrollWeek )  
			{
	
				
	            	$query = "Call  proc_getSchSchoolWeeeklySchedules (	'{$SchoolId}',
     																	'{$PayrollWeek}')  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 

						
			}				

			// Set (SCH) School Weekly Service(s) from Template
            //============================================			
			function setSchSchoolWklyServicesFromTemplate(	$PayrollWeek,
															$SchoolId,
															$ServiceTypeId,
															$ConfirmationNumber,
															$ServiceDate,
															$StartTime,
															$EndTime,
															$TotalHours,
															$WeekDay, 
															$UserId ) 
			{
					$query ="	INSERT INTO WeeklyServices
		                (	
									ClientId,
									PayrollWeek,
									SchoolId,
									ScheduleStatusId,
									ServiceTypeId,
									AssignmentTypeId,	
									ConfirmationNumber,
									ServiceDate,	 
									StartTime, 
									EndTime, 		
									TotalHours , 
									WeekDay ,
									UserId,
									TransDate 
								)	
													
							SELECT  a.Id,
									'{$PayrollWeek}',
									'{$SchoolId}',
									'0',
									'{$ServiceTypeId}',
									'0',
									'{$ConfirmationNumber}',
									'{$ServiceDate}',	 
									'{$StartTime}', 
									'{$EndTime}', 		
									'{$TotalHours}', 
									'{$WeekDay}',
									'{$UserId}',
									NOW()
							FROM  Clients a
							WHERE SchoolFL = '1' LIMIT 1 " ;	
 
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;  
				return $query;		
			}

			// Check Registrants Duplicate Assigmentd
            //=========================================			
		 	function getRegistrantDuplicateScheduleFL($RegistrantId, $ServiceDate,  $StartTime) {
			
                        $query ="SELECT count(*) as AlredyExistsFL   
									FROM WeeklyServices
									WHERE RegistrantId = '{$RegistrantId}'
									AND ServiceDate = '{$ServiceDate}'
									AND ('{$StartTime}' BETWEEN StartTime AND EndTime)
									 "; 
							
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
				return $result;
				//return $query;
			}				


 	        //===============================================
			// Set (SCH) Adjust Client Schedule 
            //===============================================			
			function setSchSchoolWklyScheduleAdjust (
								$ScheduleId,
								$StartTime,
								$EndTime,
								$TotalHours,
								$ScheduleStatusId,
								$ConfirmationNumber,
								$RegistrantId,
								$UserId) 
			{ 
                        $query = "UPDATE WeeklyServices 
						            SET StartTime = '{$StartTime}', 
                                        EndTime = '{$EndTime}', 
										TotalHours = '{$TotalHours}', 
										ScheduleStatusId = '{$ScheduleStatusId}',
										ConfirmationNumber = '{$ConfirmationNumber}',
										RegistrantId = '{$RegistrantId}',
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									WHERE 	Id = '{$ScheduleId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

			// Get School (SCH) Side Navigation Data
            //==============================================			
			function getSchStudentSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchStudentSideNavigation 
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Students (SCH) Listing
            //======================= */			
			function getSchStudents($Statuses) {
                        $query = "SELECT Id as id,  
										/*CONCAT( trim( LastName) , ', ', trim(FirstName) ) as StudentName,*/
										CONCAT( trim( LastName) , ', ', trim(FirstName) ,' (', ExtId,')' ) as StudentName,
										SearchId
							FROM SchStudents
							WHERE StatusID in {$Statuses}
							order by LastName, FirstName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Selected (SCH) Student's General Info */
            /*======================= */			
			function getSelSchStudent($StudentId) {
			
                        $query ="SELECT  Id as id, 
					                    Id,
										ExtId,
										SearchId,
										COALESCE(SchoolId,'') as SchoolId,
										StatusId,
										DateOfBirth,
										FirstName,
										LastName,
										CONCAT( trim( LastName) , ', ', trim(FirstName)) as StudentName,
										MiddleInitial,
										StreetAddress1,
										StreetAddress2,
										City ,
										State ,
										ZipCode ,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
									/*	GuardianName, */
										GuardianFirstName,
										GuardianLastName,
										COALESCE(GuardianPhone,'') as GuardianPhone,
										GuardianEmail,
										MedicalNeeds,
										Comments,
										ReportGroupId,
										UserId
										
									FROM SchStudents
								  where Id = '{$StudentId}' "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Update Selected (SCJ) Student's Info 
            //======================= */			
			function setSelSchStudent( 	$Id,
										$ExtId,
										$SearchId,
										$StatusId,
										$SchoolId,
										$DateOfBirth,
										$FirstName,
										$LastName,
										$MiddleInitial,
										$StreetAddress1,
										$StreetAddress2,
										$City,
										$State,
										$ZipCode,
										$MobilePhone,
										$HomePhone,
										$GuardianFirstName,
										$GuardianLastName,
										$GuardianPhone,
										$GuardianEmail,
										$MedicalNeeds,
										$Comments,
										$ReportGroupId,
										$UserId	)			
				{
                if(is_numeric($Id) ) { 	
			
                        $query ="Update SchStudents 
								set ExtId =  '{$ExtId}', 
								SearchId =  '{$SearchId}',
								StatusId =  '{$StatusId}',
								SchoolId =  '{$SchoolId}',
								DateOfBirth =  '{$DateOfBirth}',
								FirstName =  '{$FirstName}',
								LastName =  '{$LastName}',
								MiddleInitial =  '{$MiddleInitial}',
								StreetAddress1 =  '{$StreetAddress1}',
								StreetAddress2 =  '{$StreetAddress2}',
								City =  '{$City}',
								State =  '{$State}',
								ZipCode =  '{$ZipCode}',
								MobilePhone =  '{$MobilePhone}',
								HomePhone =  '{$HomePhone}',
								
								GuardianFirstName =  '{$GuardianFirstName}',
								GuardianLastName =  '{$GuardianLastName}',

								GuardianPhone =  '{$GuardianPhone}',
								GuardianEmail =  '{$GuardianEmail}',
								MedicalNeeds =  '{$MedicalNeeds}',
								Comments =  '{$Comments}',
								ReportGroupId =  '{$ReportGroupId}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchStudents  
								(ExtId, 
								SearchId,
								StatusId,
								SchoolId,
								DateOfBirth,
								FirstName,
								LastName,
								MiddleInitial,
								StreetAddress1,
								StreetAddress2,
								City,
								State,
								ZipCode,
								MobilePhone,
								HomePhone,
								GuardianFirstName,
								GuardianLastName,
								GuardianPhone,
								GuardianEmail,
								MedicalNeeds,
								Comments,
								ReportGroupId,
								UserId,
								TransDate )
				values 	('{$ExtId}',  
						'{$SearchId}',	
						'{$StatusId}',
						'{$SchoolId}',
						'{$DateOfBirth}',  
						'{$FirstName}',  
						'{$LastName}',
						'{$MiddleInitial}',
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}',
						'{$State}',
						'{$ZipCode}',
						'{$MobilePhone}',
						'{$HomePhone}',
						'{$GuardianFirstName}',
						'{$GuardianLastName}',						
						'{$GuardianPhone}',
						'{$GuardianEmail}',
						'{$MedicalNeeds}',
						'{$Comments}',
						'{$ReportGroupId}',
						'{$UserId}',
						NOW()  ) ";    }
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}					

			// Get (SCH) Student Messages
            //=========================================			
		 	function getSchStudentMessages($StudentId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM SchStudentMessages a, Users b  
								WHERE StudentId = '{$StudentId}'
								AND a.UserId = b.UserId 
								Order By  a.TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			/* Set (SCH) Student Message */
            /*======================= */			
			function setSchStudentMessages  ($StudentId,
											$HighPriority,
											$Msg,
											$UserId )  
			{
				
                       $query ="Insert into SchStudentMessages 
								(StudentId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$StudentId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						//return $query;
						
			}	

			// Get (SCH) Student Assignment Headers
            //=========================================			
		 	function getSchStudentAssignmentHeaders($StudentId) {
			         
                        $query ="SELECT a.Id as Id ,
								a.Id,
								a.StudentId,
								a.StatusId,
								ServiceTypeId as OrigServiceTypeId,
								ServiceTypeId,
								ServiceTypeDesc,
								RegistrantTypeId,
                                RegistrantTypeDesc,
								AssignmentTypeId,
								CASE AssignmentTypeId 
									WHEN '1' THEN 'Long Term'
										ELSE 'Per Diem'
								END AS AssignmentTypeDesc,
								ConfirmationNumber,
								DATE_FORMAT( StartDate, '%m-%d-%Y' ) AS StartDate, 
								DATE_FORMAT( EndDate, '%m-%d-%Y' ) AS EndDate, 
								/*==*/

								(SELECT  CASE count(*) 
								WHEN 0 THEN ''
								ELSE group_concat( WeekDayDesc SEPARATOR ',' )
								END  
								FROM SchSchoolAssignmentSelDays f
								where a.Id = f.AssignmentId) as SelectedDaysDesc,

								(SELECT  CASE count(*) 
								WHEN 0 THEN ''
								ELSE group_concat( WeekDayId SEPARATOR ',' )
								END  
								FROM SchSchoolAssignmentSelDays f
								where a.Id = f.AssignmentId) as SelectedDaysId,

								/*==*/

								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	SchStudentAssignmentHeader a, 
									Users b, 
									SchServiceTypes d,
									RegistrantTypes f  
								WHERE StudentId = '{$StudentId}'
								AND a.UserId = b.UserId
								AND d.ServiceCategoryId = 1 /* Category: Student */
								AND a.ServiceTypeId = d.Id
								AND d.RegistrantTypeId  = f.Id 
								ORDER BY  a.TransDate Desc ";
 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				


			/* Set (SCH) Student Assignment Header */
            /*====================================== */			
			function setSchStudentAssignmentHeader( $StudentId,
													$StatusId,
													$AssignmentId,
													$AssignmentTypeId,
													$ConfirmationNumber,
													$StartDate,
													$EndDate,
													$ServiceTypeId,
													$UserId )  
			{
	
				
	            	$query = "Call  proc_setSchStudentAssignmentHeader ('{$StudentId}',
    																	'{$StatusId}',
    																	'{$AssignmentId}',
    																	'{$AssignmentTypeId}',
    																	'{$ConfirmationNumber}',
    																	'{$StartDate}',
    																	'{$EndDate}',
    																	'{$ServiceTypeId}',
    																	'{$UserId}')  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result; 
						return $query; 

						
			}				


			// Get (SCH) Student Assignment Details 
            //=========================================			
		 	

		 	function getSchStudentAssignmentDetails($AssignmentId) {
			
                        $query ="SELECT a.Id,
                        				a.Id AS id,
                        				b.WeekDayId,
                        				b.WeekDay,
                        				CASE   
											WHEN a.StartTime THEN a.StartTime
											ELSE '07:00:00'
										END AS StartTime,

                        				CASE  
											WHEN a.EndTime THEN a.EndTime 
											ELSE '15:00:00'
										END AS EndTime,


                        				CASE   
											WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )  
										END AS StartTimeFrm,
										
                        				CASE   
											WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
										END AS EndTimeFrm,



                        				CASE   
											WHEN a.TotalHours THEN a.TotalHours 
											ELSE '8.00'
										END AS TotalHours,
										a.RegistrantId,
										COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' )
															FROM 	Registrants c, RegistrantTypes f
													WHERE a.RegistrantId = c.Id
													AND   c.TypeId = f.Id ),'Not Selected') as RegistrantName,
								COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ),'') AS UserName,
								COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                        FROM  SchStudentAssignmentDetails a

						RIGHT JOIN DaysOfWeek b
						ON a.WeekDayId = b.WeekDayId
						AND a.AssignmentId = '{$AssignmentId}'


						LEFT JOIN Users c 
						ON a.Userid = c.Userid	

					     ";

						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;

				}		

			/* Set Student (SCH) Assignment Detail  */
            /*========================================== */			
			function setSchStudentAssignmentDetail( $AssignmentId,
													$AssignmentDetailId,	
													$WeekDayId,
													$StartTime,
													$EndTime,
													$TotalHours,
													$RegistrantId,
													$UserId )  
			{
	
				
				if(is_numeric($AssignmentDetailId) ) { 	
			
                        $query ="UPDATE SchStudentAssignmentDetails
								SET AssignmentId =  '{$AssignmentId}', 
								StartTime =  '{$StartTime}',
								EndTime =  '{$EndTime}',
								TotalHours =  '{$TotalHours}',
								RegistrantId =  '{$RegistrantId}',

								UserId =  '{$UserId}',
								TransDate = NOW()
							WHERE Id = '{$AssignmentDetailId}' ";
                } else {
                       $query ="INSERT into SchStudentAssignmentDetails 
								(AssignmentId, 
								WeekDayId,
								StartTime,
								EndTime,
								TotalHours,
								RegistrantId,
								UserId,
								TransDate )
				VALUES 	('{$AssignmentId}',  
						'{$WeekDayId}',
						'{$StartTime}',
						'{$EndTime}',
						'{$TotalHours}',
						'{$RegistrantId}',  
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						
			}				


			/* Set Student (SCH) Assignment Detail - ALL */
            /*========================================== */			
			function setSchStudentAssignmentDetailAll(  $AssignmentId,
														$StartTime,
														$EndTime,
														$TotalHours,
														$RegistrantId,
														$UserId )  
			{




				$query ="DELETE FROM  SchStudentAssignmentDetails
							Where AssignmentId = '{$AssignmentId}'";
				 

				$result = $this->connection->query($query);
                 if (DB::isError($result)){
                    die("Could not query the database:<br />$query ".DB::errorMessage($result));
						
                }				

               
                sleep(1);


				$query ="INSERT into SchStudentAssignmentDetails 
												(AssignmentId, 
												WeekDayId,
												StartTime,
												EndTime,
												TotalHours,
												RegistrantId,
												UserId,
												TransDate )
					SELECT  '{$AssignmentId}',
						    WeekDayId,
							'{$StartTime}',
							'{$EndTime}',
							'{$TotalHours}',
							'{$RegistrantId}',  
							'{$UserId}',
							NOW()  						       	
					FROM     DaysOfWeek	";			

                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
				 


			}

 			/* Set (SCH) Student Weekly Scheudle */
            /*====================================== */			
			function getSchStudentWklySchedules( $StudentId,
												 $PayrollWeek )  
			{
	
				
	            	$query = "Call  proc_getSchStudentWeeeklySchedules ('{$StudentId}',
     																	'{$PayrollWeek}')  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 

						
			}				


			// Set (SCH) Student Weekly Service(s) from Template
            //============================================			
			function setSchStudentWklyServicesFromTemplate(	$PayrollWeek,
															$StudentId,
															$ServiceTypeId,
															$ConfirmationNumber,
															$ServiceDate,
															$StartTime,
															$EndTime,
															$TotalHours,
															$WeekDay, 
															$UserId ) 
			{
					$query ="	INSERT INTO WeeklyServices
		                (	
									ClientId,
									PayrollWeek,
									StudentId,
									ScheduleStatusId,
									ServiceTypeId,
									AssignmentTypeId,	
									ConfirmationNumber,
									ServiceDate,	 
									StartTime, 
									EndTime, 		
									TotalHours , 
									WeekDay ,
									UserId,
									TransDate 
								)	
													
							SELECT  a.Id,
									'{$PayrollWeek}',
									'{$StudentId}',
									'0',
									'{$ServiceTypeId}',
									'0',
									'{$ConfirmationNumber}',
									'{$ServiceDate}',	 
									'{$StartTime}', 
									'{$EndTime}', 		
									'{$TotalHours}', 
									'{$WeekDay}',
									'{$UserId}',
									NOW()
							FROM  Clients a
							WHERE SchoolFL = '1' LIMIT 1 " ;	
 
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;  
				return $query;		
			}

			//====================================			
			// Get (SCH) Student Monthly Calendar
			//====================================			
			function getSchStudentMonthlyCalendar($StudentId, $StartDate) {
			
 
							
                    $query = "Call proc_getSchStudentMontlyCalendar ('{$StudentId}', '{$StartDate}')  "; 
                    $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                    if (DB::isError($result)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result));
                    }
		
					return $result;
						
			}

			//====================================			
			// Get (SCH) School Monthly Calendar
			//====================================			
			function getSchSchoolMonthlyCalendar($SchoolId, $StartDate) {
			
 
							
                    $query = "Call proc_getSchSchoolMontlyCalendar ('{$SchoolId}', '{$StartDate}')  "; 
                    $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                    if (DB::isError($result)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result));
                    }
		
					return $result;
						
			}

			// Get (SCH) Schoolboard Side Navigation Data
            //=======================			
			function getSchSchoolboardSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchSchoolboardNavigation 
					    WHERE Status = 1
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


			// Get (SCH) Student Unassigned Mandates Data
            //======================================			
			function getSchStudentUnassignedMandates() {
			
                        $query = " SELECT 	a.Id as MandateId,
												a.Id,
												StudentId, 
												b.SearchId as StudentSearchId,
												CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
												a.SchoolId, 
												
												CASE a.SchoolId 
												WHEN '0' THEN 'School Undefined'
												ELSE (SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ') 
															FROM    SchSchools c,
																	SchDistricts d
																	WHERE a.SchoolId = c.Id  
																	AND   c.DistrictId = d.Id	
															) 
												END AS SchoolName,

												CASE a.RegistrantId 
												WHEN '0' THEN 'Registrant Undefined'
												ELSE (SELECT CONCAT( trim( d.LastName ) , ', ',trim( d.FirstName))   
		                         					FROM  Registrants d  
													WHERE  a.RegistrantId = d.Id
		                         				 	
												) 
												END AS RegistrantName,



												ServiceTypeDesc,
												CONCAT( `SessionFrequency` , ' X ', `SessionLength` , ' X ', `SessionGrpSize` ) AS MandateDesc,
												Language,
												'Registrant Not Assigned ' AS ProblemDesc,
												CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
												a.UserId , 
												a.TransDate

										FROM 	SchStudentMandates a, 
												SchStudents b, 
												SchServiceTypes f,
												Users e
										WHERE a.StatusId =1
										AND a.StudentId = b.Id	
										AND a.ServiceTypeId = f.Id
										AND a.UserId = e.UserId
										AND ((a.RegistrantId = 0) || (a.SchoolId = 0))
										ORDER By b.LastName, b.FirstName
									";		
 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get (SCH) Student Mandates List Data
            //======================================			
			function getSchStudentMandatesList($StudentId) {
			
	                        $query = "SELECT 	a.Id as id,
												a.Id as MandateId,
												StudentId, 
												CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
												a.StatusId,
												CASE a.SchoolId 
													WHEN '0' THEN ''
												ELSE a.SchoolId
												END AS SchoolId,

												(SELECT SchoolName from SchSchools c
												   WHERE a.SchoolId = c.Id ) as SchoolName,  
												ServiceTypeDesc,
												ServiceTypeId,
												a.StatusId , 
												SECMandateStatus,
												DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
												DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,
												CASE a.RegistrantId 
													WHEN '0' THEN ''
												ELSE a.RegistrantId
												END AS RegistrantId,
												

												CallInDate,
												DOEAssignmentDate,
												PlaceOfService,

												SECMandateStatus,
												DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
												CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize ) AS MandateDesc,
												Language,
												CONCAT( trim( RegistrantExtFirstName) , ' ', trim(RegistrantExtLastName)) as RegistrantName,
												CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
												DOESchoolName,
												a.UserId , 
												a.TransDate
			
										FROM 	SchStudentMandates a, 
												SchStudents b, 
												SchServiceTypes f,
											Users e
											WHERE a.StudentId = '{$StudentId}'
											AND a.StudentId = b.Id	
											AND a.ServiceTypeId = f.Id
											AND a.UserId = e.UserId
										ORDER BY ServiceTypeId
 										";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get (SCH) Student Mandate Data
            //======================================			
			function getSchStudentMandate($MandateId) {
			
                        $query = "SELECT 	a.Id as id,
											a.Id , 
											StudentId , 
											a.StatusId , 
											SECMandateStatus,
											DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
											DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,
											RegistrantId , 
											CallInDate,
											DOEAssignmentDate,
											PlaceOfService,
											CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
											a.UserId , 
											a.TransDate
						FROM SchStudentMandates a,  
							 SchServiceTypes e,
							 Users f   
						WHERE a.Id = '{$MandateId}'
						AND a.ServiceTypeId = e.Id
						AND a.UserId = f.UserId
 										";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Set (SCH) Student Mandate Data
            //======================================			
			function setSchStudentMandate(	$MandateId,	
											$StatusId,
											$PlaceOfService,
											$StartDate,
											$EndDate,
											$SchoolId,
											$RegistrantId,
											$UserId ) {
			
                        $query = "UPDATE SchStudentMandates 
								SET StatusId = '{$StatusId}',
								    PlaceOfService = '{$PlaceOfService}', 
									StartDate = '{$StartDate}',
									EndDate = '{$EndDate}',
									SchoolId = '{$SchoolId}',
									RegistrantId = '{$RegistrantId}',
								    UserId = '{$UserId}',
									TransDate = now()
							WHERE 	Id = '{$MandateId}' 
 								";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


			// Set (SCH) Student School from Mandate  
            //======================================			
			function setSchStudentSchoolFromMandate($MandateId,	
													$SchoolId,
													$UserId ) 
			{
			
                        $query = "UPDATE SchStudents a, SchStudentMandates b 
								SET a.SchoolId = '{$SchoolId}',
								    a.UserId = '{$UserId}',
									a.TransDate = now()
							WHERE 	b.Id = '{$MandateId}'
							AND     a.Id = b.StudentId 
 								";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


			// Get (SCH) Registrant  Side Navigation Data
            //==============================================			
			function getSchRegistrantSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchRegistrantSideNavigation 
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get Registrant Web Navigation Data
            //=======================			
			function getSchRegistrantWebNavigation() {
			
                        $query = "SELECT *							
						FROM SchRegistrantWebNavigation 
					    WHERE Status = 1
						ORDER BY id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}



			// Get (SCH) Registrants  Listing
            //=======================			
			function getSchRegistrants($Statuses, $SearchId) {
			
                        $query = "SELECT a.Id as id,  
											a.TypeId,
											a.ExtId,
											a.SearchId,			
											CONCAT( trim( a.LastName) , ', ', trim(a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
										FROM Registrants a, RegistrantTypes b
										WHERE Typeid  = b.Id 
										AND StatusID in {$Statuses}
										AND SearchId like '{$SearchId}'
										
										AND EXISTS (SELECT 1 FROM Clients c, ClientApprovedRegistrants d
														WHERE a.Id = d.RegistrantId
														AND   d.ClientId = c.Id
														AND   c.SchoolFL = '1'	
											)
										ORDER BY LastName, FirstName

										";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;

			}

			// Get (SCH) Registrant Types Listing
            //=======================			
			function getSchRegistrantTypes() {
			
                        $query = "SELECT Id as id, 	
										 Id as RegistrantTypeId,
									     RegistrantGroupId ,
                                         RegistrantTypeDesc  										 
									FROM RegistrantTypes
									WHERE RegistrantStatusId = '1'
									AND RegistrantGroupId = '7'
									order by RegistrantTypeDesc";
									
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}			

			/* Get (SCH) Registrant's Date Sessions Data
            //===========================*/		
			function getSchRegistrantDateSessions($RegistrantId, $ServiceDate) {
			
                        $query = "SELECT 	Id as id,
											Id as SessionId,  
											a.Status,
											RegistrantId,
											ServiceDate,  	
											DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
											StartTime as StartTimeSort,
											DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
											EndTime as EndTimeSort,
											TotalHours,
											GroupSize,
											AttachedServicesNum,
											(SELECT COUNT(*) FROM SchSchoolWeeklyServices c
											  WHERE c.SessionId = a.Id 
											  AND c.SessionId != 0)as NumOfServAttached,
											CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
											TransDate
									FROM SchRegistrantDateSessions a, Users b
										WHERE ServiceDate = '{$ServiceDate}' 
										AND RegistrantId  = '{$RegistrantId}' 
										AND a.UserId = b.UserId 
										AND a.Status < 2
									Order by StartTimeSort  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get (SCH) Registrant's Date Sessions Data
            //===========================*/		
	/*
			function getSchRegistrantUnVerifiedWeeklySessions($RegistrantId, $PayrollWeek, $ServiceDate) {
			
                        $query = "SELECT  	a.Id as MandateId,
											a.ServiceTypeId, 
										   	c.ServiceTypeDesc,	
									       	a.SessionLength,
									       	a.SessionGrpSize,
									       	a.SessionFrequency,
									       	a.StudentId,
									       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName

									FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
									WHERE a.RegistrantId = '{$RegistrantId}' 
									AND a.StatusId = '1'
									AND a.StudentId = b.Id
									AND '{$ServiceDate}' BETWEEN a.StartDate AND a.EndDate
									AND a.ServiceTypeId = c.Id
									AND  (SELECT COUNT(*) FROM WeeklyServices d
									       	  	WHERE d.MandateId = a.Id 
									       	  	AND d.ScheduleStatusId > 5
									       	  	AND d.PayrollWeek = '{$PayrollWeek}'    
									       	) < a.SessionFrequency	
									AND NOT EXISTS (SELECT 1 FROM WeeklyServices e
												WHERE e.MandateId = a.Id 
									       	  	AND e.ScheduleStatusId > 5
									       	  	AND e.ServiceDate = '{$ServiceDate}' 
									       	  	AND a.ServiceTypeId = e.ServiceTypeId
										)	 ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
	*/	
			/* Get (SCH) Registrant's Date Sessions Data
            //===========================*/		
			function getSchRegistrantVerifiedDailySessions($RegistrantId, $ServiceDate) {
			
                        $query = "SELECT  	DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 	
									 		DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
											FORMAT((a.TotalHours * 60), 0) as TotalHours,
											group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
											group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
											
											SessionGrpSize,
											a.RegistrantId,
											COALESCE((SELECT StoredName FROM SchStudentsSessionNotes h
											WHERE a.Id = h.ScheduleId),'') as StoredDocName,

											COALESCE((SELECT DocumentName FROM SchStudentsSessionNotes h
											WHERE a.Id = h.ScheduleId),'') as DocumentName,
											-- a.ScheduleStatusId,
											CASE SessionDeliveryModeId 
												WHEN 'V' THEN 'Audio & Video'
												WHEN 'A' THEN 'Audio Only'
												WHEN 'I' THEN 'In-Person'
											ELSE 'Undefined'
											END AS SessionDeliveryModeDesc 
											

									FROM 	WeeklyServices a, SchStudents b
												WHERE a.RegistrantId = '{$RegistrantId}' 
												AND   a.ServiceDate =  '{$ServiceDate}'  
												AND   a.ScheduleStatusId > 5
												AND   b.Id = a.StudentId
									GROUP BY a.StartTime, a.EndTime, a.TotalHours			
										 ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}


           // Set (SCH) Registrant Verified Mandated Sessions 
            //=======================			
			function setSchRegistrantVerifiedMandatedSessions( 
									$ServiceDate,
									$PayrollWeek,
									$WeekDay,
									$MandateId,
									$StartTime,
									$EndTime,
									$TotalHours,
									$GroupSize,
									$SessionDeliveryModeId,
									$UserId ) 
					{
				
							$query ="INSERT INTO WeeklyServices
							                  (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												RegistrantId, 
												SchoolId,
												StudentId,
												ServiceTypeId,
												MandateId,
												SessionGrpSize,
												SessionDeliveryModeId,
												UserId,
												TransDate )	
												
						    SELECT
										'{$PayrollWeek}',
										a.Id,
										b.Id,
										'7',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
                                        c.RegistrantId, 
										c.SchoolId,
										c.StudentId,
									  	c.ServiceTypeId,
									  	'{$MandateId}',
									 	'{$GroupSize}',
 									 	'{$SessionDeliveryModeId}',
 									 	'{$UserId}',
                                         now()	 
                            FROM Clients a, ClientUnits b, SchStudentMandates c             
                            WHERE a.SchoolFL = '1'
                            AND   b.ClientId = a.Id 
                            AND   c.Id =  '{$MandateId}' LIMIT 1  ";
                                        
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;  
				return $query;			
			}				

			/* Set (SCH) Registrant Weekly Scheudle */
            /*====================================== */			
			function getSchRegistrantWklySchedules( $RegistrantId,
													$PayrollWeek )  
			{
	
				
	            	$query = "Call  proc_getSchRegistrantWeeeklySchedules (	'{$RegistrantId}',
     																		'{$PayrollWeek}')  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 

						
			}				


			/* Get (SCH) School Alert Messages
            ======================= */			
		 	function getSchSchoolAlertMessages() {
			
                        $query ="SELECT Id as AlertId ,
								Msg,
								StatusId,
								0 as  Seq,
								'Orig' as AlertType,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b  
								WHERE a.UserId = b.UserId 
								AND a.SchoolFL = '1'
								
						UNION								
								SELECT c.AlertId ,
								c.Msg,
								a.StatusId,
								c.Id as Seq,
								'Resp' as AlertType,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( c.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b, AlertMessageResponses c  
								WHERE a.Id = c.AlertId   
								AND c.UserId = b.UserId 
								AND a.SchoolFL = '1'
								Order By    StatusId, SortTransDate Desc,  AlertId Desc, Seq "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $query;
						return $result;
			}	

			/* Get (SCH) Schoolboard Reports Data
            //=====================================*/		
			function getSchSchoolboardReports() {
			
                        $query = "SELECT Id as id,
										 ReportDesc,
										 ReportLink			
						FROM SchSchoolBoardReports 
					order by Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

 			/* Set (SCH) School Date Range Scheudle */
            /*====================================== */			
			function getSchSchoolDateRangeSchedules($SchoolId,
													$FromDate,
													$ToDate
													)  
			{
	
				
	            	$query = "Call  proc_getSchSchoolDateRangeSchedules ( 	'{$SchoolId}',
     																		'{$FromDate}',
     																		'{$ToDate}'
     																	)  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 

												
			}				


 			/* Set (SCH) Student Date Range Scheudle */
            /*====================================== */			
			function getSchStudentDateRangeSchedules($StudentId,
												  	 $FromDate,
													 $ToDate
													)  
			{
	
				
	            	$query = "Call  proc_getSchStudentDateRangeSchedules ( 	'{$StudentId}',
     																		'{$FromDate}',
     																		'{$ToDate}'
     																	)  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 

												
			}				

	 			/* Set (SCH) Registrant Date Range Scheudle */
            /*====================================== */			
			function getSchRegistrantDateRangeSchedules($RegistrantId,
												  	 	$FromDate,
													 	$ToDate
													   )  
			{
	
				
	            	$query = "Call  proc_getSchRegistrantDateRangeSchedules ( 	'{$RegistrantId}',
     																			'{$FromDate}',
     																			'{$ToDate}'
     																		)  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 

												
			}				

			//====================================			
			// Get (SCH) Registrant Monthly Calendar
			//====================================			
			function getSchRegistrantMonthlyCalendar($RegistrantId, $StartDate) {
			
 
							
                    $query = "Call proc_getSchRegistrantMontlyCalendar ('{$RegistrantId}', '{$StartDate}')  "; 
                    $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                    if (DB::isError($result)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result));
                    }
		
					return $result;
						
			}


 			/* Get (SCH) Schoolboard (Schools) Date Range Scheudle */
            /*====================================== */			
			function getSchSchoolboardSchoolsDateRangeSchedules($FromDate,
																$ToDate
																)  
			{
	
				
	            	$query = "Call  proc_getSchSchoolboardSchoolsDateRangeSchedules ('{$FromDate}', 	 
			     																	 '{$ToDate}'
     																				)  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						

												
			}				

			/* Get (SCH) Schoolboard (Students) Date Range Scheudle */
            /*====================================== */			
			function getSchSchoolboardStudentsDateRangeSchedules($FromDate,
																$ToDate
																)  
			{
	
				
	            	$query = "Call  proc_getSchSchoolboardStudentsDateRangeSchedules ('{$FromDate}', 	 
			     																	 '{$ToDate}'
     																				)  "; 
                    
                       $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						

												
			}				


			/* Get (SCH) Registrant's Assigned Mandates Data
            //===========================================================================*/		
			function getSchRegistrantAssignedMandates($RegistrantId, $FromDate, $ToDate) {
			
                        $query = "SELECT  	a.Id as MandateId,
											a.ServiceTypeId, 
										   	c.ServiceTypeDesc,	
									       	a.SessionLength,
									       	a.SessionGrpSize,
									       	a.SessionFrequency,
									       	a.StudentId,
									       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
									       	(SELECT COUNT(*) FROM WeeklyServices d 
									       	  	WHERE d.MandateId = a.Id 
									       	  	AND d.ScheduleStatusId > 5
									       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

											) as NumbeOfSessions

									FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
									WHERE a.RegistrantId = '{$RegistrantId}'  
									AND a.StatusId = '1'
									AND b.StatusId = '1'
									AND a.StudentId = b.Id
									AND a.ServiceTypeId = c.Id
									
									AND EXISTS ( SELECT 1 FROM WeeklyServices d 
									       	  	WHERE d.MandateId = a.Id 
									       	  	AND d.ScheduleStatusId > 5
									       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

									)
                                    

                                    Order By b.LastName, b.FirstName ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Set (SCH) Registant Client (NYC DOE) Approval (New Registrants ONLY)  
            //====================================================================			
			function setSchRegistrantClientApproval($SearchId,	
													$TypeId,
													$UserId ) 
			{
			
                        $query = " INSERT INTO ClientApprovedRegistrants ( 
										SearchId,
										Status, 
										ClientId, 
										ClientUnitId,
										ServiceTypeId,
										RegistrantId, 
										UserId,		
										TransDate )
							


							SELECT RAND(),
									'1',
									a.Id,	
									(SELECT b.Id 
										FROM ClientUnits b
										WHERE b.ClientId = a.Id
										LIMIT 1
									),	

									(SELECT c.DefaultServiceTypeId
										FROM RegistrantTypes c
										WHERE c.Id = '{$TypeId}'
									),	

									f.Id,
									'{$UserId}',

									 NOW() 

									FROM Clients a, Registrants f
									WHERE SchoolFL = '1'  	
									AND f.SearchId = '{$SearchId}'								 
 
 								";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}



			/* Get (SCH) Registrant's Assigned Students Data
            //===========================================================================*/		
			function getSchRegistrantAssignedStudents($RegistrantId) {
			
                        $query = "SELECT  	a.Id as MandateId,
											a.ServiceTypeId, 
										   	c.ServiceTypeDesc,	
									    /*   	
									       	a.SessionLength,
									       	a.SessionGrpSize,
									       	a.SessionFrequency,
										*/
											CONCAT( a.SessionFrequency , ' X ', a.SessionLength , ' X ', a.SessionGrpSize ) AS MandateDesc,
									       	
									       	a.StudentId,
									       	b.ExtId,
									       	
									       	COALESCE(DATE_FORMAT( (COALESCE((SELECT MIN(ServiceDate) FROM WeeklyServices f 
									       	    WHERE a.Id = f.MandateId 
									       	    AND   a.StudentId = f.StudentId
									       	    AND   f.ScheduleStatusId > 5 
									       	    AND   a.RegistrantId =  '{$RegistrantId}' 
									       	),'')), '%m-%d-%Y' ),'') as ServiceStartDate,
									       	/*
									       	COALESCE((SELECT MIN(ServiceDate) FROM WeeklyServices f 
									       	    WHERE a.Id = f.MandateId  
									       	    AND   a.RegistrantId =  '{$RegistrantId}' 
									       	),'') as ServiceStartDate,
									       	*/
									       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
											b.SearchId as StudentSearchId,
											a.SchoolId, 
											
											CASE a.SchoolId 
											WHEN '0' THEN 'School Undefined'
											ELSE (SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ') 
														FROM    SchSchools f,
																SchDistricts d
																WHERE a.SchoolId = f.Id  
																AND   f.DistrictId = d.Id	
														) 
											END AS SchoolName

									FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
									WHERE a.RegistrantId = '{$RegistrantId}'  
									AND a.StatusId = '1'
									AND b.StatusId = '1'
									AND a.StudentId = b.Id
									AND a.ServiceTypeId = c.Id
									

                                    Order By b.LastName, b.FirstName ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Set Change Schedule Status 
            //=======================			
			function setChangeScheduleStatus(
								$ScheduleId,
								$ScheduleStatusId,
								$UserId) 
			{ 
                        $query = "UPDATE WeeklyServices 
						            SET ScheduleStatusId = '{$ScheduleStatusId}', 
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									WHERE  Id = '{$ScheduleId}'";
						
						$result = $this->connection->query($query);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}

			/* Get (SCH) Registrant's Sesion Notes
            //===========================================================================*/		
			function getSchRegistrantSessionNotes($RegistrantId) {
			
                        $query = "SELECT  a.Id AS Id, 
                                a.Id as SessionNotesId, 
                                OriginalFileName as SessionNotesDesc, 
                            /*    OriginalFileName */
                                StoredName,
          						StoredNameExt,	
                                CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) as UserName,
                                a.TransDate

                FROM    SchRegistrantSessionNotes a, 
                        Users e
                WHERE   a.RegistrantId = '{$RegistrantId}'
                    AND a.UserId = e.UserId ";

                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
                        //return $query;	
								
			}

			/* Check Student's Duplicate Sessions
            //===========================================================================*/		
			function getSchStudentCheckDupSessions($MandateId, $ServiceDate, $StartTime, $EndTime) {
			
                        $query = "SELECT  count(*) as Dup_Count  FROM WeeklyServices a,  SchStudentMandates b 
										where b.Id = '{$MandateId}'
										and   a.ScheduleStatusId > 6
										and   a.StudentId = b.StudentId
										and   a.ServiceDate = '{$ServiceDate}'
										and   a.StartTime between '{$StartTime}' and SUBTIME('{$EndTime}', '1')
 									";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Check Mandates's Remaining Freq.
            //===========================================================================*/		

     		function getSchMandateRemainingFreq($MandateId, $PayrollWeek ) {
      
                        $query = "SELECT  a.SessionFrequency - (SELECT count (*) 
                                  FROM WeeklyServices b
                                  where a.Id = b.MandateId
                                  and b.ScheduleStatusId > 6
                                  and b.PayrollWeek = '{$PayrollWeek}'
                              ) as RemaininqFreq  
 
               FROM SchStudentMandates a
               WHERE a.Id = '{$MandateId}' ";
                                 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
      
            return $result;
            
      }


} // End

 			

?>