<?php
  //$command = 'pdftk ../hr/P6UywSAjfmJfVba.pdf ../hr/27mQbzzBYfjthh6.pdf ../hr/3hS7eEqCrqYg3Gt.pdf cat output ../hr/123.pdf';
  
	$data = $_GET['data'];

	$array =  (explode(",",$data));


	
	$date_time = new DateTime();
		
	$output_file = 'Combined_'.$date_time->getTimestamp();
  
	
	$command = 'pdftk ';
  
	foreach ($array as $pdf_name)
	{
		$command = $command.' '.$pdf_name; 
	} 
  
	$command = $command.' cat output ../hr/'.$output_file.'.pdf';

	/*
	echo '$command';
	echo '</br>';
	echo var_dump($command);
	*/
	
   	
   
  $result = shell_exec($command);
  header('Content-disposition: attachment; filename=123.pdf');
  header('Content-type: application/pdf');
  readfile('../hr/'.$output_file.'.pdf');
  
  
  
  
?>
