<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$RegistrantCredItemTransId = $_POST['RegistrantCredItemTransId'];
$HighPriority = $_POST['HighPriority'];
$Msg = $_POST['Msg'];
$UserId = $_POST['UserId'];


$result = $rcr_transaction->setRegistrantCredItemMsgs(	$RegistrantCredItemTransId,
														$Msg,
														$HighPriority,
														$UserId ); 

$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
