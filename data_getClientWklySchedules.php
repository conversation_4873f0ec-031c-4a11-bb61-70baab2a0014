<?php 

	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

	$ClientId = $_GET['ClientId'];
	$PayrollWeek = $_GET['PayrollWeek'];
	$ServiceDate = $_GET['ServiceDate'];
	$ClientUnitId = $_GET['ClientUnitId'];
	
	$ClientUnitId=json_decode($ClientUnitId,true);
	$InclClientUnitId =  implode(",",$ClientUnitId);



	$ServiceTypeId = $_GET['ServiceTypeId'];
 

	if (!$ServiceTypeId) {
		$ServiceTypeId = '%%';
	}
	 

	 

	$result = $rcr_transaction->getClientWklySchedules ($ClientId, 
														$PayrollWeek, 
														$ServiceDate,
														$InclClientUnitId,
														$ServiceTypeId	
														);
	  

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
 
?>
