
/*=========================================*/

DELIMITER $$

DROP TRIGGER IF EXISTS trig_AddNewClientTrigger  $$

CREATE TRIGGER trig_AddNewClientTrigger AFTER INSERT ON Clients

FOR EACH ROW BEGIN


	
	/*========================================*/
	/* Add Default Unit to a New Client */
	/*========================================*/
	
	Insert into ClientUnits 
	(
		ClientId,
		UnitName, 
		StreetAddress1, 
		StreetAddress2, 
		City, 
		State, 
		ZipCode, 
		OfficePhone, 
		Fax, 
		Email, 
		UserId,
		TransDate	
	)
	
	VALUES (
		NEW.Id,
		'Default Unit',
		NEW.StreetAddress1, 
		NEW.StreetAddress2, 
		NEW.City, 
		NEW.State, 
		NEW.ZipCode, 
		NEW.OfficePhone, 
		NEW.Fax, 
		NEW.Email, 
		NEW.UserId,
		NEW.TransDate
		
		
	);
	
	/*========================================*/
	/* Add Client Shifts  */
	/*========================================*/
	
	INSERT INTO ClientShifts 
	(
		ClientId, 
		ShiftId, 
		Status, 
		ShiftName, 
		StartTime, 
		EndTime, 
		LunchHour, 
		TotalHours, 
		UserId, 
		TransDate	)

   SELECT	NEW.Id,
			Id,
			'1',
			ShiftNameDefault,
			StartTime, 
			EndTime, 
			LunchHour, 
			TotalHours, 
		NEW.UserId,
		NEW.TransDate
	FROM DefaultShifts			
		
	;
	
	
END; $$

DELIMITER ;