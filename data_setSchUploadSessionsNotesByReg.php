<?php 



	require_once('DB.php');
	include('db_login.php');


	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


	$RegistrantId = $_POST['RegistrantId'];
	$FileExt = $_POST['FileExt'];
	$OrigFileName = $_POST['OrigFileName'];
	$StoredFileName = $_POST['StoredFileName'];
	$UserId = $_POST['UserId'];





	$UploadFileName =  '../hr/'.$StoredFileName.'.'.$FileExt;
	//$UploadFileName =  '../hr/Test.xlsx';


   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $UploadFileName);

		
	} else {



		//print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: File Name - ".$UploadFileName."}";
	    Return ; 

	}
 
 

	$query = "INSERT INTO SchRegistrantSessionNotes 
					(
						RegistrantId,
						OriginalFileName,
 						StoredName,
						UserId,
						TransDate )
					VALUES ('{$RegistrantId}',
							'{$OrigFileName}',  
							'{$UploadFileName}',
							'{$UserId}',
							NOW()  )  ";
							
	
	$result = $connection->query ($query);

	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

 

	$connection->disconnect();


		echo  "{ success: true,  data: 1}";


?>