<?php 

require "ewDataHandler.php"; 

$ClientId = $_GET['ClientId'];
$RegistrantTypeId = $_GET['RegistrantTypeId'];
$ServiceDate = $_GET['ServiceDate'];
$StartTime = $_GET['StartTime'];
$EndTime = $_GET['EndTime'];

$rcr_transaction = new dataHandler(); 


$result = $rcr_transaction->getClientApprRegistrantsByProximity($ClientId, 
																$RegistrantTypeId,
																$ServiceDate,
																$StartTime,
																$EndTime  );

$rcr_transaction->disconnectDB (); 

echo  "{ success: true,  data: ".json_encode($result)."}";
  

?>
