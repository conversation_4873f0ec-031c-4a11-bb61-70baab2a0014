<?php 


	require "ewDataHandler.php"; 

  
	$rcr_transaction = new dataHandler(); 

	$StudentId = $_POST['StudentId'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	$ConfirmationNumber = $_POST['ConfirmationNumber'];
	$PayrollWeek = $_POST['PayrollWeek'];
	$StartTime = $_POST['StartTime'];
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];

	$Dow = $_POST['Dow'];
	$UserId = $_POST['UserId'];


	// Process for ALL Selected Week Dates
	//==========================================


	$dow_array = explode(",", $Dow);
	$dow_count = count($dow_array);

	for ($i=0; $i<$dow_count; $i++)
	  {

		
		// Calculate Service Date(s)
		//==========================================
		$offset = (7 - $dow_array[$i]);
		$ServiceDate = $PayrollWeek;
		$ServiceDate = date('Y-m-d', strtotime($ServiceDate . ' - '.$offset.' day'));

		// Calculate Week Day
		//==========================================

		$wd = date('l', strtotime($ServiceDate));
		$WeekDay = substr($wd, 0, 3);
		
			 
		
				$result = $rcr_transaction->setSchStudentWklyServicesFromTemplate(	$PayrollWeek,
																					$StudentId,
																					$ServiceTypeId,
																					$ConfirmationNumber,
																					$ServiceDate,
																					$StartTime,
																					$EndTime,
																					$TotalHours,
																					$WeekDay, 
																					$UserId ); 
			
			 


			
	  
		if ($i > 30) {
			break;
		}
	  
	  }
	  

	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

 
//echo ('Step 01');

?>
