<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];

  $InclStatuses =$_GET['InclStatuses']; 

  
    $query = "	SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
                      TotalHours as TotalHoursUnf,
                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
                      
                      COALESCE((SELECT SessionFrequency FROM SchStudentMandates i 
                        WHERE a.MandateId = i.Id),'5') as SessionFrequency,

                      CASE  SessionGrpSize 
                        WHEN '0' THEN '1'
                      ELSE SessionGrpSize
                      END AS SessionGrpSize,

                      a.RegistrantId,
                      CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      SchoolName,
                      
                    /*  
                      (SELECT a.PaidFL FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as PaidFL,
                      (SELECT a.BilledFL FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as BilledFL,
                    */
                      ServiceTypeDesc                        

                  FROM  WeeklyServices a, 
                        SchStudents b, 
                        Registrants e,
                        ScheduleStatuses g, 
                        SchSchools d,
                        SchServiceTypes h 

                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.RegistrantId = e.Id 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                      /*   AND   a.ScheduleStatusId = 8  */
                        AND   a.ScheduleStatusId  in ({$InclStatuses})  
                        AND   b.Id = a.StudentId
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   a.ServiceTypeId = h.Id
                  GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours  ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

  echo $ret;
  //echo $query;

?>

