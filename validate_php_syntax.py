#!/usr/bin/env python3
"""
PHP Syntax Validator
====================

Simple script to validate PHP syntax after conversion.
"""

import subprocess
import sys
import os

def check_php_syntax(php_file):
    """Check PHP syntax using php -l command"""
    if not os.path.exists(php_file):
        print(f"❌ File not found: {php_file}")
        return False
    
    try:
        # Run php -l to check syntax
        result = subprocess.run(['php', '-l', php_file], 
                              capture_output=True, 
                              text=True)
        
        if result.returncode == 0:
            print(f"✅ {php_file}: Syntax OK")
            return True
        else:
            print(f"❌ {php_file}: Syntax Error")
            print(f"   Error: {result.stderr.strip()}")
            return False
            
    except FileNotFoundError:
        print("❌ PHP not found. Please install PHP to validate syntax.")
        return False
    except Exception as e:
        print(f"❌ Error checking {php_file}: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 validate_php_syntax.py <php_file> [php_file2] ...")
        print("Example: python3 validate_php_syntax.py ewDataHandler_fixed.php")
        sys.exit(1)
    
    php_files = sys.argv[1:]
    all_valid = True
    
    print("PHP Syntax Validation")
    print("=" * 30)
    
    for php_file in php_files:
        if not check_php_syntax(php_file):
            all_valid = False
    
    print("=" * 30)
    if all_valid:
        print("✅ All files have valid PHP syntax!")
    else:
        print("❌ Some files have syntax errors. Please review and fix.")
        sys.exit(1)

if __name__ == "__main__":
    main()
