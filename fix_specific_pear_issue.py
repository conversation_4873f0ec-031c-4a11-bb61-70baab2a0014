#!/usr/bin/env python3
"""
Specific fix for the PEAR DB::connect issue
This script targets the exact pattern causing the MYSQLI_NOT_NULL_FLAG error
"""

import re
import sys
import os

def fix_pear_db_connection(file_path):
    """Fix the specific PEAR DB connection issue"""
    
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found")
        return False
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    changes_made = []
    
    # 1. Fix the main DB::connect pattern with variable interpolation
    pattern1 = r'\$this->connection\s*=\s*DB::connect\s*\(\s*"mysqli://\$db_username:\$db_password@\$db_host/\$db_database"\s*\)'
    if re.search(pattern1, content):
        replacement1 = """$this->connection = new mysqli($db_host, $db_username, $db_password, $db_database)"""
        content = re.sub(pattern1, replacement1, content)
        changes_made.append("Fixed main DB::connect() call")
    
    # 2. Fix any other DB::connect patterns
    pattern2 = r'DB::connect\s*\(\s*"mysqli://[^"]*"\s*\)'
    if re.search(pattern2, content):
        replacement2 = 'new mysqli($db_host, $db_username, $db_password, $db_database)'
        content = re.sub(pattern2, replacement2, content)
        changes_made.append("Fixed additional DB::connect() calls")
    
    # 3. Fix DB::isError($this->connection) checks
    pattern3 = r'if\s*\(\s*DB::isError\s*\(\s*\$this->connection\s*\)\s*\)'
    if re.search(pattern3, content):
        replacement3 = 'if ($this->connection->connect_error)'
        content = re.sub(pattern3, replacement3, content)
        changes_made.append("Fixed DB::isError() connection checks")
    
    # 4. Fix DB::errorMessage($this->connection) calls
    pattern4 = r'DB::errorMessage\s*\(\s*\$this->connection\s*\)'
    if re.search(pattern4, content):
        replacement4 = '$this->connection->connect_error'
        content = re.sub(pattern4, replacement4, content)
        changes_made.append("Fixed DB::errorMessage() calls")
    
    # 5. Add connection error check after new mysqli()
    # Look for new mysqli() calls without proper error checking
    mysqli_pattern = r'(\$this->connection\s*=\s*new mysqli\([^)]+\))\s*;?'
    
    def add_error_check(match):
        mysqli_call = match.group(1)
        return f"""{mysqli_call};
        if ($this->connection->connect_error) {{
            die("Connection failed: " . $this->connection->connect_error);
        }}"""
    
    # Only add error check if it's not already there
    if 'new mysqli(' in content and 'connect_error' not in content:
        content = re.sub(mysqli_pattern, add_error_check, content)
        changes_made.append("Added connection error checking")
    
    # 6. Remove the PEAR DB include
    include_pattern = r'require_once\s*\(\s*[\'"]DB\.php[\'"]\s*\)\s*;'
    if re.search(include_pattern, content):
        content = re.sub(include_pattern, '// MySQLi is built into PHP - no include needed', content)
        changes_made.append("Removed PEAR DB include")
    
    # Write the fixed content
    if content != original_content:
        # Create backup
        backup_file = file_path + '.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"Backup created: {backup_file}")
        
        # Write fixed file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fixed {file_path}")
        print("Changes made:")
        for i, change in enumerate(changes_made, 1):
            print(f"  {i}. {change}")
        
        return True
    else:
        print("No changes needed")
        return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 fix_specific_pear_issue.py <php_file>")
        print("Example: python3 fix_specific_pear_issue.py ewDataHandler.php")
        sys.exit(1)
    
    php_file = sys.argv[1]
    
    print(f"Fixing PEAR DB connection issue in {php_file}...")
    
    if fix_pear_db_connection(php_file):
        print("\n✅ Fix completed successfully!")
        print("\nNext steps:")
        print("1. Test the PHP file to ensure it works")
        print("2. Check that MySQLi extension is installed: php -m | grep mysqli")
        print("3. Verify your db_login.php has the correct variable names")
    else:
        print("\n❌ No fixes were applied")

if __name__ == "__main__":
    main()
