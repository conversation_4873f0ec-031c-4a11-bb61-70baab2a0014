<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId = $_POST['RegistrantId'];
	$CredItemId = $_POST['CredItemId'];
	$CredItemStatus = $_POST['CredItemStatus'];
	$CredItemType = $_POST['CredItemType'];
	$ExpirationDate = $_POST['ExpirationDate'];
	$OrigDocumentDate = $_POST['OrigDocumentDate'];
	$Results = $_POST['Results'];
	$UserId = $_POST['UserId'];


	// Credentialing Item Type 1 - Neeed Once
	//================================
	if ($CredItemType == 1) {
		
		$ExpirationDate = NULL;
		
		if ($OrigDocumentDate) {
		
			$CredItemStatus = '2';  
			$ComplianceLevelId = '2';   
		} else {
			$CredItemStatus == '1'; 
			$ComplianceLevelId = '1';
			
		}

	}

	// Credentialing Item Type 2 and 3 - Renewal Needed
	//================================

	if ($CredItemType != 1) {


			$exp_date = date('Y-m-d', strtotime($ExpirationDate));
			$curr_date = date('Y-m-d');

			$exp_date1 = strtotime($exp_date);
			$curr_date1 = strtotime($curr_date);

			if ($curr_date1 > $exp_date1)  {
				$ComplianceLevelId = '1';  
				$CredItemStatus = '1';
			} else {
				$ComplianceLevelId = '2';
				$CredItemStatus = '2';
			}

	}

	$result = $rcr_transaction->setRegistrantCredItems (	$RegistrantId,
															$CredItemId, 
															$CredItemStatus,
															$ComplianceLevelId,
															$OrigDocumentDate,
															$ExpirationDate,
															$Results,
															$UserId ); 
															
															
	
	if ($CredItemStatus == '1') {
	
		$AwatingVerification = '0';
	
		$result1 = $rcr_transaction->setRegistrantDocumentVerificationStatus (	$RegistrantId,
																				$CredItemId, 
																				$AwatingVerification,
																				$UserId ); 

		
	
	}
	

	$rcr_transaction->disconnectDB (); 
	//echo  '{ success: true };
	echo $result;
?>
