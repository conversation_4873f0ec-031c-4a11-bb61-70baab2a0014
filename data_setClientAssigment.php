<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$Id = $_POST['Id'];
$StatusId = $_POST['StatusId'];
$ClientId = $_POST['ClientId'];
$ClientUnitId = $_POST['ClientUnitId'];
$ServiceTypeId = $_POST['ServiceTypeId'];
$StartDate = $_POST['StartDate'];
$EndDate = $_POST['EndDate'];
$ConfirmationNumber = $_POST['ConfirmationNumber'];
$UserId = $_POST['UserId'];


$result = $rcr_transaction->setClientAssigment(	$Id,
												$StatusId,
												$ClientId,
												$ClientUnitId,
												$ServiceTypeId,
												$StartDate,
												$EndDate,
												$ConfirmationNumber,	
												$UserId); 

$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
