

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_SchoolAssigmentsToWklyServicesConversion$$

	CREATE PROCEDURE proc_SchoolAssigmentsToWklyServicesConversion ()  

	BEGIN


			DECLARE v_MonDate, v_TueDate, v_WedDate, v_ThuDate, v_FriDate, v_PayrollWeek DATE ; 
			DECLARE v_SchoolClientId INT ; 

			SELECT Id INTO v_SchoolClientId
				FROM Clients
			WHERE SchoolFL = '1' LIMIT 1;	

			/* Mon */
			SELECT DATE_ADD(curdate(), INTERVAL (9 - IF(DAYOFWEEK(curdate())=1, 8, DAYOFWEEK(curdate()))) DAY) INTO v_MonDate; 

			/* Tue */
			SELECT DATE_ADD(v_MonDate, INTERVAL 1 DAY) INTO v_TueDate;

			/* Wed */
			SELECT DATE_ADD(v_MonDate, INTERVAL 2 DAY) INTO v_WedDate;

			/* Thu */
			SELECT DATE_ADD(v_MonDate, INTERVAL 3 DAY) INTO v_ThuDate;

			/* Fri */
			SELECT DATE_ADD(v_MonDate, INTERVAL 4 DAY) INTO v_FriDate;

			/* Sat - Payroll Week */
			SELECT DATE_ADD(v_MonDate, INTERVAL 5 DAY) INTO v_PayrollWeek;

	/* School Assignments 
	 ============================*/		
	 		create temporary table tmp engine=memory
			
			SELECT 	a.SchoolId,
					a.ServiceTypeId,
					a.ConfirmationNumber,
					a.AssignmentTypeId,
					b.RegistrantId,
					b.WeekDayId,
					d.WeekDay,
					CASE b.WeekDayId
						WHEN 1 THEN v_MonDate	
						WHEN 2 THEN v_TueDate	
						WHEN 3 THEN v_WedDate	
						WHEN 4 THEN v_ThuDate	
						WHEN 5 THEN v_FriDate	
					END as ServiceDate,	
					b.StartTime,
					b.EndTime,
					b.TotalHours
			FROM  	SchSchoolAssignmentHeader a,
					SchSchoolAssignmentDetails b,
					Registrants c,
					DaysOfWeek d
			WHERE a.StatusId = '1'
			AND  v_MonDate between a.StartDate and a.EndDate 
			AND  a.Id = b.AssignmentId 
			AND  b.RegistrantId = c.Id 
			AND  b.WeekDayId = d.WeekDayId
			  ;
	 

			/* Delete Duplicates
			 =======================*/	  

			 DELETE FROM tmp 
			 WHERE EXISTS (SELECT 1 FROM WeeklyServices a 
			 				WHERE tmp.SchoolId = a.SchoolId
			 				AND   tmp.ServiceDate = a.ServiceDate
			 				AND   tmp.ServiceTypeId = a.ServiceTypeId
			 				AND   a.CLientId = v_SchoolClientId 	
			 	);


			INSERT IGNORE INTO WeeklyServices
		                (	
							CLientId,
							PayrollWeek,
							SchoolId,
							ScheduleStatusId,
							ServiceTypeId,
							AssignmentTypeId,	
							ConfirmationNumber,
							RegistrantId,
							ServiceDate,	 
							StartTime, 
							EndTime, 		
							TotalHours , 
							WeekDay ,
							UserId,
							TransDate 
						)	
											
					SELECT  v_SchoolClientId,
							v_PayrollWeek,
							SchoolId,
							'7',
							ServiceTypeId,
							AssignmentTypeId,
							ConfirmationNumber,
							RegistrantId,
							ServiceDate,	 
							StartTime, 
							EndTime, 		
							TotalHours , 
							WeekDay ,
							'0',
							NOW()
					FROM tmp	;
						 						

	/* Student Assignments
	 ============================*/		
	 		create temporary table tmp1 engine=memory
			
			SELECT 	a.StudentId,
					a.ServiceTypeId,
					a.ConfirmationNumber,
					a.AssignmentTypeId,
					b.RegistrantId,
					b.WeekDayId,
					d.WeekDay,
					CASE b.WeekDayId
						WHEN 1 THEN v_MonDate	
						WHEN 2 THEN v_TueDate	
						WHEN 3 THEN v_WedDate	
						WHEN 4 THEN v_ThuDate	
						WHEN 5 THEN v_FriDate	
					END as ServiceDate,	
					b.StartTime,
					b.EndTime,
					b.TotalHours
			FROM  	SchStudentAssignmentHeader a,
					SchStudentAssignmentDetails b,
					Registrants c,
					DaysOfWeek d
			WHERE a.StatusId = '1'
			AND  v_MonDate between a.StartDate and a.EndDate 
			AND  a.Id = b.AssignmentId 
			AND  b.RegistrantId = c.Id 
			AND  b.WeekDayId = d.WeekDayId
			  ;
	 

			/* Delete Duplicates
			 =======================*/	  

			 DELETE FROM tmp1 
			 WHERE EXISTS (SELECT 1 FROM WeeklyServices a 
			 				WHERE tmp1.StudentId = a.StudentId
			 				AND   tmp1.ServiceDate = a.ServiceDate
			 				AND   tmp1.ServiceTypeId = a.ServiceTypeId
			 				AND   a.CLientId = v_SchoolClientId 	
			 	);


			INSERT IGNORE INTO WeeklyServices
		                (	
							CLientId,
							PayrollWeek,
							StudentId,
							ScheduleStatusId,
							ServiceTypeId,
							AssignmentTypeId,	
							ConfirmationNumber,
							RegistrantId,
							ServiceDate,	 
							StartTime, 
							EndTime, 		
							TotalHours , 
							WeekDay ,
							UserId,
							TransDate 
						)	
											
					SELECT  v_SchoolClientId,
							v_PayrollWeek,
							StudentId,
							'7',
							ServiceTypeId,
							AssignmentTypeId,
							ConfirmationNumber,
							RegistrantId,
							ServiceDate,	 
							StartTime, 
							EndTime, 		
							TotalHours , 
							WeekDay ,
							'0',
							NOW()
					FROM tmp1	;

		

			drop temporary table if exists tmp;
			drop temporary table if exists tmp1;



	END $$

	DELIMITER ;	