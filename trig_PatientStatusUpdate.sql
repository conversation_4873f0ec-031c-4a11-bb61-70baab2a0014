
/*=========================================*/

DELIMITER $$

DROP TRIGGER IF EXISTS trig_PatientStatusUpdate  $$

CREATE TRIGGER trig_PatientStatusUpdate BEFORE UPDATE ON Patients

FOR EACH ROW BEGIN
	 
        IF NEW.StatusId = 2 THEN
		
			SET @p_schedule_id = (SELECT Id FROM WeeklyServices a
										WHERE PatientId = NEW.Id	
										AND a.ScheduleStatusId = '7'
										AND ((a.ServiceDate > curdate()) or (a.ServiceDate = curdate() and a.StartTime > curtime())) 
									);
			
			SET @p_msg_id = (SELECT MAX(Id) FROM WeeklyServicesMessages 
										WHERE ScheduleId = @p_schedule_id	);
									 

			UPDATE WeeklyServices 
			SET ScheduleStatusId = '1',
				PatientId = 0
			WHERE Id = @p_schedule_id ;
			
			UPDATE WeeklyServicesMessages 
			SET Msg = 'Assigned Patient was Discharged!'  
			WHERE Id = @p_msg_id ;
			
			
		END IF;
     
	
		 
	
END; $$

DELIMITER ;