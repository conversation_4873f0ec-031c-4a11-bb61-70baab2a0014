<?php
	
	require "ewDataHandler.php";

	$rcr_transaction = new dataHandler(); 
	
	$ContractId = $_GET['ContractId'];
	$CalendayYear = $_GET['CalendayYear'];
	
	
	$StartDate = mktime(0, 0, 0, 01, 01,  $CalendayYear);
	$LastYearDate = mktime(0, 0, 0, 12, 31,  $CalendayYear);
	
	
	 
	$w = date("w", $StartDate);
	
	if (($w == 6) || ($w == 0)  ) {
	
		
		$w = 1;
	
		 
	} 	
	
	$days = array('Monday', 'Tuesday', 'Wednesday','Thursday','Friday');
	$allDates = Array();
	 
	while(1) {
	 
		$x=$w-1;		
	 
		for ($x; ($x<5); $x++)
			{
				$week_date = strtotime($days[$x], $StartDate);
				
				//=============================
				
				//$ContractId = 1;
				//$CalendayYear = '2014';
				$HolidayDate = date('Y-m-d', $week_date);
				
				$result = $rcr_transaction->getDuplicateHolidays($ContractId, $CalendayYear, $HolidayDate);
				
				if (empty($result)) {
				
				
					//=============================
					$j++;
					$calendarDate = date('Y-m-d', $week_date);
					$calendarDateDesc = date('m-d-Y', $week_date);
					$allDates[] = Array("id" => $j, "CalendarDate" => $calendarDate, "CalendarDateDesc" => $calendarDateDesc);

						
						if ($week_date >= $LastYearDate) {
							break;
						}
				}	 
			}
			
			if ($week_date >= $LastYearDate) {
				break 1;
			}

				
			$StartDate = strtotime('+1 Day', $week_date);
			$w=1;
			
			
			

	}
 
	echo json_encode($allDates);
	
	$rcr_transaction->disconnectDB ();
	
	 
?>