


	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_UpdateRegistrantsMandatoryCredItems$$

	CREATE PROCEDURE proc_UpdateRegistrantsMandatoryCredItems (p_registrant_type_id INT,  p_user_id INT)


	BEGIN




		/*============================================================================= */
		/* Insert Cred. Items (Non-Conditonal )based on Registrant Type ID */
		/*============================================================================= */

		   
		insert into RegistrantCredItems
			(
			RegistrantId,
			StatusId, 
			CredItemId,
			UserId,
			TransDate
			)
		SELECT 	b.Id,
				'2',
				MandatoryCredItemId,
				p_user_id,
				NOW() 
		FROM  CredentialingItemsMandatory a, Registrants b  
			WHERE a.RegistrantTypeId = p_registrant_type_id
			AND   b.TypeId = p_registrant_type_id
			AND   b.StatusId = '1'
			AND CondFL = 0 
			AND NOT EXISTS ( SELECT 1 FROM RegistrantCredItems c 
								WHERE b.Id = c.RegistrantId
								AND   a.MandatoryCredItemId = c.CredItemId
							)

			;
			    
		/*================================================================================== */
		/* Insert into "RegistrantCredentialingItemsConditional" DEFAULT Conditonal Items      */
		/*================================================================================== */
		
		  
		create temporary table tmp engine=memory
		
		SELECT b.Id as RegistrantId,
			   a.Id as ConditionalItemId,
			   a.ConditionalSwitchDefault as ConditionalSwitch	
		FROM 	CredentialingItemsConditionalHeader a, 
				Registrants b,
				CredentialingItemsMandatory c
			WHERE c.RegistrantTypeId = p_registrant_type_id
			AND   b.TypeId = p_registrant_type_id
			AND   a.Id = c.MandatoryCredItemId 
			AND   b.StatusId = '1'
			AND   CondFL = 1
			AND NOT EXISTS ( SELECT 1 FROM RegistrantCredentialingItemsConditional d 
								WHERE b.Id = d.RegistrantId
								AND   a.Id = d.ConditionalItemId
							)	  
			;
		 

		 
		INSERT INTO RegistrantCredentialingItemsConditional 
		SELECT b.Id,
			   a.Id,
			   a.ConditionalSwitchDefault	
		FROM 	CredentialingItemsConditionalHeader a, 
				Registrants b,
				CredentialingItemsMandatory c
			WHERE c.RegistrantTypeId = p_registrant_type_id
			AND   b.TypeId = p_registrant_type_id
			AND   a.Id = c.MandatoryCredItemId 
			AND   b.StatusId = '1'
			AND   CondFL = 1
			AND NOT EXISTS ( SELECT 1 FROM RegistrantCredentialingItemsConditional d 
								WHERE b.Id = d.RegistrantId
								AND   a.Id = d.ConditionalItemId
							)	  
			;
		 	
		/*============================================================================= */
		/* Insert Cred. Items (Conditonal )based on Defaulr Conditions */
		/*============================================================================= */

		    
		INSERT INTO RegistrantCredItems
			(
			RegistrantId,
			CredItemId,
			UserId,
			TransDate
			)
		SELECT 	a.RegistrantId,
				c.CredItemId,
				p_user_id,
				NOW() 
		FROM  tmp a, CredentialingItemsConditionalDetails c
			WHERE a.ConditionalItemId = c.ConditionalItemId
			AND a.ConditionalSwitch = c.ConditionalSwitch 
			AND NOT EXISTS ( SELECT 1 FROM RegistrantCredItems b 
								WHERE a.RegistrantId = b.RegistrantId
								AND   c.CredItemId = b.CredItemId
							)

			;
			
		 	

		drop temporary table if exists tmp;
	 
		
	END $$

	DELIMITER ;	