<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$AdjustmentId = $_POST['AdjustmentId'];
$InvoiceNumber = $_POST['InvoiceNumber'];
$AdjAmount = $_POST['AdjAmount'];
$AdjAmountOrig = $_POST['AdjAmountOrig'];
$AdjReason = $_POST['AdjReason'];
$UserId = $_POST['UserId'];


$result = $rcr_transaction->setClientInvoiceAdjustment(	$AdjustmentId,
														$InvoiceNumber,
														$AdjAmount,
														$AdjReason,
														$UserId ); 


													
$result1 = $rcr_transaction->setClientInvoiceTotalAmount($InvoiceNumber,
														$AdjAmount,
														$AdjAmountOrig,
														$UserId ); 

													
													
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
