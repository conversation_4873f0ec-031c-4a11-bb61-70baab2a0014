<?php 

 	ini_set("memory_limit","-1");

/*
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/ 

    require_once("db_GetSetData.php");

	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


 

 
	$FileExt = $_POST['FileExt'];
	$UploadFileName = $_POST['UploadFileName'];
	$UserId = $_POST['UserId'];
 
 
 
    $UploadFileName = preg_replace('/[^A-Za-z0-9. -]/', '', $UploadFileName);
    

    if ($FileExt == 'xlsx') {

		$inputFileType = 'Excel2007';
	} else {

		$inputFileType = 'Excel5';

	}	

 

	$inputFileName =  '../pr/'.$UploadFileName;

   
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);

		
	} else {



		//print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: File Name - ".$inputFileName."}";
	    Return ; 

	}
    

	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	/**  Load $inputFileName to a PHPExcel Object  **/
	
	$objReader->setReadDataOnly(true);
	$objPHPExcel = $objReader->load($inputFileName);
	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);

	$cnt = 0;	
	$linecount = 0;	
 

	foreach ($sheetData as &$row) { // foreach - start

                 $i++;


                $student_ext_id = $row["C"];
                $guardian_first_name = $row["D"];
                $guardian_last_name = $row["E"];

                $guardian_email = $row["F"];
                $guardian_phone = $row["G"];
 
                if (is_numeric($student_ext_id))  { 

                	$linecount++;
                
                    // echo $i.' $student_ext_id: '.$student_ext_id;
                    // echo ' first name: '.$guardian_first_name;
                    // echo ' last name: '.$guardian_last_name;
                    // echo ' email: '.$guardian_email;
                    // echo ' phone #: '.$guardian_phone.'</br>';

                    $conn = getCon();

                    $guardian_last_name = mysqli_real_escape_string($conn, $guardian_last_name); 

                    $query = " UPDATE SchStudents
                                    SET GuardianFirstName = '{$guardian_first_name}',
                                        GuardianLastName = '{$guardian_last_name}',
                                        GuardianEmail = '{$guardian_email}',
                                        GuardianPhone = '{$guardian_phone}',
                                        UserId = '1',
                                        TransDate = NOW()
                                WHERE ExtId = '{$student_ext_id}'     
                            ";


                    // echo ' query: '.$query.'</br>';


                    $ret =  setData ($conn, $query);    

                     setDisConn($conn);    
                 
                 }


	} // for foreach - end	

 
 	 
	unlink($inputFileName);


	echo  "{ success: true, transactions: '{$linecount}'}";


?>