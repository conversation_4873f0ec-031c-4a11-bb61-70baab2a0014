<?php 


	require "ewDataHandler.php"; 
	   
	$rcr_transaction = new dataHandler();  

	$form_data = json_decode(file_get_contents('php://input'));




	$result = $rcr_transaction->setSelSchStudent(	$form_data->{'Id'},
													$form_data->{'ExtId'},
													$form_data->{'SearchId'},
													$form_data->{'StatusId'},
													$form_data->{'SchoolId'},
													$form_data->{'DateOfBirth'},
													$form_data->{'FirstName'},
													$form_data->{'LastName'},
													$form_data->{'MiddleInitial'},
													$form_data->{'StreetAddress1'},
													$form_data->{'StreetAddress2'},
													$form_data->{'City'},
													$form_data->{'State'},
													$form_data->{'ZipCode'},
													$form_data->{'MobilePhone'},
													$form_data->{'HomePhone'},
													$form_data->{'GuardianFirstName'},
													$form_data->{'GuardianLastName'},													
													$form_data->{'GuardianPhone'},
													$form_data->{'GuardianEmail'},
													$form_data->{'MedicalNeeds'},
													$form_data->{'Comments'},
													$form_data->{'ReportGroupId'},
													$form_data->{'UserId'} ); 

	$rcr_transaction->disconnectDB (); 

	echo $result;

?>
