<?php 
require "ewDataHandler.php";  
  
$rcr_transaction = new dataHandler(); 

$ClientId = $_GET['ClientId'];
if (strlen($ClientId) == 0) {
    $ClientId = '1'; 
}

$result = $rcr_transaction->getSelClient($ClientId);


$rcr_transaction->disconnectDB (); 

$json_data = '{ success: true,  data: '.json_encode($result).'}';   
$json_data = str_replace("[", "", $json_data);
$json_data = str_replace("]", "", $json_data);

echo $json_data;



  
?>
