<?PHP


    
	require_once('DB.php');
	include("db_login.php");
 
    $connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	
	$query = "Update RegistrantCredItems a, CredentialingItems b
				set ComplianceLevelId = 1,
				    CredItemStatus = 3
				WHERE b.Id = a.CredItemId
				AND CredItemType = 2
				AND ExpirationDate < curdate( )
				AND ComplianceLevelId != 3
				AND ExpirationDate IS NOT NULL";
	
	$result = $connection->getAll($query, DB_FETCHMODE_ASSOC);
    
		
	$connection->disconnect();

	echo $query;
     
	

?>





 