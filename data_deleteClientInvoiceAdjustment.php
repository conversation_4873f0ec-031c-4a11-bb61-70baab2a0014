<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$AdjustmentId = $_POST['AdjustmentId'];
$InvoiceNumber = $_POST['InvoiceNumber'];
$AdjAmount = $_POST['AdjAmount'];
$UserId = $_POST['UserId'];


$result = $rcr_transaction->deleteClientInvoiceAdjustment(	$AdjustmentId );


$AdjAmount = $AdjAmount * -1;
$AdjAmountOrig = 0;
													
$result1 = $rcr_transaction->setClientInvoiceTotalAmount($InvoiceNumber,
														$AdjAmount,
														$AdjAmountOrig,
														$UserId ); 

													
													
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
