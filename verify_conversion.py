#!/usr/bin/env python3
"""
Verify that PHP file has been properly converted from PEAR DB to MySQLi
"""

import re
import sys
import os

def verify_conversion(file_path):
    """Verify that the PHP file has been properly converted"""
    
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} not found")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Verifying conversion of {file_path}...")
    print("=" * 50)
    
    # Check for problematic PEAR patterns
    issues = []
    warnings = []
    good_patterns = []
    
    # 1. Check for PEAR DB includes
    if re.search(r'require_once\s*\(\s*[\'"]DB\.php[\'"]\s*\)', content):
        issues.append("❌ Still contains PEAR DB include statement")
    else:
        good_patterns.append("✅ PEAR DB include removed")
    
    # 2. Check for DB::connect calls
    db_connect_matches = re.findall(r'DB::connect\s*\([^)]+\)', content)
    if db_connect_matches:
        issues.append(f"❌ Still contains {len(db_connect_matches)} DB::connect() calls")
        for i, match in enumerate(db_connect_matches[:3], 1):  # Show first 3
            issues.append(f"   {i}. {match}")
    else:
        good_patterns.append("✅ All DB::connect() calls converted")
    
    # 3. Check for new mysqli() calls
    mysqli_matches = re.findall(r'new mysqli\s*\([^)]+\)', content)
    if mysqli_matches:
        good_patterns.append(f"✅ Found {len(mysqli_matches)} MySQLi connection(s)")
    else:
        warnings.append("⚠️  No MySQLi connections found")
    
    # 4. Check for DB::isError calls
    db_error_matches = re.findall(r'DB::isError\s*\([^)]+\)', content)
    if db_error_matches:
        issues.append(f"❌ Still contains {len(db_error_matches)} DB::isError() calls")
    else:
        good_patterns.append("✅ All DB::isError() calls converted")
    
    # 5. Check for DB::errorMessage calls
    db_error_msg_matches = re.findall(r'DB::errorMessage\s*\([^)]+\)', content)
    if db_error_msg_matches:
        issues.append(f"❌ Still contains {len(db_error_msg_matches)} DB::errorMessage() calls")
    else:
        good_patterns.append("✅ All DB::errorMessage() calls converted")
    
    # 6. Check for getAll() method calls
    getall_matches = re.findall(r'->getAll\s*\([^)]+\)', content)
    if getall_matches:
        issues.append(f"❌ Still contains {len(getall_matches)} getAll() method calls")
    else:
        good_patterns.append("✅ All getAll() methods converted")
    
    # 7. Check for escapeSimple() calls
    escape_matches = re.findall(r'->escapeSimple\s*\([^)]+\)', content)
    if escape_matches:
        issues.append(f"❌ Still contains {len(escape_matches)} escapeSimple() calls")
    else:
        good_patterns.append("✅ All escapeSimple() calls converted")
    
    # 8. Check for disconnect() calls
    disconnect_matches = re.findall(r'->disconnect\s*\(\s*\)', content)
    if disconnect_matches:
        issues.append(f"❌ Still contains {len(disconnect_matches)} disconnect() calls")
    else:
        good_patterns.append("✅ All disconnect() calls converted")
    
    # 9. Check for proper MySQLi error handling
    if re.search(r'\$this->connection->connect_error', content):
        good_patterns.append("✅ MySQLi connection error handling found")
    else:
        warnings.append("⚠️  No MySQLi connection error handling found")
    
    if re.search(r'\$this->connection->error', content):
        good_patterns.append("✅ MySQLi query error handling found")
    else:
        warnings.append("⚠️  No MySQLi query error handling found")
    
    # 10. Check for set_charset usage
    if re.search(r'->set_charset\s*\([^)]+\)', content):
        good_patterns.append("✅ MySQLi set_charset() found")
    else:
        warnings.append("⚠️  No MySQLi set_charset() found")
    
    # Print results
    print("\n🟢 GOOD CONVERSIONS:")
    for pattern in good_patterns:
        print(f"  {pattern}")
    
    if warnings:
        print("\n🟡 WARNINGS:")
        for warning in warnings:
            print(f"  {warning}")
    
    if issues:
        print("\n🔴 ISSUES FOUND:")
        for issue in issues:
            print(f"  {issue}")
        print("\n❌ Conversion incomplete - issues need to be fixed")
        return False
    else:
        print("\n✅ Conversion appears successful!")
        return True

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 verify_conversion.py <php_file>")
        print("Example: python3 verify_conversion.py ewDataHandler_final_fix.php")
        sys.exit(1)
    
    php_file = sys.argv[1]
    
    success = verify_conversion(php_file)
    
    if success:
        print("\n🎉 The file appears to be properly converted to MySQLi!")
        print("\nNext steps:")
        print("1. Test the PHP file in your application")
        print("2. Ensure MySQLi extension is installed")
        print("3. Verify database credentials in db_login.php")
    else:
        print("\n🚨 The conversion needs more work!")
        print("Consider running the converter again or fixing issues manually.")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
