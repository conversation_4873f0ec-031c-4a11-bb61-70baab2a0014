

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getCredItemsClientMandatorySelected$$


CREATE  PROCEDURE proc_getCredItemsClientMandatorySelected (IN p_client_id INT, p_client_unit_id INT, p_service_type_id INT )
											  
BEGIN


	
	/*=================================================================================*/
	/* Get All Unselected Client Mandatory Credentialing Items for a gived Client/Client Unit   */ 
	/*=================================================================================*/
	
	create temporary table tmp engine=memory

		SELECT a.Id as id, 
		a.CredItemDesc,
		'0' as CondFL
		FROM  CredentialingItems a
		WHERE  EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b
						WHERE b.ClientId = p_client_id
						AND b.ClientUnitId = p_client_unit_id
                        AND a.id = b.MandatoryCredItemId
						AND b.ServiceTypeId = p_service_type_id
						AND CondFL = 0)  						
	UNION
		SELECT  c.Id as id, 
		c.ConditionalItemDesc as CredItemDesc,
		'1' as CondFL
		FROM  CredentialingItemsConditionalHeader c
		WHERE  EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b
						WHERE b.ClientId = p_client_id
						AND b.ClientUnitId = p_client_unit_id
						AND b.ServiceTypeId = p_service_type_id
                        AND c.id = b.MandatoryCredItemId
						AND CondFL = 1)  						
		
		
		; 	
		
	
	SELECT * FROM tmp a
	ORDER BY a.CredItemDesc ;	
	 
	
	
	drop temporary table if exists tmp;
	
 
END$$

DELIMITER ;	
