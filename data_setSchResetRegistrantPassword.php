<?php

	require_once('DB.php');
	include("db_login.php");
 
    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 

	$NotificationEmails = $_POST['NotificationEmails'];
	$Subject = $_POST['Subject'];
	$Message = $_POST['Message'];
	$RegistrantId = $_POST['RegistrantId'];
	$RegistrantFirstNameInit = $_POST['RegistrantFirstNameInit'];
	$RegistrantLastNameOrig = $_POST['RegistrantLastName'];

	$last_name_arr = explode(" ",trim($RegistrantLastNameOrig));

	$RegistrantFirstNameInit = $connection->escapeSimple($RegistrantFirstNameInit);
	$RegistrantLastName = $connection->escapeSimple($last_name_arr[0]);

    $login = $RegistrantFirstNameInit.$RegistrantLastName;
    $pass = $login.'1';  


	// CONCAT( LOWER( SUBSTRING( NEW.FirstName, 1, 1 ) ) , LOWER(SPLIT_STR(trim(REPLACE(NEW.LastName,'-',' ')), ' ', 1)) ),
	// MD5(CONCAT( LOWER( SUBSTRING( NEW.FirstName, 1, 1 ) ) , LOWER(SPLIT_STR(trim(REPLACE(NEW.LastName,'-',' ')), ' ', 1)),'1' )),


    $query = "UPDATE Users b, Registrants a 
				set b.Login = CONCAT( LOWER( '{$RegistrantFirstNameInit}' ) , LOWER(SPLIT_STR(trim(REPLACE('{$RegistrantLastName}','-',' ')), ' ', 1))), 
				    b.Password = MD5(CONCAT( LOWER( '{$RegistrantFirstNameInit}' ) , LOWER(SPLIT_STR(trim(REPLACE('{$RegistrantLastName}','-',' ')), ' ', 1)),'1' ) ) ,
				    b.ResetFL = '1'
				WHERE a.Id = '{$RegistrantId}'
				AND b.ExtId =  a.SearchId";
	
	$result = $connection->getAll($query, DB_FETCHMODE_ASSOC);

  	echo $query;


	/*==================*/

 

	$to      = $NotificationEmails;
	
	$subject = $Subject;
	$message = $Message;
	$headers = 'From: <EMAIL>' . "\r\n" .
	    'Reply-To: <EMAIL>' . "\r\n" .
	    'X-Mailer: PHP/' . phpversion();

	mail($to, $subject, $message, $headers);
 
	 
	$connection->disconnect();
 

?>