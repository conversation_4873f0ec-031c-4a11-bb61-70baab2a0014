
/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getPayrollPPWeeklyTransactions$$

CREATE PROCEDURE proc_getPayrollPPWeeklyTransactions (IN p_payroll_week date)  

BEGIN

	DECLARE v_ScheduleIdList  VARCHAR(512); 
 	
	/*============================================*/
	
	create temporary table tmp engine=memory

    SELECT 	'Shift' as TransType,
    		a.Id AS ScheduleId, 
    		b.Extid as EmplNumber,
    		a.ClientId,
    		'1' as PayNumber,

			CASE a.ShiftTypeId
   				When '1' Then 'PR'
   				ELSE 'SH'
   			END as ClientNameAbbr,   			

    		'   ' as PatientTypeAbbr,
   			'    ' as ServiceTypeDesc	,
   			'                  ' as Location,
   			'                  ' as PlaceOfWork,

   			ClientName,
   			UnitName as Worked,
   			b.FirstName, 
   			b.LastName,
			b.Id as RegistrantId,
			b.TypeId as RegistrantTypeId,
			ServiceDate as ServiceDateSort,
			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
			DATE_FORMAT( StartTime, '%l:%i %p' ) as TimeIn,
			DATE_FORMAT( EndTime, '%l:%i %p' ) as TimeOut,
			StartTime as OrigStartTime,
			EndTime as OrigEndTime,
			PayAdjustAmount,
			TotalHours,
			TotalHours as OrigTotalHours, 
			TotalHours as PayrollHours,
			PatientId,
			ScheduleStatusId,
			a.ShiftTypeId,
			PayorTypeId,
			000.00 as PayRate,
			0000.00 as PayAmount,
			'0' as HolidayFL

		FROM 	WeeklyServices a, 
				Registrants b,
				ClientUnits c,
				Clients d,
				Patients e  
		WHERE PayrollWeek <=  p_payroll_week
		AND   PayrollSubmitFL = '0' 
		AND   a.ScheduleStatusId in ('7','9','10','11')	
		AND   a.RegistrantId = b.Id
		AND   a.ClientId = d.Id 
		AND   a.ClientUnitId = c.Id
		AND   a.PatientId = e.Id 
		AND   e.PayorTypeId = '2'		 
		;

	/*	Set "Place of Work"
     =======================*/

		UPDATE tmp
			SET PlaceOfWork = ClientNameAbbr;


    /*  Next Date Part of the "Split Shifts" 
     ===================================*/

    create temporary table tmp2 engine=memory

    SELECT 	'Shift' as TransType,
    		a.Id AS ScheduleId, 
    		b.Extid as EmplNumber,
    		a.ClientId,
    		'1' as PayNumber,
			CASE a.ShiftTypeId
   				When '1' Then 'PR'
   				ELSE 'SH'
   			END as ClientNameAbbr,   			
    		'   ' as PatientTypeAbbr,
   			'    ' as ServiceTypeDesc	,
   			'                  ' as Location,
   			'                  ' as PlaceOfWork,
   			ClientName,
   			UnitName as Worked,
   			b.FirstName, 
   			b.LastName,
			b.Id as RegistrantId,
			b.TypeId as RegistrantTypeId,
			ServiceDate as ServiceDateSort,
			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
			DATE_FORMAT( StartTime, '%l:%i %p' ) as TimeIn,
			DATE_FORMAT( EndTime, '%l:%i %p' ) as TimeOut,
			StartTime as OrigStartTime,
			EndTime as OrigEndTime,
			PayAdjustAmount,
			TotalHours,
			TotalHours as OrigTotalHours, 
			TotalHours as PayrollHours,
			PatientId,
			ScheduleStatusId,
			a.ShiftTypeId,
			PayorTypeId,
			000.00 as PayRate,
			0000.00 as PayAmount,
			'0' as HolidayFL

        FROM    WeeklyServices a, 
                Registrants b,
                ClientUnits c,
                Clients d,
                Patients e 
        WHERE PayrollWeek <=  p_payroll_week
        AND   PayrollSubmitFL = '0' 
        AND   a.ScheduleStatusId in ('7','9','10','11') 
        AND   a.EndTime < a.StartTime
        AND   a.RegistrantId = b.Id
        AND   a.ClientId = d.Id 
        AND   a.ClientUnitId = c.Id 
		AND   a.PatientId = e.Id 
		AND   e.PayorTypeId = '2'
        ;


 	

 
    SET v_ScheduleIdList =   (Select group_concat( DISTINCT ScheduleId SEPARATOR ', ' ) 
    FROM tmp2    
    WHERE HolidayFL = '1' 
    GROUP BY 'all');  


    DELETE FROM tmp
    WHERE ScheduleId in (v_ScheduleIdList);

    INSERT INTO tmp
    SELECT * FROM tmp2
    WHERE ScheduleId in (v_ScheduleIdList);
 

    /*  Mark Holiday Shifts                         
    ==========================================================*/


    UPDATE tmp a , CompanyHolidays b 
        SET    HolidayFL = '1'
    WHERE a.ServiceDateSort = b.HolidayDate ; 


    /* Set Holiday "Hourly" Shifts "Place of Work" and "ClientNameAbbr" 
     ================================*/

	UPDATE tmp
          SET PlaceOfWork = 'PH',
              ClientNameAbbr = 'PH'
    WHERE ClientNameAbbr = 'PR'
    AND   HolidayFL = '1';


	/*	Set "Place of Work"
     =======================*/

		UPDATE tmp
			SET PlaceOfWork = ClientNameAbbr;


	/*	Update "Shift Type" for "Live-In" Shifts 
     =========================================*/
	
	UPDATE tmp 
       set ShiftTypeId = '2',
           PayrollHours = 13
    WHERE TotalHours = 24;
       


	/*	Set Service Type Abbr/Location
     =======================*/
	UPDATE tmp a, Patients b, ServiceTypes c
		SET a.ServiceTypeDesc = c.ServiceTypeDesc,
		    a.Location = b.LastName	 	
	WHERE a.PatientId = b.Id
	AND  b.ServiceTypeId = c.Id ;	





	/*	Set "Pay Amount" for "Hourly" Shifts 
     =========================================*/
/*
	UPDATE tmp a, PatientsPPBillingRates b 
		SET a.PayRate = b.PayRate,
			a.PayAmount = b.PayRate * a.TotalHours
	WHERE a.PatientId = b.PatientId
	AND a.ShiftTypeId = '1'
	AND a.ShiftTypeId = b.ShiftTypeId 
	;
*/

	/*	Set "Pay Amount" for "Live-In" Shifts 
     =========================================*/

	UPDATE tmp a, PatientsPPBillingRates b 
		SET a.PayRate = (b.PayRate / 13),
			a.PayAmount = b.PayRate 
	WHERE a.PatientId = b.PatientId
	AND a.ShiftTypeId = '2'
	AND a.ShiftTypeId = b.ShiftTypeId 
	;

	/*	Calculate Holiday Pay Rates 						
	==========================================================*/
	UPDATE tmp 
		SET PayRate =  PayRate * 1.5,
            PayAmount = PayAmount * 1.5
	WHERE HolidayFL = '1'
	AND   PayAmount > 0 ; 


	/*	Set "Workded" to "Client Name" for all "PP" a Facilites 						
	==========================================================*/

	UPDATE tmp
		SET Worked = ClientName
	WHERE ClientId != '38';	
	 


	/*	Add Payroll Adjustment
     ==================================*/
 
    INSERT INTO tmp
    (
			TransType,
			ScheduleId,
	        EmplNumber,
	        ClientId,
    		PayNumber,
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			PayAdjustAmount,
			ServiceDateSort,
			ServiceDate, 
			TimeIn,
			TimeOut,
			OrigStartTime,
			OrigEndTime,
			TotalHours,
			PayrollHours,
			PayorTypeId
    )   

		SELECT	
			'Adj',
			ScheduleId,
	        EmplNumber,
	        '',
    		'1',
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			PayAdjustAmount,
			ServiceDate as ServiceDateSort,
			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
			DATE_FORMAT( TimeIn, '%l:%i %p' ) as TimeIn,
			DATE_FORMAT( TimeOut, '%l:%i %p' ) as TimeOut,
			TimeIn as OrigStartTime,
			TimeOut as OrigEndTime,
			TotalHours,
			TotalHours as PayrollHours,
			'2' 
	
		FROM PayrollAdjustments
		WHERE PayrollWeek =  p_payroll_week 
		AND PayorTypeId = '2'  	;


	/*	Calculate Registrants' Overtime 
     ===================================*/

	create temporary table tmp1 engine=memory

	Select  RegistrantId,
			EmplNumber,
			FirstName, 
   			LastName,

			CAST((sum(PayrollHours) - 40) as SIGNED) as OvrtHours,
			0.00 as OvrtPayAmt,
			5.00 as OvrtPayRate,
			0 AS PatientId, 
			'                       ' as PatientName  	
		FROM tmp
		WHERE TransType != 'Adj'
		AND   HolidayFL != '1'
		AND   ShiftTypeId != '2' 
		AND   RegistrantTypeId != '12'
		AND   PayorTypeId = '2' 
	GROUP BY 	RegistrantId, 
				EmplNumber,
				FirstName,
				LastName
	HAVING  sum(PayrollHours) > 40;		


	/* Set Patient Name  
     ===========================*/
	
     UPDATE tmp1 a 
     	SET a.PatientId = (SELECT b.PatientId
     						FROM tmp b
     							WHERE  a.RegistrantId = b.RegistrantId
     						LIMIT 1
     						)   
     ;	


     UPDATE tmp1 a, Patients b 
     	SET a.PatientName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName))
     WHERE  a.PatientId = b.Id ;
     	


	/*	Adjust ServiceTypeDesc for  Holidays 						
	==========================================================*/
/*
	UPDATE tmp 
		SET PlaceOfWork =  CONCAT(PlaceOfWork,'H') 
    WHERE  HolidayFL = '1' ;

*/


	/*	Add Overtime Hours/Pay Amount
     ==================================*/
 

    INSERT INTO tmp
    (
	        TransType,
	        EmplNumber,
    		PayNumber,
   			FirstName, 
   			LastName,
   			PlaceOfWork,
   			ClientNameAbbr,
			ServiceDate, 
			TimeIn,
			TimeOut,
			Location,
			Worked,
			PayRate,
			PayAmount,
			TotalHours,
			PayrollHours,
			PayAdjustAmount,
			PayorTypeId,
			ServiceDateSort

    )   

		SELECT	
	        'Ovr',
	        EmplNumber,
    		'1',
   			FirstName, 
   			LastName,
   			'Overtime',
   			'SH',
   			'',
   			'',
   			'',
   			/*LastName,*/
   			/*'Overtime',*/
   			PatientName,
			CONCAT('Overtime ',CAST(OvrtHours as CHAR(3)),' Hrs'),
			OvrtPayRate, 
			(OvrtHours * OvrtPayRate),
			OvrtHours,
			OvrtHours,
			/*'',*/
			(OvrtHours * OvrtPayRate),
			'2',
			'2099-01-01'
	
		FROM tmp1 	;



 	/*==================================*/
 	 
 
 	/*	Load Patients in the "Home" (ClientId = 38) 
     ==================================*/
	 
 	create temporary table tmp3 engine=memory

 	
	SELECT 	ScheduleId,
	        EmplNumber,
	        ClientId,
    		PayNumber,
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			PayAdjustAmount,
			ServiceDateSort,
			ServiceDate, 
			TimeIn,
			TimeOut,
			OrigStartTime,
			OrigEndTime,
			PayRate,
			PayAmount,
			'' as TotalHours,
			PayrollHours 


	  FROM tmp 
	 	WHERE Worked = 'Private Home' 
	 	AND TransType = 'Shift'
	 	ORDER BY Location, ServiceDateSort,  TimeIn 
	 ;

 	/*	Load Patients in  "Facilities" (ClientId != 38) 
     ==================================*/
	 
 	INSERT INTO  tmp3 

 	
	SELECT 	ScheduleId,
	        EmplNumber,
	        ClientId,
    		PayNumber,
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			PayAdjustAmount,
			ServiceDateSort,
			ServiceDate, 
			TimeIn,
			TimeOut,
			OrigStartTime,
			OrigEndTime,
			PayRate,
			PayAmount,
			'' as TotalHours,
			PayrollHours 


	  FROM tmp 
	 	WHERE Worked != 'Private Home'
	 	AND TransType = 'Shift'
	 	ORDER BY Worked, Location, ServiceDateSort  
	 ;
 	 
 	/*	Load "Overtime" Transactions
     ==================================*/
	 
 	INSERT INTO  tmp3 

 	
	SELECT 	ScheduleId,
	        EmplNumber,
	        ClientId,
    		PayNumber,
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			'',
			'',
			'0000-00-00', 
			'',
			'',
			'',
			'',
			PayRate,
			PayAmount,
			'' as TotalHours,
			PayrollHours 


	  FROM tmp 
	 	WHERE TransType = 'Ovr'
	 ;

 	/*	Load "Adjuistments" Transactions
     ==================================*/
	 
 	INSERT INTO  tmp3 

 	
	SELECT 	ScheduleId,
	        EmplNumber,
	        ClientId,
    		PayNumber,
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			PayAdjustAmount,
			ServiceDateSort,
			ServiceDate, 
			TimeIn,
			TimeOut,
			OrigStartTime,
			OrigEndTime,
			PayRate,
			PayAmount,
			'' as TotalHours,
			PayrollHours 


	  FROM tmp 
	 	WHERE TransType = 'Adj'
	 ;

	  
 
	 SELECT   
			ScheduleId,
	        EmplNumber,
	        ClientId,
    		PayNumber,
    		PlaceOfWork,
    		ClientNameAbbr,
   			Location,
   			Worked,
   			FirstName, 
   			LastName,
			PayAdjustAmount,
			ServiceDateSort,
			ServiceDate, 
			TimeIn,
			TimeOut,
			OrigStartTime,
			OrigEndTime,
			PayRate,
			PayAmount, 
			TotalHours,
			PayrollHours 

	 FROM tmp3;
	 

	drop temporary table if exists tmp;
	drop temporary table if exists tmp1;
	drop temporary table if exists tmp2;
	drop temporary table if exists tmp3;
	 
	
END $$

DELIMITER ;	