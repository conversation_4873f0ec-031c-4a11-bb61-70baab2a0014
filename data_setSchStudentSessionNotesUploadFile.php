<?php

	require_once('DB.php');
	include('db_login.php');

	
	
	$ScheduleId = $_POST['ScheduleId'];
	$RegistrantId = $_POST['RegistrantId'];
	$StudentId = $_POST['StudentId'];
	$DocumentName = $_POST['OrigDocumentName'];
	$UserId = $_POST['UserId'];

	
	
	$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 



	//==================================
	// Check if Registrant's Document already exists
	//==================================
	
	$query = "SELECT StoredName as GeneratedName 
				FROM SchStudentsSessionNotes			 
			WHERE ScheduleId = '{$ScheduleId}' ";
	
	$result = $connection->query ($query);

	
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }


	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$generated_file_name = $row['GeneratedName'];
	}	


	/* Generate Name for newaly uploaded file 
	 =============================================*/
	$new_file_name =  generateRandomString();
	$new_file_path =  '../hr/'.$new_file_name.'.pdf';


	/* Upload New File 
	 =============================================*/
		
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $new_file_path);
		
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    Return ; 

	}


	
	
	
	
	/* If Document for selected Cred. Item was prev. uploaded - delete it
	   and update RegistrantDocuments table with "new" Dociment Name      
	 ===========================================================*/

	if (!empty($generated_file_name)) {


		$orig_file_path =  '../hr/'.$generated_file_name.'.pdf';
	
		/* Delete "Old" file form disk
		 ================================ */
		
		unlink($orig_file_path);

		/* Update Student Session Notes table  
		 ====================================== */

		$query1 =	"UPDATE SchStudentsSessionNotes	
						SET StoredName = '{$new_file_name}',
						    UserId = '{$UserId}',
						    TransDate = NOW()			 
				WHERE ScheduleId = '{$ScheduleId}' ";
		
		$result1 = $connection->query ($query1);

		
		if (DB::isError($result1)){
	                die("Could not query the database:<br />$query ".DB::errorMessage($result1));
	    }


	} else {


		/* Add new Session Notes Image   table  
		 ============================================================== */

		$query2 = "INSERT into SchStudentsSessionNotes 
										 (
											 ScheduleId,
											 RegistrantId,
											 StudentId,
											 DocumentName,
											 StoredName,
											 UserId,
											 TransDate )
							values (
									'{$ScheduleId}',
									'{$RegistrantId}',
									'{$StudentId}',  
									'{$DocumentName}',
									'{$new_file_name}',
									'{$UserId}',
									NOW()  )  
							ON DUPLICATE KEY UPDATE DocumentName = '{$DocumentName}'" ;
	
	$result2 = $connection->query ($query2);
	if (DB::isError($result2)){
                die("Could not query the database:<br />$query1 ".DB::errorMessage($result2));
    }


	}
	

	
	
	//====================================================
	

	
	


	
	$connection->disconnect();
	
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";

	function generateRandomString($length = 15) {
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}	
	
?>