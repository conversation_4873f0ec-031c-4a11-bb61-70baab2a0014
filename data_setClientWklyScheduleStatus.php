<?php 


require "ewDataHandler.php";  
  
$rcr_transaction = new dataHandler(); 

$ScheduleId  =  $_POST['ScheduleId'];
$ScheduleStatusId = $_POST['ScheduleStatusId'];
$ScheduleOrigBy = $_POST['ScheduleOrigBy'];
$RivertToPending = $_POST['RivertToPending'];
$RegistrantConfFL = $_POST['RegistrantConfFL'];
$ClientConfFL = $_POST['ClientConfFL'];
$RegistrantId = $_POST['RegistrantId'];
$ClientId = $_POST['ClientId'];
$CancelReason = $_POST['CancelReason'];
$Msg = $_POST['Msg'];
$UserId = $_POST['UserId'];
$HighPriority = 0;

$RegistrantId1 = $RegistrantId; 

// Cancelled by Registrant
//=================================
if ($ScheduleStatusId == '2') {
	
	// Revert to Pending Status
	//==========================
	if ($RivertToPending) {
		$ScheduleStatusId = 0;
		$RegistrantId = 0;
	}  
	$ClientConfFL = 0;
	$RegistrantConfFL = 0; 
    $CancelledByWorker = 1;
}

// Cancelled by Client
//=================================
if ($ScheduleStatusId == '3') {
	$RegistrantId = 0;
	$ClientConfFL = 0;
	$RegistrantConfFL = 0;
	$CancelledByClient = 1;
}

// Cancelled by Coordinator
//=================================
if ($ScheduleStatusId == '4') {
	
	// Revert to Pending Status
	//==========================
	if ($RivertToPending) {
		$ScheduleStatusId = 0;
	}  

	
	$RegistrantId = 0;
	$ClientConfFL = 0;
	$RegistrantConfFL = 0;
}

// Change Client Schedule Status
//================================= 
$result = $rcr_transaction->setClientWklyScheduleStatus(
								$ScheduleId,  
								$ScheduleStatusId, 
								$RegistrantConfFL, 
								$ClientConfFL, 
								$RegistrantId, 
								$UserId	); 

// Add New Schedule Message
//================================= 
$result = $rcr_transaction->setClientWklyScheduleMsg(
								$ScheduleId, 
								$Msg,
								$HighPriority,	  
								$UserId); 

// Add New Cancelled by Registrant(Worker) Entry 
//================================= 
if ($CancelledByWorker == 1) {
	$result = $rcr_transaction->setCancelledByRegistrant(
								$RegistrantId1,
								$ScheduleId, 
								$CancelReason,
								$UserId); 
}

// Add New Cancelled by Client Entry 
//================================= 
if ($CancelledByClient == '1') {
	$result = $rcr_transaction->setCancelledByClient(
								$ClientId,
								$ScheduleId, 
								$CancelReason,
								$UserId); 
}								
								
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;

?>
