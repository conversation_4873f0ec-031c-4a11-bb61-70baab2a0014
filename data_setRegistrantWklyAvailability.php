<?php 


	require "ewDataHandler.php"; 
  
	$rcr_transaction = new dataHandler(); 

	$PayrollWeek = $_POST['PayrollWeek'];
	$RegistrantId = $_POST['RegId'];
	$RegistrantTypeId = $_POST['RegistrantTypeId'];
	$UserId = $_POST['UserId'];
	
	
	
	
	//=================================
	// Process Mon Schedule 
	//=================================
	$MonData = json_decode ( stripslashes ( $_POST['MonData'] ), true);

	$cnt = count($MonData);
	
	if ($cnt > 0) {
	//if (!empty($MonData)) {
	 
		
		$ServiceDate = $_POST['MonServiceDate'];
		$WeekDay = 'Mon';

		foreach ($MonData as &$value) {
					
			$ShiftId = $value;
					 
			  
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
					
			 	
		}
	}	

	//=================================
	// Process Tue Schedule 
	//=================================
	$TueData = json_decode ( stripslashes ( $_POST['TueData'] ), true);

	
	if (!empty($TueData)) {
	 
		
		$ServiceDate = $_POST['TueServiceDate'];
		$WeekDay = 'Tue';

		foreach ($TueData as &$value) {
					
			$ShiftId = $value;
					 
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
		}
	}		
	
	//=================================
	// Process Wed Schedule 
	//=================================
	$WedData = json_decode ( stripslashes ( $_POST['WedData'] ), true);

	
	if (!empty($WedData)) {
	 
		
		$ServiceDate = $_POST['WedServiceDate'];
		$WeekDay = 'Wed';

		foreach ($WedData as &$value) {
					
			$ShiftId = $value;
					 
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
			 	
		}
	}	
	
	//=================================
	// Process Thu Schedule 
	//=================================
	$ThuData = json_decode ( stripslashes ( $_POST['ThuData'] ), true);

	
	if (!empty($ThuData)) {
	 
		
		$ServiceDate = $_POST['ThuServiceDate'];
		$WeekDay = 'Thu';

		foreach ($ThuData as &$value) {
					
			$ShiftId = $value;
					 
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
			 	
					
			 	
		}
	}		

	//=================================
	// Process Fri Schedule 
	//=================================
	$FriData = json_decode ( stripslashes ( $_POST['FriData'] ), true);

	
	if (!empty($FriData)) {
	 
		
		$ServiceDate = $_POST['FriServiceDate'];
		$WeekDay = 'Fri';

		foreach ($FriData as &$value) {
					
			$ShiftId = $value;
					 
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
			 	

					
			 	
		}
	}	

	//=================================
	// Process Sat Schedule 
	//=================================
	$SatData = json_decode ( stripslashes ( $_POST['SatData'] ), true);

	
	if (!empty($SatData)) {
	 
		
		$ServiceDate = $_POST['SatServiceDate'];
		$WeekDay = 'Sat';

		foreach ($SatData as &$value) {
					
					$ShiftId = $value;
					 
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
			 	
			 	
		}
	}	

	//=================================
	// Process Sun Schedule 
	//=================================
	$SunData = json_decode ( stripslashes ( $_POST['SunData'] ), true);

	
	if (!empty($SunData)) {
	 
		
		$ServiceDate = $_POST['SunServiceDate'];
		$WeekDay = 'Sun';

		foreach ($SunData as &$value) {
					
			$ShiftId = $value;
					 
			  
			$result = $rcr_transaction->setRegistrantWklyAvailability(	$PayrollWeek,
																		$ServiceDate,
																		$WeekDay, 
																		$RegistrantId,
																		$ShiftId,
																		$UserId); 
			 	
			 	
		}
	}		
	//================================

	
											
	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
	//echo $result;


?>
