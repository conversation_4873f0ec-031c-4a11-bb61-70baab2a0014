
/*=========================================*/

DELIMITER $$

DROP TRIGGER IF EXISTS trig_RegistrantCredentialingItemsConditionalAfterDelete  $$

CREATE TRIGGER trig_RegistrantCredentialingItemsConditionalAfterDelete AFTER DELETE ON RegistrantCredentialingItemsConditional

FOR EACH ROW BEGIN

	
	 
	DELETE FROM RegistrantCredItems
  	  WHERE  RegistrantCredItems.RegistrantId  = OLD.RegistrantId
      AND    CredItemId in (SELECT a.CredItemId 
					FROM	CredentialingItemsConditionalDetails a 	
					WHERE a.ConditionalItemId = OLD.ConditionalItemId )	;
 
	
	
END $$

DELIMITER ;