  
<?php
    
	/** Include path **/
	require_once('DB.php');
	include('db_login.php');
	

	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//Portal Out Input File  
	//==========================================================
	$out_File = "../uploads/doe_billing_upload.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");
 	

	//Portal Input Input File   
	//==========================================================

	$inputFileName = '../uploads/doe_billing_upload.csv';


	/* Upload New File 
	 =============================================*/
 
 		
   if($ufile != none){ 
      
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	} else {
		
		$err_flag = '1';
		//print "Error uploading extracted file. Please try again!!! "; 
	  
		$linecount = 0;
		echo  "{ success: true, transactions: '{$linecount}'}";

	    Return ; 

	}
 
   
 

	$cnt = 0;	
	$linecount = 0;	


	$file_handle = fopen("../uploads/doe_billing_upload.csv", "r");
	
	ini_set("auto_detect_line_endings", true);

	$cnt = 0;	
	$linecount = 0;	

   while (($row_xls = fgetcsv($file_handle)) !== FALSE) {
        
		 	

			// Check if Wrog file was selected 
			// ====================================


				$cnt++;
		
		
				//=========================
				// Write Header
				//========================	
				
				if ($cnt == 1) {


				// Check if Wrog file was selected 
			 	//====================================

			 			if ($row_xls[0] != 'SRAP FISCAL YR') {

			 				$err_flag = '1';


			 			} else {

			 				// Write Heading 
                            // ==================
			 				$write_flag = '1';


			 			}




				}


				if ($cnt != 1) { // cnt !=1 - srart  


					//==============================
					// Get Therapist Id
					//==============================	

					$therapist_id = $row_xls[10];

					//==============================
					// Get Student Id
					//==============================
					
					$student_id = $row_xls[11];

					//==============================
					// Get Service Code
					//==============================

					$doe_service_code = $row_xls[14];


					//==============================
					// Get Service Date
					//==============================
					
					$service_date = date("Y-m-d", strtotime($row_xls[24]));


			 	 	
				    // echo ' Therapist Id: '.$therapist_id.' Student ID: '.$student_id.' Service Code: '.$doe_service_code.' Service Date: '.$service_date.'</br>'; 			 	
			      
	 			    
			
		if (strpos($doe_service_code, '1') !== false) { //Individual

			$query1 = "SELECT 	DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
									DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
									a.Id as ServiceId,
									a.SessionGrpSize as GroupSize,
									a.SessionDeliveryModeId 
							FROM 	WeeklyServices a,
									Registrants b,
									SchStudents c,
									SchServiceTypes d,
									SchStudentMandates e 							
							WHERE ServiceDate = '{$service_date}'
							AND a.ScheduleStatusId in ('7','8')
							AND a.RegistrantId = b.Id
							AND a.StudentId = c.Id  
							AND a.ServiceTypeId = d.Id 
							AND b.ExtId like '%{$therapist_id}%'
							AND a.MandateId = e.Id
							AND e.SessionGrpSize = 1
							AND c.ExtId = '{$student_id}'";


		} else { // Group


			$query1 = " SELECT 		DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
									DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
									a.Id as ServiceId,
									a.SessionGrpSize as GroupSize,
									a.SessionDeliveryModeId

							FROM 	WeeklyServices a,
									Registrants b,
									SchStudents c,
									SchServiceTypes d,
									SchStudentMandates e  							
							WHERE ServiceDate = '{$service_date}'
							AND a.ScheduleStatusId in ('7','8')
							AND a.RegistrantId = b.Id
							AND a.StudentId = c.Id  
							AND a.ServiceTypeId = d.Id 
							AND b.ExtId like '%{$therapist_id}%'
							AND a.MandateId = e.Id
							AND e.SessionGrpSize > 1
							AND c.ExtId = '{$student_id}'";

		}


					$result1 = $connection->query ($query1);
					
					// echo '$query1: '.$query1.'</br>'; 


					if ($result1->numRows() == 1) { // numRows() == 1 - Start
				 
				 
				
						if (DB::isError($result1)){
							//die("Could not query the database:<br />$query ".DB::errorMessage($result1));
							die($db->getMessage());
						}

						
						while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {
								$start_time = $row['StartTime'];
								$end_time = $row['EndTime'];
								$service_id = $row['ServiceId'];
								$group_size = $row['GroupSize'];
								$delivery_method_id = $row['SessionDeliveryModeId'];
						}  
						
					
						if (!$group_size) {
							$group_size = 1;
						}
						

						if ($delivery_method_id == 'I') {
						
							$place_of_service = 'S';
						
						} else {

							$place_of_service = 'R';

						}


					
						$out_Line_str = implode("\t", $row_xls); 
						
									
						
						$row_xls[25] = 'P';
						$row_xls[26] =  $group_size;
						$row_xls[27] =  $start_time;
						$row_xls[28] = $end_time;
//						$row_xls[29] = 'B';
						$row_xls[29] = $place_of_service;
							
						$linecount++;
						$i++;
						$write_flag = '1';
						
						 
				}	// numRows() == 1 - End




			} // Cnt !=1  - end

			// Wrong file selected error
			// =====================================
			if ($err_flag == '1') {

				//print "Wrong file was selected. Please try again!!! "; 
			  
				$linecount = 0;
				echo  "{ success: true, transactions: '{$linecount}'}";
			    Return ; 

			}


			$out_Line = $row_xls;
			$out_Line_str = implode("\t", $out_Line); 
		
			if ($write_flag == '1') {
		
				$out_Line_str = $out_Line_str."\n";
				//print $out_Line_str;

				fwrite($fh, $out_Line_str);


				$write_flag = '0';
				$start_time = '';
				$end_time = '';

			}	
	 
		//} // for foreach - end	
	   	
	}	 


	$connection->disconnect();
	
   
	
	fclose($file_handle);
			  

	//$msg = "";
	//$msg = $msg."Total Transactions To Be Uploaded: ".transactions: '{$linecount}';

	echo  "{ success: true, transactions: '{$linecount}'}";
		//Return ;  

?>