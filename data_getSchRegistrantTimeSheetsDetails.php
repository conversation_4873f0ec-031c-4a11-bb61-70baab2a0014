  
<?php
    
  require_once("db_GetSetData.php");

  $conn = getCon();

  $RegistrantId = $_GET['RegistrantId'];
  //$Month = $_GET['Month'];
  //$Year = $_GET['Year'];
  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];
  $SessionTypeId = $_GET['SessionTypeId'];


    // All Sesions
    //======================= 
    if ($SessionTypeId == 0) {  
        $query = " SELECT DISTINCT b.ExtId,
                          b.FirstName,
                          b.LastName, 
                          DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) AS DOB,
                          a.ServiceDate as ServiceDateSort,
                          DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
                          a.StartTime as StartTimeSort,
                          DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                          DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                          a.SessionGrpSize,
                          ROUND((TotalHours * 60)) as Duration,
                          SessionDeliveryModeId
                       
        FROM WeeklyServices a, 
             SchStudents b,
             SchStudentMandates c 
      WHERE a.RegistrantId = '{$RegistrantId}' 
        AND   a.ScheduleStatusId > 6    
        /*
        AND month(a.ServiceDate) = '{$Month}'
        AND year(a.ServiceDate) = '{$Year}' */
        AND a.ServiceDate between '{$FromDate}' and '{$ToDate}'

        AND b.Id = a.StudentId
        AND a.MandateId = c.Id
      Order BY ServiceDateSort,   StartTimeSort      ";


    }

    // In-Persion Sesions
    //======================= 
    if ($SessionTypeId == 1) {  
        $query = " SELECT DISTINCT
                          b.ExtId,
                          b.FirstName,
                          b.LastName, 
                          DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) AS DOB,
                          a.ServiceDate as ServiceDateSort,
                          DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
                          a.StartTime as StartTimeSort,
                          DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                          DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                          a.SessionGrpSize,
                          ROUND((TotalHours * 60)) as Duration,
                          SessionDeliveryModeId
                       
        FROM WeeklyServices a, 
             SchStudents b,
             SchStudentMandates c 
      WHERE a.RegistrantId = '{$RegistrantId}' 
        AND   a.ScheduleStatusId > 6    
        /*
        AND month(a.ServiceDate) = '{$Month}'
        AND year(a.ServiceDate) = '{$Year}' */
        AND a.ServiceDate between '{$FromDate}' and '{$ToDate}'
        AND a.SessionDeliveryModeId = 'I'
        AND b.Id = a.StudentId
        AND a.MandateId = c.Id
      Order BY ServiceDateSort,   StartTimeSort      ";


    }

    // Tele-Therapy Sesions
    //======================= 
    if ($SessionTypeId == 2) {  
        $query = " SELECT DISTINCT
                          b.ExtId,
                          b.FirstName,
                          b.LastName, 
                          DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) AS DOB,
                          a.ServiceDate as ServiceDateSort,
                          DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
                          a.StartTime as StartTimeSort,
                          DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                          DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                          a.SessionGrpSize,
                          ROUND((TotalHours * 60)) as Duration,
                          SessionDeliveryModeId
                       
        FROM WeeklyServices a, 
             SchStudents b,
             SchStudentMandates c 
      WHERE a.RegistrantId = '{$RegistrantId}' 
        AND   a.ScheduleStatusId > 6    
        /*
        AND month(a.ServiceDate) = '{$Month}'
        AND year(a.ServiceDate) = '{$Year}' */
        AND a.ServiceDate between '{$FromDate}' and '{$ToDate}'
        AND a.SessionDeliveryModeId != 'I'
        AND b.Id = a.StudentId
        AND a.MandateId = c.Id
      Order BY ServiceDateSort,   StartTimeSort      ";


    }


    
  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;

?>
 
