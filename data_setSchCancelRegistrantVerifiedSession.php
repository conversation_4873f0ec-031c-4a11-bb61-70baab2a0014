<?php 


	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler();  

	$SessionSchedulesList  =  $_POST['SessionSchedulesList'];
	$ScheduleStatusId = $_POST['ScheduleStatusId'];
	$Msg = $_POST['Msg'];
	$UserId = $_POST['UserId'];
	$HighPriority = 0;




	$schedArray = explode(',', $SessionSchedulesList);
 
	/* Cancel Session Related Schedules  
	 ====================================*/
	
	foreach($schedArray as $ScheduleId) {
	

		// Cancelled by Registrant
		//==========================

		$result1 = $rcr_transaction->setChangeScheduleStatus(
								$ScheduleId, 
								$ScheduleStatusId,
								$UserId); 




		// Add New Schedule Message
		//================================= 
		$result = $rcr_transaction->setClientWklyScheduleMsg(
										$ScheduleId, 
										$Msg,
										$HighPriority,	  
										$UserId); 




	}


						
								
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result1;

?>
