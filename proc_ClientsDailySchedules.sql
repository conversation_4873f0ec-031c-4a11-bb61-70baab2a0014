

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_ClientsDailySchedules$$

CREATE PROCEDURE proc_ClientsDailySchedules (IN p_service_date date)  

BEGIN

 	
	/*============================================*/
	
	create temporary table tmp engine=memory

 	SELECT  a.Id AS ScheduleId, 
			ScheduleStatusId, 
			ScheduleStatusDesc,
			TextColor,
			BackgroundColor,
			a.ClientId, 
			ClientName,
			ServiceDate,
			StartTime as StartTimeNum,
			EndTime as EndTimeNum,
			DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
			DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
			PayrollWeek,  
			TotalHours, 
			WeekDay, 
			RegistrantId, 
			PatientId,
			
			'                               ' as RegistrantName,
			'                               ' as RegistrantTypeDesc, 
			'                               ' as PatientName, 
			'                               ' as LastMessage, 
			RegistrantTypeId,
			ClientUnitId,
			UnitName,
			ClientConfFL,
			RegistrantConfFL,
			ScheduleOrigBy,
            000.0 as HoursScheduled,  			
			CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
			DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) AS TransDate
		FROM 	WeeklyServices a, 
				Users e,
				Clients b,
				ClientUnits c,
                ScheduleStatuses g								
			Where 	ServiceDate = p_service_date
					AND a.UserId = e.UserId
					AND a.ClientUnitId = c.Id
					AND a.ClientId = b.Id
					AND ScheduleStatusId = g.Id ; 	

	/* Set Registrant Name */
	/*================================*/
	Update  tmp a,  Registrants b, RegistrantTypes f
		Set a.RegistrantName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' )	
         Where a.Registrantid IS NOT NULL 
          AND a.RegistrantId = b.Id
	AND b.TypeId = f.Id ;

	/* Set Requested Registrant Type*/
	/*================================*/
	Update  tmp a,  RegistrantTypes f
		Set  a.RegistrantTypeDesc = f.RegistrantTypeDesc
         Where  a.RegistrantTypeId = f.Id
	  ;

	
	/* Set Patient Name */
	/*================================*/
	/*
	Update  tmp a,  Patients b
		Set a.PatientName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)  )	
         Where a.PatientId = b.Id ;
	*/ 
	  
	  
	/* Set Last Message*/
	/*================================*/
	Update  tmp a
	  Set LastMessage =  ( SELECT Msg
			FROM WeeklyServicesMessages b
			WHERE b.Id = ( SELECT max( c.Id )
				FROM WeeklyServicesMessages c
				WHERE c.ScheduleId = a.ScheduleId )) ;
	
	
							 

	
	
	SELECT 	
			ScheduleId, 
			ScheduleStatusId, 
			ScheduleStatusDesc,
			TextColor,
			BackgroundColor,
			ClientId, 
			ClientName,
			ServiceDate,
			StartTimeNum,
			EndTimeNum,
			StartTime,
			EndTime,
			PayrollWeek,  
			TotalHours, 
			WeekDay, 
			RegistrantId, 
			RegistrantName,
			RegistrantTypeDesc, 
			PatientName, 
			COALESCE(LastMessage, '') as LastMessage, 
			RegistrantTypeId,
			ClientUnitId,
			UnitName,
			ClientConfFL,
			RegistrantConfFL,
			ScheduleOrigBy,
            HoursScheduled,  			
			UserName ,  
			TransDate	
	FROM tmp
	ORDER BY ServiceDate, StartTimeNum, EndTimeNum, ScheduleId;
	
	drop temporary table if exists tmp;
	 
	
END $$

DELIMITER ;	