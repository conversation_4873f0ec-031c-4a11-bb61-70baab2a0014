

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_setRegistrantRequiredCredItems$$

CREATE PROCEDURE proc_setRegistrantRequiredCredItems (	p_registrant_id INT,
														p_user_id INT
														) 		 

BEGIN
	
	/* Process Non-Conditional Cred. Items
     =====================================*/

	create temporary table tmp engine=memory

	SELECT a.Id FROM CredentialingItems a
	WHERE EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b,
							ClientApprovedRegistrants c
				WHERE a.Id = b.MandatoryCredItemId	
                AND   b.ClientId = c.ClientId
                AND   b.ServiceTypeId = c.ServiceTypeId
                AND   c.RegistrantId = p_registrant_id
				AND   c.Status != '2'
				)
	AND ConitionalItemFL = '0'	;	
	
	INSERT INTO tmp

	SELECT a.Id FROM CredentialingItems a
	WHERE EXISTS (SELECT 1 FROM   	CredentialingItemsMandatory b,
									Registrants c
	                WHERE a.Id = b.MandatoryCredItemId 
	                AND   b.RegistrantTypeId = c.TypeId
	                AND   c.Id = p_registrant_id
					)
	AND ConitionalItemFL = '0' ;

	/* Delete Non-Conditional Cred. Items NO LONGER NEEDED
     =====================================================*/

	create temporary table tmp1 engine=memory

	SELECT DISTINCT * FROM tmp; 	
     
	/*==============================*/	

	  
    UPDATE RegistrantCredItems a, CredentialingItems c
    	   SET a.DeactivatedFL = '1'	

    WHERE a.RegistrantId = p_registrant_id 
    AND   a.CredItemId = c.Id 
    AND   c.ConitionalItemFL = '0'

    AND NOT EXISTS (SELECT 1 FROM tmp1 b 
    					WHERE b.Id = a.CredItemId );	
         

	 
	DELETE FROM tmp1  
	WHERE  EXISTS (SELECT 1 FROM RegistrantCredItems b,
									CredentialingItems c 
						WHERE tmp1.Id = b.CredItemId
						AND   b.RegistrantId = p_registrant_id
						AND   b.CredItemId = c.Id 
						AND  ConitionalItemFL = '0'				
						) ;
	 
	 
	/*==========================*/
	/* Insert New Cred. Items  te*/
	/* (Non-Conditional Items ONLY) */
	/*==========================*/

		INSERT INTO RegistrantCredItems
				(
				RegistrantId,
				StatusId, 
				CredItemId,
				UserId,
				TransDate
				)
			
			SELECT DISTINCT p_registrant_id,
							'2',
							Id,
							p_user_id,
							NOW()  
			FROM tmp1; 
			
 
    TRUNCATE TABLE tmp;
	TRUNCATE TABLE tmp1;

	/* Process Non-Conditional Cred. Items
     =====================================*/
		
 
     	INSERT INTO tmp
		SELECT a.Id FROM CredentialingItemsConditionalHeader a
		WHERE EXISTS (SELECT 1 FROM CredentialingItemsClientMandatory b,
									ClientApprovedRegistrants c
						WHERE a.Id = b.MandatoryCredItemId	
		                AND   b.ClientId = c.ClientId
		                AND   b.ServiceTypeId = c.ServiceTypeId
				        AND   b.ClientId = c.ClientId
				        AND   c.Status != '2'
		                AND   c.RegistrantId = p_registrant_id );
			 

     	INSERT INTO tmp
		SELECT a.Id FROM CredentialingItemsConditionalHeader a
		WHERE EXISTS (SELECT 1 FROM   	CredentialingItemsMandatory b,
										Registrants c
		                WHERE a.Id = b.MandatoryCredItemId 
		                AND   b.RegistrantTypeId = c.TypeId
		                AND   c.Id = p_registrant_id );
				  
		/*==============================*/	
		
		INSERT INTO tmp1				 
		SELECT DISTINCT * FROM tmp; 	
     
		/*==============================*/	
		 
		
		/* Delete Conditional Cred. Items NO LONGER NEEDED
	     =====================================================*/
	    
	    DELETE FROM RegistrantCredentialingItemsConditional  
	    WHERE RegistrantId = p_registrant_id 
	    AND NOT EXISTS (SELECT 1 FROM tmp1 a 
	    	WHERE a.Id = RegistrantCredentialingItemsConditional.ConditionalItemId  ); 
    	   


		DELETE FROM tmp1  
		WHERE  EXISTS (SELECT 1 FROM RegistrantCredentialingItemsConditional b
									
						WHERE tmp1.Id = b.ConditionalItemId
						AND   b.RegistrantId = p_registrant_id ); 
		 						
	 	 

		/*================================================================================== */
		/* Insert into "RegistrantCredentialingItemsConditional" DEFAULT Conditonal Items      */
		/*================================================================================== */
		
		 
		INSERT INTO RegistrantCredentialingItemsConditional
		
		SELECT p_registrant_id,
			   a.Id,
			   a.ConditionalSwitchDefault	
		FROM CredentialingItemsConditionalHeader a, tmp1 b
			WHERE a.Id = b.Id ;
		 

		/*============================================================================= */
		/* Insert Cred. Items (Conditonal )   */
		/*============================================================================= */

		   
		INSERT INTO RegistrantCredItems
			(
			RegistrantId,
			CredItemId,
			UserId,
			TransDate
			)
		SELECT 	 
				p_registrant_id,
				c.CredItemId,
				p_user_id,
				NOW()
		FROM  	tmp1 a,
				RegistrantCredentialingItemsConditional b, 
			  	CredentialingItemsConditionalDetails c
			WHERE b.RegistrantId = p_registrant_id
			AND b.ConditionalItemId = a.Id
			AND b.ConditionalItemId = c.ConditionalItemId
			AND b.ConditionalSwitch = c.ConditionalSwitch ;
		 

	/*============================================================================= */
	/* Delete all of the DEACTIVATED Registrant's Cred. Items   */
	/*============================================================================= */

	DELETE FROM RegistrantCredItems
	WHERE RegistrantId = p_registrant_id
	AND DeactivatedFL = '1';


	drop temporary table if exists tmp;
	drop temporary table if exists tmp1;


	END $$

DELIMITER ;	