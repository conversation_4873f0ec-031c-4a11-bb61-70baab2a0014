<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetXY</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetXY</h1>
<code>SetXY(<b>float</b> x, <b>float</b> y)</code>
<h2>Description</h2>
Defines the abscissa and ordinate of the current position. If the passed values are negative,
they are relative respectively to the right and bottom of the page.
<h2>Parameters</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
The value of the abscissa.
</dd>
<dt><code>y</code></dt>
<dd>
The value of the ordinate.
</dd>
</dl>
<h2>See also</h2>
<a href="setx.htm">SetX()</a>,
<a href="sety.htm">SetY()</a>.
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
